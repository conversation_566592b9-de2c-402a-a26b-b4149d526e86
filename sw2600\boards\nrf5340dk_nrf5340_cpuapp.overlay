/*
 * Copyright (c) 2023 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: Apache-2.0
 */

 / {
	chosen {
		nordic,pm-ext-flash = &mx25r64;
		ncs,zigbee-timer = &timer2;
        zephyr,console = &uart0;
        zephyr,shell-uart = &uart0;
    };

    aliases {
        led0 = &led0;
        led1 = &led1;
        sw0 = &button0;
        bootloader-led0 = &led0;
        mcuboot-button0 = &button0;
        mcuboot-led0 = &led0;
        mic-power = &mic_power;
	    adxl382-pwr-ctrl = &adxl382_pwr_ctrl;
        //adxl382-fsync = &adxl382;
        //adxl382-int0 = &adxl382;
        //adxl382-int1 = &adxl382;
        watchdog0 = &wdt0;
    };

    leds {
        compatible = "gpio-leds";
        led0: led_0 {
            gpios = <&gpio0 5 GPIO_ACTIVE_LOW>;
        };
        led1: led_1 {
            gpios = <&gpio0 6 GPIO_ACTIVE_LOW>;
        };
    };

    buttons {
        compatible = "gpio-keys";
        button0: button_0 {
            gpios = <&gpio0 7 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
            label = "Push button 1";
        };
    };
    
    mic_power: mic_power {
        compatible = "regulator-fixed";
        label = "MIC_VDD";
        regulator-name = "mic-vdd";
        enable-gpios = <&gpio1 4 GPIO_ACTIVE_HIGH>;
        startup-delay-us = <100000>;
        regulator-min-microvolt = <1800000>;
        regulator-max-microvolt = <1800000>;
        status = "okay";
    };

    adxl382_pwr_ctrl: adxl382-pwr-ctrl {
        compatible = "regulator-fixed";
        regulator-name = "tck106ag";
        enable-gpios = <&gpio0 26 GPIO_ACTIVE_HIGH>;
        regulator-boot-on;
        regulator-always-on;
        startup-delay-us = <10000>;
        regulator-min-microvolt = <3300000>;
        regulator-max-microvolt = <3300000>;
        status = "okay";
    };

    gpio_fwd: nrf-gpio-forwarder {
		compatible = "nordic,nrf-gpio-forwarder";
		status = "okay";
		nrf21540-spi-if {
			gpios = <&gpio1 3 0>,   /* cs-gpios */
                   <&gpio0 29 0>,   /* SPIM_SCK */
                   <&gpio0 20 0>,   /* SPIM_MISO */
                   <&gpio0 28 0>;   /* SPIM_MOSI */
		};

		nrf21540-gpio-if {
			gpios = <&gpio0 31 0>,   /* tx-en-gpios */
                   <&gpio0 30 0>,   /* rx-en-gpios */
                   <&gpio1 10 0>,   /* pdn-gpios */
                   <&gpio1 11 0>;   /* mode-gpios */
		};
	};
};

&pinctrl {
    uart0_default: uart0_default {
        group1 {
            psels = <NRF_PSEL(UART_TX, 1, 12)>;
        };

        group2 {
            psels = <NRF_PSEL(UART_RX, 1, 14)>;
        };
    };

    uart0_sleep: uart0_sleep {
        group1 {
            psels = <NRF_PSEL(UART_TX, 1, 12)>,
                    <NRF_PSEL(UART_RX, 1, 14)>;
            low-power-enable;
        };
    };

    i2c1_default: i2c1_default {
        group1 {
            psels = <NRF_PSEL(TWIM_SCL, 1, 8)>,
                    <NRF_PSEL(TWIM_SDA, 0, 24)>;
        };
    };

    i2c1_sleep: i2c1_sleep {
        group1 {
            psels = <NRF_PSEL(TWIM_SCL, 1, 8)>,
                    <NRF_PSEL(TWIM_SDA, 0, 24)>;
            low-power-enable;
        };
    };
    
    i2s0_default: i2s0_default {
        group1 {
            psels = <NRF_PSEL(I2S_SCK_M, 1, 6)>, 
                    <NRF_PSEL(I2S_LRCK_M, 0, 19)>,
                    <NRF_PSEL(I2S_SDIN, 0, 21)>;
            bias-pull-down;
            nordic,drive-mode = <NRF_DRIVE_H0H1>;
        };
    };

    i2s0_sleep: i2s0_sleep {
        group1 {
            psels = <NRF_PSEL(I2S_SCK_M, 1, 6)>,
                    <NRF_PSEL(I2S_LRCK_M, 0, 19)>,
                    <NRF_PSEL(I2S_SDIN, 0, 21)>;
            low-power-enable;
        };
    };
    
    spi2_default: spi2_default {
        group1 {
            psels = <NRF_PSEL(SPIM_SCK, 0, 23)>,
                    <NRF_PSEL(SPIM_MOSI, 1, 5)>,
                    <NRF_PSEL(SPIM_MISO, 1, 7)>;
        };
    };

    spi2_sleep: spi2_sleep {
        group1 {
            psels = <NRF_PSEL(SPIM_SCK, 0, 23)>,
                    <NRF_PSEL(SPIM_MOSI, 1, 5)>,
                    <NRF_PSEL(SPIM_MISO, 1, 7)>;
            low-power-enable;
        };
    };
};
 

&uart3 {
    status = "disabled";
};

&i2c1 {
    status = "okay";
    pinctrl-0 = <&i2c1_default>;
    pinctrl-1 = <&i2c1_sleep>;
    pinctrl-names = "default", "sleep";
    clock-frequency = <I2C_BITRATE_STANDARD>;
    zephyr,pm-device-runtime-auto;

    m117: m117@48 {
        compatible = "i2c-device";
        reg = <0x48>;
        label = "M117";
    };
};

&i2s0 {
    compatible = "nordic,nrf-i2s";
    status = "okay";
    pinctrl-0 = <&i2s0_default>;
    pinctrl-1 = <&i2s0_sleep>;
    pinctrl-names = "default", "sleep";
    clock-source = "PCLK32M_HFXO";
};

&clock {
	status = "okay";
};

&spi2 {
    status = "disabled";
};

&vregmain {
	regulator-initial-mode = <NRF5X_REG_MODE_DCDC>;
};

&vregradio {
	regulator-initial-mode = <NRF5X_REG_MODE_DCDC>;
};

&gpio0 {
    status = "okay";
};

&gpio1 {
    status = "okay";
};

&uart0 {
    status = "okay";
    current-speed = <115200>;
};

&uart1 {
    status = "disabled";
};

&qspi {
    status = "disabled";
};

&spi4 {
    status = "disabled";
};

&button1 {
    status = "disabled"; 
};

&button2 {
    status = "disabled"; 
};

&button3 {
    status = "disabled"; 
};

&led2 {
    status = "disabled"; 
};

&led3 {
    status = "disabled"; 
};

&timer2 {
	status = "disabled";
};

&timer0 {
	status = "disabled";
};

&timer1 {
	status = "disabled";
};

&wdt0 {
    status = "disabled";
};

&pwm0 {
	status = "disabled";
};

&adc {
	status = "disabled";
};

&usbd {
	status = "disabled";
};

&qspi {
	status = "disabled";
};
