/*
 * SPDX-License-Identifier: Apache-2.0
 */

/* file is auto-generated, do not modify ! */

#include <zephyr/toolchain.h>

GEN_ABS_SYM_BEGIN (_ConfigAbsSyms)

GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SERIAL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FLASH_LOAD_SIZE, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SRAM_SIZE, 448);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FLASH_LOAD_OFFSET, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUM_IRQS, 69);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC, 32768);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FLASH_SIZE, 1024);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FLASH_BASE_ADDRESS, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MP_MAX_NUM_CPUS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_RESET_HOOK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYS_CLOCK_TICKS_PER_SEC, 32768);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ROM_START_OFFSET, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PINCTRL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BUILD_OUTPUT_BIN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_XIP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MAIN_STACK_SIZE, 1024);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_IDLE_STACK_SIZE, 320);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_FLASH_LOAD_OFFSET, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_HAS_ARM_MPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_DEVICE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TICKLESS_KERNEL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE, 1024);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_INIT_PRIORITY, 30);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GEN_IRQ_VECTOR_TABLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GEN_ISR_TABLES, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_DEVICE_SYSTEM_MANAGED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HW_STACK_PROTECTION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GPIO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_ENTRY, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DCACHE_LINE_SIZE, 32);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_SW_ISR_TABLE_ALIGN, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_RTC_TIMER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_DOMAIN_NAME, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BUILD_OUTPUT_HEX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_DEVICE_POWER_DOMAIN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_HAS_TIMING_FUNCTIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SERIAL_INIT_PRIORITY, 50);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_SERIES, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_FAMILY, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GEN_SW_ISR_TABLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GEN_IRQ_START_VECTOR, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SRAM_OFFSET, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CONSOLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ISR_STACK_SIZE, 2048);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ICACHE_LINE_SIZE, 32);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PRIVILEGED_STACK_SIZE, 1024);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARDUINO_UNO_ADC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARM_ARMV8M_ITM_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARM_CORTEX_M33F_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARM_CRYPTOCELL_312_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_GPIO_KEYS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_GPIO_LEDS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_MMIO_SRAM_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_DCNF_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_FICR_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_KMU_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_MUTEX_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_OSCILLATORS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_RESET_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_SPU_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_UICR_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_USBREG_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF53X_REGULATOR_HV_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF53X_REGULATORS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_NRF5X_REGULATOR_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_NORDIC_QSPI_NOR_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_PWM_LEDS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_REGULATOR_FIXED_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ZEPHYR_BT_HCI_IPC_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_ZIGBEE_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NEWLIB_LIBC_NANO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUM_METAIRQ_PRIORITIES, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BUFFER_SIZE, 1024);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_WORK_STACK_SIZE, 1024);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WARN_EXPERIMENTAL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BT_BUF_CMD_TX_COUNT, 10);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_INIT_ARCH_HW_AT_BOOT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE, 4096);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_PARTITION_SIZE_PROVISION, 0x280);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_PARTITION_SIZE_B0_IMAGE, 0x8000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SB_VALIDATION_INFO_MAGIC, 0x86518483);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SB_VALIDATION_POINTER_MAGIC, 0x6919b47e);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SB_VALIDATION_INFO_CRYPTO_ID, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SB_VALIDATION_INFO_VERSION, 2);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SB_VALIDATION_METADATA_OFFSET, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SB_VALIDATE_FW_SIGNATURE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYSTEM_WORKQUEUE_PRIORITY, -1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HEAP_MEM_POOL_SIZE, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PCD_VERSION_PAGE_BUF_SIZE, 2046);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_SEC_TAG, 16842753);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_AWS_CA_CERT_SIZE_THRESHOLD, 1150);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_COAP_CA_CERT_SIZE_THRESHOLD, 550);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_CLIENT_ID_SRC_COMPILE_TIME, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_CLIENT_ID, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FOTA_USE_NRF_CLOUD_SETTINGS_AREA, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_WIFI_LOCATION_ENCODE_OPT_MAC_RSSI, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_LOG_OUTPUT_LEVEL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_LOG_BUF_SIZE, 256);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_LOG_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_PRINT_DETAILS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_VERBOSE_DETAILS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CLOUD_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_FEM_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_FEM_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_THREAD_COOP_PRIO, 8);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_TIMESLOT_SESSION_COUNT, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_LOW_PRIO_IRQN, 26);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_HFCLK_LATENCY, 1400);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSL_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SRAM_BASE_ADDRESS, 0x20000000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_TRUSTZONE_FLASH_REGION_SIZE, 0x4000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_TRUSTZONE_RAM_REGION_SIZE, 0x2000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_EXTERNAL_FLASH_BASE, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_EXTERNAL_FLASH_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_EXTERNAL_FLASH_PATH, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS, 67108864);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_SRAM_BASE, 0x20000000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_SRAM_SIZE, 0x80000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MGMT_FMFU_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MGMT_FMFU_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_AUDIO_MODULE_NAME_SIZE, 20);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_AUDIO_MODULE_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_AUDIO_MODULE_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MCUBOOT_APPLICATION_IMAGE_NUMBER, -1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MCUBOOT_NETWORK_CORE_IMAGE_NUMBER, -1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MCUBOOT_WIFI_PATCHES_IMAGE_NUMBER, -1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MCUBOOT_QSPI_XIP_IMAGE_NUMBER, -1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MCUBOOT_MCUBOOT_IMAGE_NUMBER, -1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WFA_QT_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WFA_QT_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WFA_QT_THREAD_STACK_SIZE, 5200);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WFA_QT_REBOOT_TIMEOUT_MS, 1000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WFA_QT_DEFAULT_INTERFACE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WPAS_READY_TIMEOUT_MS, 10000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_SPU_FLASH_REGION_SIZE, 0x4000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FPROTECT_BLOCK_SIZE, 0x4000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HW_UNIQUE_KEY_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NCS_BOOT_BANNER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NCS_NCS_BOOT_BANNER_STRING, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NCS_ZEPHYR_BOOT_BANNER_STRING, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HW_CC3XX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZTEST_MULTICORE_DEFAULT_SETTINGS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_NRF_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOOT_SIGNATURE_KEY_FILE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DT_FLASH_WRITE_BLOCK_SIZE, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_MCUBOOT_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_MBEDTLS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_OBERON_PSA_CRYPTO_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TFM_BOARD, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_PSA_ARCH_TESTS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_SOC_HWMV1_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CJSON_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_OPENTHREAD_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SUIT_ENVELOPE_TEMPLATE_FILENAME, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SUIT_ENVELOPE_TARGET, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SUIT_ENVELOPE_OUTPUT_ARTIFACT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SUIT_ENVELOPE_OUTPUT_MPI_MERGE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_SUIT_GENERATOR_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SUIT_PLATFORM_DRY_RUN_SUPPORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_SUIT_PROCESSOR_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_COREMARK_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CANOPENNODE_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CHRE_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_LZ4_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_NANOPB_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_TF_M_TESTS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_ZSCILIB_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CMSIS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_CMSIS_CORE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_CMSIS_CORE_M, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CMSIS_DSP_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CMSIS_NN_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_FATFS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_HAL_NORDIC_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_NORDIC_DRIVERS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_NRFX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_CLOCK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_DPPI, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_DPPI0, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_GPIOTE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_GPIOTE0, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_GPPI, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_IPC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFX_RESERVED_RESOURCES_HEADER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_HAL_ST_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_HAL_WURTHELEKTRONIK_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_HOSTAP_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_LIBMETAL_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_LIBLC3_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_LITTLEFS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_LORAMAC_NODE_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_LVGL_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_MIPI_SYS_T_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_NRF_WIFI_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_OPEN_AMP_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_PICOLIBC_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_SEGGER_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_SEGGER_RTT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_TINYCRYPT_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_ZCBOR_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE, 0x4e8);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRFXLIB_CRYPTO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_CC3XX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_CC3XX_PLATFORM, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CC3XX_MUTEX_LOCK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_802154_SOURCE_NRFXLIB, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_NRFXLIB_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_NRF_HW_MODELS_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOARD, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOARD_REVISION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOARD_TARGET, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOARD_NRF5340DK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOARD_QUALIFIERS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_DOMAIN_CPUNET_BOARD, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_FAMILY_NORDIC_NRF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_SERIES_NRF53X, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF5340_CPUAPP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF5340_CPUAPP_QKAA, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_CC312, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_CLOCK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_CTRLAP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_DCNF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_DPPIC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_EGU0, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_EGU1, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_EGU2, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_EGU3, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_EGU4, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_EGU5, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_GPIO0, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_GPIO1, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_GPIOTE0, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_KMU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_MUTEX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_NVMC_PE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_OSCILLATORS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_POWER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_RESET, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_SPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_UARTE0, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_USBREG, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_HW_NRF_VMC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_ANOMALY_168_WORKAROUND, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_RTC_PRETICK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET, 10);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET, 11);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_SPU_RAM_REGION_SIZE, 0x2000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF_GPIO_FORWARDER_FOR_NRF5340, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_NRF53_CPUNET_MGMT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_ENABLE_LFXO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_LFXO_CAP_INT_7PF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_HFXO_CAP_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_ENABLE_CACHE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF53_SYNC_RTC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYNC_RTC_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYNC_RTC_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF53_SYNC_RTC_INIT_PRIORITY, 90);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF53_SYNC_RTC_LOG_TIMESTAMP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF53_SYNC_RTC_IPM_OUT, 7);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF53_SYNC_RTC_IPM_IN, 8);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_IPM_MSG_CH_8_ENABLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_IPM_MSG_CH_8_RX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_SOC_SECURE_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_APPROTECT_USE_UICR, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_SECURE_APPROTECT_USE_UICR, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GPIO_INIT_PRIORITY, 40);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_COMPATIBLE_NRF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_COMPATIBLE_NRF53X, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SOC_COMPATIBLE_NRF5340_CPUAPP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KOBJECT_TEXT_AREA, 256);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_MPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE, 32);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_ON_EXIT_CPU_IDLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ISA_THUMB2, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ASSEMBLER_ISA_THUMB2, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_COMPILER_ISA_THUMB2, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_STACK_ALIGN_DOUBLE_WORD, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FAULT_DUMP, 2);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BUILTIN_STACK_GUARD, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_STACK_PROTECTION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_STORE_EXC_RETURN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FP_HARDABI, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FP16, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FP16_IEEE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M33, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_SYSTICK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_DWT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_BASEPRI, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_VTOR, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_SPLIM, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_CORTEX_M_HAS_CMSE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARMV7_M_ARMV8_M_MAINLINE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARMV8_M_MAINLINE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARMV8_M_SE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARMV7_M_ARMV8_M_FP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARMV8_M_DSP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM_TRUSTZONE_M, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE, 32);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_HAS_NRF_IDAU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_SWO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARM, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_IS_SET, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LITTLE_ENDIAN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT, 100);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES, 16);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GEN_PRIV_STACKS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ISR_TABLES_LOCAL_DECLARATION_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_EXCEPTION_DEBUG, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_TIMING_FUNCTIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_TRUSTED_EXECUTION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_STACK_PROTECTION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_USERSPACE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_RAMFUNC_SUPPORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_SUPPORTS_COREDUMP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_SUPPORTS_COREDUMP_THREADS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_SUPPORTS_ROM_START, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_SUSPEND_TO_RAM, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_THREAD_ABORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_CODE_DATA_RELOCATION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_HAS_TEE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_HAS_FPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CPU_HAS_MPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPU_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPU_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPU_GAP_FILLING, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SRAM_REGION_PERMISSIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FPU_SHARING, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MULTITHREADING, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUM_COOP_PRIORITIES, 16);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUM_PREEMPT_PRIORITIES, 15);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MAIN_THREAD_PRIORITY, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_COOP_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PREEMPT_ENABLED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PRIORITY_CEILING, -127);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_THREAD_STACK_INFO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SCHED_DUMB, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WAITQ_DUMB, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ERRNO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BOOT_DELAY, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BARRIER_OPERATIONS_ARCH, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ATOMIC_OPERATIONS_BUILTIN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUM_MBOX_ASYNC_MSGS, 10);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_MEM_POOL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SWAP_NONATOMIC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYS_CLOCK_EXISTS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TIMEOUT_64BIT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS, 365);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ARCH_MEM_DOMAIN_SUPPORTS_ISOLATED_STACKS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MEM_DOMAIN_ISOLATED_STACKS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MP_NUM_CPUS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_SUPPORTS_STATIC_INIT_GNU, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_INIT_PRIORITY_OBJECTS, 30);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_INIT_PRIORITY_LIBC, 35);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_INIT_PRIORITY_DEFAULT, 40);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_INIT_PRIORITY_DEVICE, 50);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_APPLICATION_INIT_PRIORITY, 90);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_NRF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CLOCK_CONTROL_NRF_ACCURACY, 50);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CONSOLE_INPUT_MAX_LINE_LEN, 128);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CONSOLE_HAS_DRIVER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CONSOLE_INIT_PRIORITY, 60);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_CONSOLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_CONSOLE_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_CONSOLE_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GPIO_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GPIO_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GPIO_NRFX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_GPIO_NRFX_INTERRUPT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_INTC_INIT_PRIORITY, 40);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_INTC_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_INTC_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MBOX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MBOX_NRFX_IPC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MBOX_INIT_PRIORITY, 40);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MBOX_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MBOX_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PINCTRL_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PINCTRL_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PINCTRL_STORE_REG, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PINCTRL_NRF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SERIAL_HAS_DRIVER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SERIAL_SUPPORT_ASYNC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SERIAL_SUPPORT_INTERRUPT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_NRFX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_NRFX_UARTE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_NRFX_UARTE_LEGACY_SHIM, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_0_ENHANCED_POLL_OUT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_UART_0_NRF_TX_BUFFER_SIZE, 32);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYSTEM_CLOCK_INIT_PRIORITY, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TICKLESS_CAPABLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_USBD_COMMON_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NRF_USBD_COMMON_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_USBC_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_USBC_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FULL_LIBC_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MINIMAL_LIBC_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NEWLIB_LIBC_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PICOLIBC_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NEWLIB_LIBC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_NEWLIB_LIBC_NANO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_COMMON_LIBC_ABORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NEWLIB_LIBC_MIN_REQUIRED_HEAP_SIZE, 2048);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NEWLIB_LIBC_FLOAT_PRINTF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_STDOUT_CONSOLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NEED_LIBC_MEM_PARTITION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CRC, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYS_HEAP_ALLOC_LOOPS, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SYS_HEAP_AUTO, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ZVFS_OPEN_MAX, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MPSC_PBUF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_HAS_POWEROFF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POWEROFF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_COMPLETE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_FULL_INTEGRAL, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_FP_SUPPORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_N_SPECIFIER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CBPRINTF_CONVERT_CHECK_PTR, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POSIX_AEP_CHOICE_NONE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POSIX_OPEN_MAX, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POSIX_PAGE_SIZE, 0x40);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_EVENTFD_MAX, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MAX_PTHREAD_COUNT, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MAX_PTHREAD_KEY_COUNT, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MAX_TIMER_COUNT, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MSG_COUNT_MAX, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POSIX_LIMITS_RTSIG_MAX, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POSIX_MAX_FDS, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_POSIX_MAX_OPEN_FILES, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TIMER_DELAYTIMER_MAX, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SEM_NAMELEN_MAX, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SEM_VALUE_MAX, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LIBGCC_RTLIB, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NOTIFY, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ONOFF, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PRINTK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_EARLY_CONSOLE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ASSERT_VERBOSE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LLEXT_EDK_NAME, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_CORE_INIT_PRIORITY, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_MODE_DEFERRED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_DEFAULT_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_OVERRIDE_LEVEL, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_MAX_LEVEL, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_PRINTK, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_MODE_OVERFLOW, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_PROCESS_TRIGGER_THRESHOLD, 10);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_PROCESS_THREAD, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_PROCESS_THREAD_STARTUP_DELAY_MS, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_PROCESS_THREAD_SLEEP_MS, 1000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_PROCESS_THREAD_STACK_SIZE, 768);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_TRACE_SHORT_TIMESTAMP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_FUNC_NAME_PREFIX_DBG, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_SHOW_COLOR, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_TAG_MAX_LEN, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_SUPPORTS_FORMAT_TIMESTAMP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_FORMAT_TIMESTAMP, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_UART, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_UART_BUFFER_SIZE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_UART_AUTOSTART, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_UART_OUTPUT_TEXT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_BACKEND_UART_OUTPUT_DEFAULT, 0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_USE_VLA, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_SIMPLE_MSG_OPTIMIZE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_FAILURE_REPORT_PERIOD, 1000);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LOG_OUTPUT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_MEM_ATTR, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_DEVICE_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_PM_DEVICE_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TIMER_RANDOM_INITIAL_STATE, 123456789);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_ZEPHYR_0_17, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_GNU_EXTENSIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_ORPHAN_SECTION_WARN, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ROM_END_OFFSET, 0x0);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LD_LINKER_SCRIPT_SUPPORTED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LD_LINKER_TEMPLATE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_SORT_BY_ALIGNMENT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_LAST_SECTION_ID, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_LAST_SECTION_ID_PATTERN, 0xE015E015);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_USE_RELAX, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_ITERABLE_SUBALIGN, 4);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LINKER_DEVNULL_SUPPORT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_STD_C99, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_TOOLCHAIN_SUPPORTS_GNU_EXTENSIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_SIZE_OPTIMIZATIONS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_COMPILER_TRACK_MACRO_EXPANSION, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_COMPILER_COLOR_DIAGNOSTICS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_FORTIFY_SOURCE_COMPILE_TIME, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_COMPILER_OPT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_RUNTIME_ERROR_CHECKS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_KERNEL_BIN_NAME, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_OUTPUT_STAT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_OUTPUT_PRINT_MEMORY_USAGE, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BUILD_GAP_FILL_PATTERN, 0xFF);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_BUILD_OUTPUT_STRIP_PATHS, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_CHECK_INIT_PRIORITIES, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_WARN_DEPRECATED, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_ENFORCE_ZEPHYR_STDINT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LEGACY_GENERATED_INCLUDE_PATH, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUS_CMD_LOG_LEVEL_DEFAULT, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_NUS_CMD_LOG_LEVEL, 3);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LIGHT_SWITCH_CONFIGURE_TX_POWER, 1);
GEN_ABSOLUTE_SYM_KCONFIG(CONFIG_LIGHT_SWITCH_TX_POWER, 3);

GEN_ABS_SYM_END
