/**
 * @file spi_driver.c
 * @brief SPI Driver Implementation for ADXL382
 *
 * Zephyr SPI driver wrapper for ADXL382 communication
 * Based on working implementation from sw2600_20250625
 */

#include "spi_driver.h"
#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/drivers/spi.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/logging/log.h>
#include <zephyr/devicetree.h>

LOG_MODULE_REGISTER(spi_driver, LOG_LEVEL_DBG);

/* ADXL382 SPI Protocol (from working implementation) */
#define ADXL38X_SPI_READ         0x01
#define ADXL38X_SPI_WRITE        0x00

/* Static SPI configuration */
static const struct device *spi_dev;
static struct spi_config spi_cfg;

/**
 * @brief Initialize SPI device for ADXL382 (based on working implementation)
 */
int spi_driver_init(void)
{
    /* Get SPI bus device (based on working implementation) */
    spi_dev = DEVICE_DT_GET(DT_NODELABEL(spi2));
    if (!device_is_ready(spi_dev)) {
        LOG_ERR("SPI bus %s not ready", spi_dev->name);
        return -ENODEV;
    }

    /* Get CS GPIO configuration from device tree */
    const struct gpio_dt_spec spi_cs_spec = {
        .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
        .pin = 9,
        .dt_flags = GPIO_ACTIVE_LOW
    };

    if (!spi_cs_spec.port) {
        LOG_WRN("CS GPIO not defined in device tree");
    } else if (!device_is_ready(spi_cs_spec.port)) {
        LOG_ERR("SPI CS GPIO port %s not ready", spi_cs_spec.port->name);
        return -ENODEV;
    }

    /* Configure SPI (exactly like working implementation) */
    spi_cfg.frequency = 8000000; // 8MHz
    spi_cfg.operation = SPI_OP_MODE_MASTER | SPI_WORD_SET(8) | SPI_TRANSFER_MSB;
    spi_cfg.slave = 0; // First device on SPI bus
    spi_cfg.cs.gpio = spi_cs_spec;
    spi_cfg.cs.delay = 0;

    LOG_DBG("SPI device %s initialized at %u Hz", spi_dev->name, spi_cfg.frequency);
    LOG_DBG("SPI mode - CPOL: %s, CPHA: %s",
            (spi_cfg.operation & SPI_MODE_CPOL) ? "1" : "0",
            (spi_cfg.operation & SPI_MODE_CPHA) ? "1" : "0");
    if (spi_cs_spec.port) {
        LOG_DBG("SPI CS on %s pin %d", spi_cs_spec.port->name, spi_cs_spec.pin);
    }
    LOG_INF("SPI initialized successfully");
    return 0;
}

/**
 * @brief Read ADXL382 register
 */
int spi_reg_read(uint8_t reg_addr, uint8_t *data, uint16_t bytes_number)
{
    int ret;
    uint8_t tx_buffer = (reg_addr << 1) | ADXL38X_SPI_READ;

    if (!spi_dev) {
        LOG_ERR("SPI device not initialized");
        return -EPERM;
    }

    if (!data) {
        LOG_ERR("Data buffer is NULL");
        return -EINVAL;
    }

    struct spi_buf tx_buf[2] = {
        { .buf = &tx_buffer, .len = 1 },
        { .buf = NULL, .len = bytes_number }
    };
    struct spi_buf rx_buf[2] = {
        { .buf = NULL, .len = 1 },
        { .buf = data, .len = bytes_number }
    };

    struct spi_buf_set tx_bufs = { .buffers = tx_buf, .count = ARRAY_SIZE(tx_buf) };
    struct spi_buf_set rx_bufs = { .buffers = rx_buf, .count = ARRAY_SIZE(rx_buf) };

    /* Use spi_transceive (exactly like working implementation) */
    ret = spi_transceive(spi_dev, &spi_cfg, &tx_bufs, &rx_bufs);
    if (ret) {
        LOG_ERR("SPI read error (Addr: 0x%02X): %d", reg_addr, ret);
        return ret;
    }

    LOG_DBG("Read reg 0x%02X: 0x%02X", reg_addr, data[0]);
    return 0;
}

/**
 * @brief Write ADXL382 register
 */
int spi_reg_write(uint8_t reg_addr, const uint8_t *data, uint16_t bytes_number)
{
    int ret;
    uint8_t tx_header = ADXL38X_SPI_WRITE | (reg_addr << 1);

    if (!spi_dev) {
        LOG_ERR("SPI device not initialized");
        return -EPERM;
    }

    if (!data) {
        LOG_ERR("Data buffer is NULL");
        return -EINVAL;
    }

    struct spi_buf tx_buf[2] = {
        { .buf = &tx_header, .len = 1 },
        { .buf = (void *)data, .len = bytes_number }
    };

    struct spi_buf_set tx_bufs = { .buffers = tx_buf, .count = ARRAY_SIZE(tx_buf) };

    /* Use spi_write (exactly like working implementation) */
    ret = spi_write(spi_dev, &spi_cfg, &tx_bufs);
    if (ret) {
        LOG_ERR("SPI write error (Addr: 0x%02X): %d", reg_addr, ret);
        return ret;
    }

    LOG_DBG("Write reg 0x%02X: 0x%02X", reg_addr, data[0]);
    return 0;
}

/**
 * @brief Update ADXL382 register bits
 */
int spi_reg_update_bits(uint8_t reg_addr, uint8_t mask, uint8_t data)
{
    int ret;
    uint8_t reg_val;

    /* Read current register value */
    ret = spi_reg_read(reg_addr, &reg_val, 1);
    if (ret) {
        return ret;
    }

    /* Update bits */
    reg_val = (reg_val & ~mask) | (data & mask);

    /* Write back */
    ret = spi_reg_write(reg_addr, &reg_val, 1);
    if (ret) {
        return ret;
    }

    LOG_DBG("Update reg 0x%02X: mask=0x%02X, data=0x%02X", reg_addr, mask, data);
    return 0;
}
