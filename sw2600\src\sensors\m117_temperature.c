/**
 * @file m117_temperature.c
 * @brief M117 Temperature Sensor Driver Implementation
 * 
 * GPIO I2C implementation for M117 temperature sensor
 * Optimized for ultra-low power consumption
 */

#include "m117_temperature.h"
#include <zephyr/logging/log.h>

LOG_MODULE_REGISTER(m117_temp, LOG_LEVEL_INF);

/* GPIO I2C Configuration */
static const struct gpio_dt_spec sda_pin = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio0)),
    .pin = 24,  // P0.24 - SDA pin
    .dt_flags = GPIO_ACTIVE_HIGH
};

static const struct gpio_dt_spec scl_pin = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 8,   // P1.08 - SCL pin
    .dt_flags = GPIO_ACTIVE_HIGH
};

/* M117 I2C Configuration */
#define M117_I2C_ADDR           0x40
#define M117_CMD_MEASURE        0xF3    // No hold master mode
#define M117_TEMP_COEFF_A       175.72f
#define M117_TEMP_COEFF_B       46.85f

/* GPIO I2C Timing (microseconds) */
#define I2C_DELAY_US            5       // 100kHz I2C timing

/* Static function prototypes */
static void gpio_i2c_delay(void);
static void gpio_i2c_start(void);
static void gpio_i2c_stop(void);
static bool gpio_i2c_write_byte(uint8_t data);
static uint8_t gpio_i2c_read_byte(bool ack);
static uint8_t crc8_calc(const uint8_t *data, uint8_t len);

/**
 * @brief GPIO I2C delay function
 */
static void gpio_i2c_delay(void)
{
    k_busy_wait(I2C_DELAY_US);
}

/**
 * @brief GPIO I2C start condition
 */
static void gpio_i2c_start(void)
{
    gpio_pin_set_dt(&sda_pin, 1);
    gpio_pin_set_dt(&scl_pin, 1);
    gpio_i2c_delay();
    gpio_pin_set_dt(&sda_pin, 0);
    gpio_i2c_delay();
    gpio_pin_set_dt(&scl_pin, 0);
    gpio_i2c_delay();
}

/**
 * @brief GPIO I2C stop condition
 */
static void gpio_i2c_stop(void)
{
    gpio_pin_set_dt(&sda_pin, 0);
    gpio_pin_set_dt(&scl_pin, 1);
    gpio_i2c_delay();
    gpio_pin_set_dt(&sda_pin, 1);
    gpio_i2c_delay();
}

/**
 * @brief GPIO I2C write byte
 */
static bool gpio_i2c_write_byte(uint8_t data)
{
    for (int i = 7; i >= 0; i--) {
        gpio_pin_set_dt(&sda_pin, (data >> i) & 1);
        gpio_i2c_delay();
        gpio_pin_set_dt(&scl_pin, 1);
        gpio_i2c_delay();
        gpio_pin_set_dt(&scl_pin, 0);
        gpio_i2c_delay();
    }
    
    /* Read ACK */
    gpio_pin_configure_dt(&sda_pin, GPIO_INPUT);
    gpio_pin_set_dt(&scl_pin, 1);
    gpio_i2c_delay();
    bool ack = !gpio_pin_get_dt(&sda_pin);
    gpio_pin_set_dt(&scl_pin, 0);
    gpio_pin_configure_dt(&sda_pin, GPIO_OUTPUT);
    gpio_i2c_delay();
    
    return ack;
}

/**
 * @brief GPIO I2C read byte
 */
static uint8_t gpio_i2c_read_byte(bool ack)
{
    uint8_t data = 0;
    
    gpio_pin_configure_dt(&sda_pin, GPIO_INPUT);
    
    for (int i = 7; i >= 0; i--) {
        gpio_pin_set_dt(&scl_pin, 1);
        gpio_i2c_delay();
        if (gpio_pin_get_dt(&sda_pin)) {
            data |= (1 << i);
        }
        gpio_pin_set_dt(&scl_pin, 0);
        gpio_i2c_delay();
    }
    
    /* Send ACK/NACK */
    gpio_pin_configure_dt(&sda_pin, GPIO_OUTPUT);
    gpio_pin_set_dt(&sda_pin, !ack);
    gpio_i2c_delay();
    gpio_pin_set_dt(&scl_pin, 1);
    gpio_i2c_delay();
    gpio_pin_set_dt(&scl_pin, 0);
    gpio_i2c_delay();
    
    return data;
}

/**
 * @brief Calculate CRC8 checksum
 */
static uint8_t crc8_calc(const uint8_t *data, uint8_t len)
{
    uint8_t crc = 0xFF;
    
    for (uint8_t i = 0; i < len; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x31;
            } else {
                crc <<= 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief Initialize M117 temperature sensor
 */
int m117_init(void)
{
    int ret;

    LOG_INF("Initializing M117 temperature sensor...");

    /* Configure GPIO pins */
    if (!gpio_is_ready_dt(&sda_pin) || !gpio_is_ready_dt(&scl_pin)) {
        LOG_ERR("GPIO devices not ready");
        return -ENODEV;
    }

    ret = gpio_pin_configure_dt(&sda_pin, GPIO_OUTPUT);
    if (ret) {
        LOG_ERR("Failed to configure SDA pin: %d", ret);
        return ret;
    }

    ret = gpio_pin_configure_dt(&scl_pin, GPIO_OUTPUT);
    if (ret) {
        LOG_ERR("Failed to configure SCL pin: %d", ret);
        return ret;
    }

    /* Initialize I2C bus to idle state */
    gpio_pin_set_dt(&sda_pin, 1);
    gpio_pin_set_dt(&scl_pin, 1);
    gpio_i2c_delay();

    LOG_INF("M117 temperature sensor initialized");
    return 0;
}

/**
 * @brief Read temperature from M117 sensor
 */
int m117_read_temperature(float *temperature)
{
    uint8_t temp_data[3];
    uint8_t crc_calc_val, crc_read_val;
    uint16_t raw_temp;

    if (!temperature) {
        return -EINVAL;
    }

    LOG_DBG("Starting M117 temperature conversion...");

    /* Start I2C transaction */
    gpio_i2c_start();

    /* Send device address + write */
    if (!gpio_i2c_write_byte((M117_I2C_ADDR << 1) | 0)) {
        LOG_ERR("Failed to send device address");
        gpio_i2c_stop();
        return -EIO;
    }

    /* Send measurement command */
    if (!gpio_i2c_write_byte(M117_CMD_MEASURE)) {
        LOG_ERR("Failed to send measurement command");
        gpio_i2c_stop();
        return -EIO;
    }

    gpio_i2c_stop();

    /* Wait for conversion (max 85ms for 14-bit) */
    k_msleep(100);

    /* Read temperature data */
    gpio_i2c_start();

    /* Send device address + read */
    if (!gpio_i2c_write_byte((M117_I2C_ADDR << 1) | 1)) {
        LOG_ERR("Failed to send read address");
        gpio_i2c_stop();
        return -EIO;
    }

    /* Read temperature data (3 bytes: MSB, LSB, CRC) */
    temp_data[0] = gpio_i2c_read_byte(true);   // MSB
    temp_data[1] = gpio_i2c_read_byte(true);   // LSB
    temp_data[2] = gpio_i2c_read_byte(false);  // CRC

    gpio_i2c_stop();

    /* Verify CRC */
    crc_calc_val = crc8_calc(temp_data, 2);
    crc_read_val = temp_data[2];

    LOG_DBG("Raw temp data: 0x%02X%02X, CRC: 0x%02X", 
            temp_data[0], temp_data[1], crc_read_val);

    if (crc_calc_val != crc_read_val) {
        LOG_WRN("CRC mismatch: calc=0x%02X, read=0x%02X", 
                crc_calc_val, crc_read_val);
    }

    /* Convert to temperature */
    raw_temp = (temp_data[0] << 8) | temp_data[1];
    *temperature = (M117_TEMP_COEFF_A * raw_temp / 65536.0f) - M117_TEMP_COEFF_B;

    LOG_DBG("Temperature: %.2f°C", (double)*temperature);
    return 0;
}

/**
 * @brief Deinitialize M117 temperature sensor
 */
int m117_deinit(void)
{
    /* Set pins to high-impedance state for power saving */
    gpio_pin_configure_dt(&sda_pin, GPIO_DISCONNECTED);
    gpio_pin_configure_dt(&scl_pin, GPIO_DISCONNECTED);
    
    LOG_DBG("M117 temperature sensor deinitialized");
    return 0;
}
