/*
 * SW2600 Step 1: Temperature Sensor Integration
 * 
 * Features:
 * - M117 temperature sensor data collection
 * - Low power mode with 3.5µA sleep current
 * - 1-minute wake cycle for data collection
 * - Power management integration
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/pm/device.h>
#include "sensors/m117_sensor.h"
#include "power/simple_power_manager.h"

LOG_MODULE_REGISTER(sw2600_temp, LOG_LEVEL_INF);

/* Configuration */
#define DATA_COLLECTION_INTERVAL_S  60      // 1 minute data collection cycle
#define TEMPERATURE_READ_COUNT      3       // Number of temperature readings per cycle

/* Global state */
static bool m117_initialized = false;

/**
 * @brief Initialize M117 temperature sensor
 */
static int init_temperature_sensor(void)
{
    int ret;
    
    LOG_INF("Initializing M117 temperature sensor...");
    
    ret = m117_init(M117_MPS_1, M117_REPEAT_HIGH);
    if (ret < 0) {
        LOG_ERR("Failed to initialize M117 sensor: %d", ret);
        return ret;
    }
    
    m117_initialized = true;
    LOG_INF("M117 temperature sensor initialized successfully");
    
    return 0;
}

/**
 * @brief Collect temperature data
 */
static int collect_temperature_data(void)
{
    int ret;
    float temperature;
    float temp_sum = 0.0f;
    int valid_readings = 0;
    
    if (!m117_initialized) {
        LOG_ERR("M117 sensor not initialized");
        return -ENODEV;
    }
    
    LOG_INF("=== Temperature Data Collection ===");
    
    /* Collect multiple temperature readings */
    for (int i = 0; i < TEMPERATURE_READ_COUNT; i++) {
        ret = m117_measure_temperature(&temperature);
        if (ret == 0) {
            LOG_INF("Temperature reading %d: %.2f°C", i + 1, (double)temperature);
            temp_sum += temperature;
            valid_readings++;
        } else {
            LOG_WRN("Temperature reading %d failed: %d", i + 1, ret);
        }
        
        /* Small delay between readings */
        k_sleep(K_MSEC(100));
    }
    
    /* Calculate and display average */
    if (valid_readings > 0) {
        float avg_temperature = temp_sum / valid_readings;
        LOG_INF("Average temperature: %.2f°C (%d valid readings)", 
                (double)avg_temperature, valid_readings);
    } else {
        LOG_ERR("No valid temperature readings obtained");
        return -EIO;
    }
    
    LOG_INF("Temperature data collection completed");
    return 0;
}

/**
 * @brief Suspend I2C device for power saving
 */
static int suspend_i2c_device(void)
{
    const struct device *i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            LOG_WRN("Failed to suspend I2C device: %d", ret);
            return ret;
        }
        LOG_DBG("I2C device suspended for power saving");
    }
    
    return 0;
}

/**
 * @brief Resume I2C device after wake up
 */
static int resume_i2c_device(void)
{
    const struct device *i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
        if (ret < 0) {
            LOG_WRN("Failed to resume I2C device: %d", ret);
            return ret;
        }
        LOG_DBG("I2C device resumed");
    }
    
    return 0;
}

/**
 * @brief Main data collection and power management cycle
 */
static void data_collection_cycle(void)
{
    int ret;
    
    LOG_INF("=== SW2600 Data Collection Cycle ===");
    LOG_INF("Wake up for temperature data collection");
    
    /* Resume I2C device */
    resume_i2c_device();
    
    /* Collect temperature data */
    ret = collect_temperature_data();
    if (ret < 0) {
        LOG_ERR("Temperature data collection failed: %d", ret);
    }
    
    /* Suspend I2C device to save power */
    suspend_i2c_device();
    
    LOG_INF("Data collection completed, entering low power mode");
    LOG_INF("Next wake up in %d seconds", DATA_COLLECTION_INTERVAL_S);
}

/**
 * @brief Main function
 */
int main(void)
{
    int ret;
    
    LOG_INF("=== SW2600 Temperature Sensor Application Starting ===");
    LOG_INF("Features:");
    LOG_INF("  - M117 temperature sensor");
    LOG_INF("  - %d second data collection cycle", DATA_COLLECTION_INTERVAL_S);
    LOG_INF("  - Low power mode (target: 3.5µA)");
    
    /* Initialize power manager */
    ret = simple_power_manager_init();
    if (ret < 0) {
        LOG_ERR("Power manager initialization failed: %d", ret);
        return ret;
    }
    
    /* Initialize temperature sensor */
    ret = init_temperature_sensor();
    if (ret < 0) {
        LOG_ERR("Temperature sensor initialization failed: %d", ret);
        /* Continue without temperature sensor for power testing */
    }
    
    LOG_INF("=== SW2600 Application Started Successfully ===");
    
    /* Main application loop */
    while (1) {
        /* Perform data collection cycle */
        data_collection_cycle();
        
        /* Enter low power mode with wake-up timer */
        ret = simple_power_manager_sleep(DATA_COLLECTION_INTERVAL_S);
        if (ret < 0) {
            LOG_ERR("Failed to enter low power mode: %d", ret);
            /* Fallback to regular sleep */
            k_sleep(K_SECONDS(DATA_COLLECTION_INTERVAL_S));
        }
        
        /* System will wake up here after timer expires */
        LOG_INF("System wake up from low power mode");
    }
    
    return 0;
}
