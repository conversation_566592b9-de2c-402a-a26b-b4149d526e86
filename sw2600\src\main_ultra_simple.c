/*
 * Ultra Simple Power Test - No sensors, minimal configuration
 * Target: 3.5µA sleep current
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/**
 * @brief Configure GPIO for wake-up
 */
static int configure_wakeup_gpio(void)
{
    int ret;
    
    if (!gpio_is_ready_dt(&button)) {
        printk("Button GPIO not ready\n");
        return -ENODEV;
    }
    
    /* Configure button as input with pull-up */
    ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        printk("Failed to configure button GPIO: %d\n", ret);
        return ret;
    }
    
    /* Configure interrupt for wake-up */
    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        printk("Failed to configure button interrupt: %d\n", ret);
        return ret;
    }
    
    printk("Wake-up GPIO configured\n");
    return 0;
}

int main(void)
{
    printk("\n=== SW2600 ULTRA SIMPLE POWER TEST ===\n");
    printk("Target: 3.5µA sleep current\n");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        printk("GPIO config failed: %d\n", ret);
    }
    
    /* Wait a bit for output */
    k_sleep(K_SECONDS(2));
    
    printk("Entering system off mode...\n");
    printk("Press button to wake up\n");
    
    /* Small delay for UART output */
    k_msleep(100);
    
    /* Enter system off mode directly */
    sys_poweroff();
    
    /* Should never reach here */
    printk("ERROR: Should not reach here!\n");
    return 0;
}
