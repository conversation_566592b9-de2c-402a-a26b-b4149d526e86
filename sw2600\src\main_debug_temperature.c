/*
 * SW2600 Debug: Temperature Sensor Test
 * 
 * Simplified version for debugging temperature sensor issues
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/pm/device.h>

/* Test Configuration */
#define TEST_DELAY_S            10      // 10 seconds for testing
#define SLEEP_DURATION_S        30      // 30 seconds sleep

/* Device references */
static const struct device *uart_dev;
static const struct device *i2c_dev;

/**
 * @brief Test I2C device availability
 */
static void test_i2c_device(void)
{
    printk("=== I2C Device Test ===\n");
    
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    if (!i2c_dev) {
        printk("ERROR: I2C device not found\n");
        return;
    }
    
    if (!device_is_ready(i2c_dev)) {
        printk("ERROR: I2C device not ready\n");
        return;
    }
    
    printk("I2C device found and ready\n");
}

/**
 * @brief Suspend devices for power saving
 */
static void suspend_devices(void)
{
    printk("Suspending devices for power saving...\n");
    
    /* Suspend I2C */
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            printk("Failed to suspend I2C: %d\n", ret);
        } else {
            printk("I2C suspended\n");
        }
    }
    
    /* Suspend UART */
    uart_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (uart_dev && device_is_ready(uart_dev)) {
        int ret = pm_device_action_run(uart_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            printk("Failed to suspend UART: %d\n", ret);
        } else {
            printk("UART suspended\n");
        }
    }
}

/**
 * @brief Enter deep sleep mode
 */
static void enter_deep_sleep(void)
{
    printk("=== ENTERING DEEP SLEEP MODE ===\n");
    printk("Expected: Current should drop to ~3.5µA\n");
    printk("System will reset after %d seconds\n", SLEEP_DURATION_S);
    
    /* Small delay to ensure UART output */
    k_msleep(100);
    
    /* Suspend devices */
    suspend_devices();
    
    /* Enter system off mode */
    sys_poweroff();
}

/**
 * @brief Main function
 */
int main(void)
{
    printk("\n=== SW2600 Debug: Temperature Sensor Test ===\n");
    printk("Board: %s\n", CONFIG_BOARD);
    printk("Purpose: Debug temperature sensor and power issues\n");
    printk("\n");
    
    /* Test I2C device */
    test_i2c_device();
    
    /* Simple test sequence */
    printk("Test sequence:\n");
    printk("  1. Wait %d seconds\n", TEST_DELAY_S);
    printk("  2. Enter deep sleep mode (target: 3.5µA)\n");
    printk("  3. Wake up after %d seconds\n", SLEEP_DURATION_S);
    printk("  4. System will reset and repeat\n");
    printk("\n");

    /* Countdown */
    for (int i = TEST_DELAY_S; i > 0; i--) {
        printk("Deep sleep countdown: %d seconds remaining\n", i);
        k_sleep(K_SECONDS(1));
    }

    /* Enter deep sleep */
    enter_deep_sleep();

    /* Should not reach here */
    printk("ERROR: Should not reach here!\n");
    
    /* If sleep failed, continue loop */
    while (1) {
        printk("Sleep failed, retrying in 10 seconds...\n");
        k_sleep(K_SECONDS(10));
        enter_deep_sleep();
    }

    return 0;
}
