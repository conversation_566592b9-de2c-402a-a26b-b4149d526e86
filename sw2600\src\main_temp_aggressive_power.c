/*
 * SW2600 Temperature + Aggressive Power Management
 * Disable I2C completely after temperature reading
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>

#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* Collect temperature data with aggressive power management */
static void collect_temperature_data(void)
{
    LOG_INF("=== Temperature Data Collection ===");
    
    /* Initialize M117 sensor */
    int ret = m117_init(M117_MPS_1, M117_REPEAT_MEDIUM);
    if (ret < 0) {
        LOG_ERR("M117 init failed: %d", ret);
        return;
    }
    
    /* Read temperature */
    float temperature = 0.0f;
    ret = m117_measure_temperature(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature measurement failed: %d", ret);
    } else {
        LOG_INF("Temperature: %.2f°C", (double)temperature);
    }
    
    LOG_INF("=== Data Collection Complete ===");
    
    /* Aggressive power management - disable I2C completely */
    LOG_INF("Disabling I2C for power saving...");
    const struct device *i2c_dev = DEVICE_DT_GET_OR_NULL(DT_NODELABEL(i2c1));
    if (i2c_dev && device_is_ready(i2c_dev)) {
        ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret == 0) {
            LOG_INF("I2C suspended successfully");
        } else {
            LOG_ERR("I2C suspend failed: %d", ret);
        }
    }
}

/* Suspend all devices for ultra-low power */
static void suspend_all_devices(void)
{
    LOG_INF("Suspending all devices...");
    
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
}

int main(void)
{
    LOG_INF("\n=== SW2600 AGGRESSIVE POWER TEST ===");
    LOG_INF("Target: <5µA sleep current");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }
    
    /* Collect temperature data */
    collect_temperature_data();
    
    /* Wait before sleep */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Preparing for ultra-low power mode...");
    
    /* Suspend all devices */
    suspend_all_devices();
    
    LOG_INF("Entering system off mode...");
    LOG_INF("Press button to wake up");
    
    /* Final delay for UART output */
    k_msleep(200);
    
    /* Enter system off mode */
    LOG_INF("Calling sys_poweroff()...");
    k_msleep(100);
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running - sys_poweroff failed!");
    }
    return 0;
}
