[{"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/include -IC:/ncs/zy/sw2600/src -IC:/ncs/zy/sw2600/src/sensors -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wno-maybe-uninitialized -Wno-unused-parameter -Wno-double-promotion -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o CMakeFiles\\app.dir\\src\\main.c.obj -c C:\\ncs\\zy\\sw2600\\src\\main.c", "file": "C:\\ncs\\zy\\sw2600\\src\\main.c", "output": "CMakeFiles\\app.dir\\src\\main.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/include -IC:/ncs/zy/sw2600/src -IC:/ncs/zy/sw2600/src/sensors -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wno-maybe-uninitialized -Wno-unused-parameter -Wno-double-promotion -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o CMakeFiles\\app.dir\\src\\sensors\\m117_temperature.c.obj -c C:\\ncs\\zy\\sw2600\\src\\sensors\\m117_temperature.c", "file": "C:\\ncs\\zy\\sw2600\\src\\sensors\\m117_temperature.c", "output": "CMakeFiles\\app.dir\\src\\sensors\\m117_temperature.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc32c_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc32c_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc32c_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc32c_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc32_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc32_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc32_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc32_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc24_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc24_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc24_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc24_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc16_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc16_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc16_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc16_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc8_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc8_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc8_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc8_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc7_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc7_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc7_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc7_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc4_sw.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc4_sw.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\crc\\crc4_sw.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\crc\\crc4_sw.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\heap\\heap.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\heap\\heap.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\heap\\heap.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\heap\\heap.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf_packaged.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\cbprintf_packaged.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\cbprintf_packaged.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf_packaged.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\printk.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\printk.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\printk.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\printk.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\sem.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\sem.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\sem.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\sem.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\thread_entry.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\thread_entry.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\thread_entry.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\thread_entry.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf_complete.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\cbprintf_complete.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\cbprintf_complete.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf_complete.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\cbprintf.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\cbprintf.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\assert.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\assert.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\assert.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\assert.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\mpsc_pbuf.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\mpsc_pbuf.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\mpsc_pbuf.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\mpsc_pbuf.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\poweroff.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\poweroff.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\os\\poweroff.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\poweroff.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\dec.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\dec.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\dec.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\dec.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\hex.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\hex.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\hex.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\hex.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\rb.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\rb.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\rb.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\rb.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\timeutil.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\timeutil.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\timeutil.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\timeutil.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\bitarray.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\bitarray.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\bitarray.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\bitarray.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\onoff.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\onoff.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\onoff.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\onoff.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\notify.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\notify.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\utils\\notify.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\notify.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\misc\\generated\\configs.c.obj -c C:\\ncs\\zy\\sw2600\\build\\zephyr\\misc\\generated\\configs.c", "file": "C:\\ncs\\zy\\sw2600\\build\\zephyr\\misc\\generated\\configs.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\misc\\generated\\configs.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_core.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_core.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_core.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_core.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_mgmt.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_mgmt.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_mgmt.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_mgmt.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_cache.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_cache.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_cache.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_cache.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_msg.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_msg.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_msg.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_msg.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_output.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_output.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\log_output.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_output.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\backends\\log_backend_uart.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\backends\\log_backend_uart.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\logging\\backends\\log_backend_uart.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\backends\\log_backend_uart.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\mem_mgmt\\mem_attr.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\mem_mgmt\\mem_attr.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\mem_mgmt\\mem_attr.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\mem_mgmt\\mem_attr.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\pm\\device.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\pm\\device.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\pm\\device.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\pm\\device.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\pm\\device_system_managed.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\pm\\device_system_managed.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\pm\\device_system_managed.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\pm\\device_system_managed.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\tracing\\tracing_none.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\tracing\\tracing_none.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\subsys\\tracing\\tracing_none.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\subsys\\tracing\\tracing_none.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v2.9.0-zigbee\\nrf\\lib\\boot_banner\\banner.c.obj -c C:\\ncs\\v2.9.0-zigbee\\nrf\\lib\\boot_banner\\banner.c", "file": "C:\\ncs\\v2.9.0-zigbee\\nrf\\lib\\boot_banner\\banner.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v2.9.0-zigbee\\nrf\\lib\\boot_banner\\banner.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c.obj -c C:\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c", "file": "C:\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c.obj -c C:\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c", "file": "C:\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c", "output": "zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v2.9.0-zigbee\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -fno-lto -o zephyr\\CMakeFiles\\offsets.dir\\arch\\arm\\core\\offsets\\offsets.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\offsets\\offsets.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\offsets\\offsets.c", "output": "zephyr\\CMakeFiles\\offsets.dir\\arch\\arm\\core\\offsets\\offsets.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr_pre0.dir\\misc\\empty_file.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "output": "zephyr\\CMakeFiles\\zephyr_pre0.dir\\misc\\empty_file.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr_final.dir\\misc\\empty_file.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "output": "zephyr\\CMakeFiles\\zephyr_final.dir\\misc\\empty_file.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr_final.dir\\isr_tables.c.obj -c C:\\ncs\\zy\\sw2600\\build\\zephyr\\isr_tables.c", "file": "C:\\ncs\\zy\\sw2600\\build\\zephyr\\isr_tables.c", "output": "zephyr\\CMakeFiles\\zephyr_final.dir\\isr_tables.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/common/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\common\\CMakeFiles\\arch__common.dir\\sw_isr_common.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\common\\sw_isr_common.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\common\\sw_isr_common.c", "output": "zephyr\\arch\\common\\CMakeFiles\\arch__common.dir\\sw_isr_common.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\common\\CMakeFiles\\isr_tables.dir\\isr_tables.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\common\\isr_tables.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\common\\isr_tables.c", "output": "zephyr\\arch\\common\\CMakeFiles\\isr_tables.dir\\isr_tables.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\fatal.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\fatal.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\fatal.c", "output": "zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\fatal.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\nmi.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\nmi.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\nmi.c", "output": "zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\nmi.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\nmi_on_reset.S.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\nmi_on_reset.S", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\nmi_on_reset.S", "output": "zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\nmi_on_reset.S.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\exc_exit.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\exc_exit.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\exc_exit.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\exc_exit.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fault.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\fault.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\fault.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fault.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fault_s.S.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\fault_s.S", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\fault_s.S", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fault_s.S.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fpu.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\fpu.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\fpu.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fpu.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\reset.S.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\reset.S", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\reset.S", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\reset.S.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\scb.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\scb.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\scb.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\scb.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\thread_abort.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\thread_abort.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\thread_abort.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\thread_abort.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\vector_table.S.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\vector_table.S", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\vector_table.S", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\vector_table.S.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\swap.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\swap.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\swap.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\swap.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\swap_helper.S.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\swap_helper.S", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\swap_helper.S", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\swap_helper.S.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\irq_manage.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\irq_manage.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\irq_manage.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\irq_manage.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\prep_c.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\prep_c.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\prep_c.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\prep_c.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\thread.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\thread.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\thread.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\thread.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\cpu_idle.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\cpu_idle.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\cpu_idle.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\cpu_idle.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\irq_init.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\irq_init.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\irq_init.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\irq_init.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\isr_wrapper.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\isr_wrapper.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\isr_wrapper.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\isr_wrapper.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\cmse\\CMakeFiles\\arch__arm__core__cortex_m__cmse.dir\\arm_core_cmse.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\cmse\\arm_core_cmse.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\cortex_m\\cmse\\arm_core_cmse.c", "output": "zephyr\\arch\\arch\\arm\\core\\cortex_m\\cmse\\CMakeFiles\\arch__arm__core__cortex_m__cmse.dir\\arm_core_cmse.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/core/mpu/cortex_m -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_core_mpu.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\mpu\\arm_core_mpu.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\mpu\\arm_core_mpu.c", "output": "zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_core_mpu.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/core/mpu/cortex_m -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_mpu.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu.c", "output": "zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_mpu.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/core/mpu/cortex_m -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_mpu_regions.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu_regions.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu_regions.c", "output": "zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_mpu_regions.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\newlib\\CMakeFiles\\lib__libc__newlib.dir\\libc-hooks.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\libc\\newlib\\libc-hooks.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\libc\\newlib\\libc-hooks.c", "output": "zephyr\\lib\\libc\\newlib\\CMakeFiles\\lib__libc__newlib.dir\\libc-hooks.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -fno-builtin-malloc -o zephyr\\lib\\libc\\common\\CMakeFiles\\lib__libc__common.dir\\source\\stdlib\\abort.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\libc\\common\\source\\stdlib\\abort.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\lib\\libc\\common\\source\\stdlib\\abort.c", "output": "zephyr\\lib\\libc\\common\\CMakeFiles\\lib__libc__common.dir\\source\\stdlib\\abort.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\lib\\posix\\options\\CMakeFiles\\lib__posix__options.dir\\C_\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "output": "zephyr\\lib\\posix\\options\\CMakeFiles\\lib__posix__options.dir\\C_\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_base_addresses.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\validate_base_addresses.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\validate_base_addresses.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_base_addresses.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/adc.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/dt-util.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/gpio/gpio.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/i2c/i2c.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/pwm/pwm.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc.h -include C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/regulator/nrf5x.h -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_binding_headers.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\validate_binding_headers.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\validate_binding_headers.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_binding_headers.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_enabled_instances.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\validate_enabled_instances.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\validate_enabled_instances.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_enabled_instances.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\soc.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\nrf53\\soc.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\nrf53\\soc.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\soc.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\nrf53_cpunet_mgmt.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\nrf53\\nrf53_cpunet_mgmt.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\nrf53\\nrf53_cpunet_mgmt.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\nrf53_cpunet_mgmt.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\sync_rtc.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\nrf53\\sync_rtc.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\nrf53\\sync_rtc.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\sync_rtc.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\common\\poweroff.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\common\\poweroff.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\soc\\nordic\\common\\poweroff.c", "output": "zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\common\\poweroff.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/arch/common/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\interrupt_controller\\CMakeFiles\\drivers__interrupt_controller.dir\\C_\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c", "output": "zephyr\\drivers\\interrupt_controller\\CMakeFiles\\drivers__interrupt_controller.dir\\C_\\ncs\\v2.9.0-zigbee\\zephyr\\misc\\empty_file.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\clock_control\\CMakeFiles\\drivers__clock_control.dir\\clock_control_nrf.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\clock_control\\clock_control_nrf.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\clock_control\\clock_control_nrf.c", "output": "zephyr\\drivers\\clock_control\\CMakeFiles\\drivers__clock_control.dir\\clock_control_nrf.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\console\\CMakeFiles\\drivers__console.dir\\uart_console.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\console\\uart_console.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\console\\uart_console.c", "output": "zephyr\\drivers\\console\\CMakeFiles\\drivers__console.dir\\uart_console.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\gpio\\CMakeFiles\\drivers__gpio.dir\\gpio_nrfx.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\gpio\\gpio_nrfx.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\gpio\\gpio_nrfx.c", "output": "zephyr\\drivers\\gpio\\CMakeFiles\\drivers__gpio.dir\\gpio_nrfx.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\mbox\\CMakeFiles\\drivers__mbox.dir\\mbox_nrfx_ipc.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\mbox\\mbox_nrfx_ipc.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\mbox\\mbox_nrfx_ipc.c", "output": "zephyr\\drivers\\mbox\\CMakeFiles\\drivers__mbox.dir\\mbox_nrfx_ipc.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\pinctrl\\CMakeFiles\\drivers__pinctrl.dir\\common.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\pinctrl\\common.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\pinctrl\\common.c", "output": "zephyr\\drivers\\pinctrl\\CMakeFiles\\drivers__pinctrl.dir\\common.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\pinctrl\\CMakeFiles\\drivers__pinctrl.dir\\pinctrl_nrf.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\pinctrl\\pinctrl_nrf.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\pinctrl\\pinctrl_nrf.c", "output": "zephyr\\drivers\\pinctrl\\CMakeFiles\\drivers__pinctrl.dir\\pinctrl_nrf.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\serial\\CMakeFiles\\drivers__serial.dir\\uart_nrfx_uarte.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\serial\\uart_nrfx_uarte.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\serial\\uart_nrfx_uarte.c", "output": "zephyr\\drivers\\serial\\CMakeFiles\\drivers__serial.dir\\uart_nrfx_uarte.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\timer\\CMakeFiles\\drivers__timer.dir\\sys_clock_init.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\timer\\sys_clock_init.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\timer\\sys_clock_init.c", "output": "zephyr\\drivers\\timer\\CMakeFiles\\drivers__timer.dir\\sys_clock_init.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\drivers\\timer\\CMakeFiles\\drivers__timer.dir\\nrf_rtc_timer.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\timer\\nrf_rtc_timer.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\drivers\\timer\\nrf_rtc_timer.c", "output": "zephyr\\drivers\\timer\\CMakeFiles\\drivers__timer.dir\\nrf_rtc_timer.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\nrf\\drivers\\hw_cc3xx\\CMakeFiles\\..__nrf__drivers__hw_cc3xx.dir\\hw_cc3xx.c.obj -c C:\\ncs\\v2.9.0-zigbee\\nrf\\drivers\\hw_cc3xx\\hw_cc3xx.c", "file": "C:\\ncs\\v2.9.0-zigbee\\nrf\\drivers\\hw_cc3xx\\hw_cc3xx.c", "output": "modules\\nrf\\drivers\\hw_cc3xx\\CMakeFiles\\..__nrf__drivers__hw_cc3xx.dir\\hw_cc3xx.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\mdk\\system_nrf5340_application.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\mdk\\system_nrf5340_application.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\mdk\\system_nrf5340_application.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\mdk\\system_nrf5340_application.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\nrfx_glue.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\modules\\hal_nordic\\nrfx\\nrfx_glue.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\modules\\hal_nordic\\nrfx\\nrfx_glue.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\nrfx_glue.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_flag32_allocator.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_flag32_allocator.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_flag32_allocator.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_flag32_allocator.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_ipc.c.obj -c C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_ipc.c", "file": "C:\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_ipc.c", "output": "modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v2.9.0-zigbee\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_ipc.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\main_weak.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\main_weak.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\main_weak.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\main_weak.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\banner.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\banner.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\banner.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\banner.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\busy_wait.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\busy_wait.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\busy_wait.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\busy_wait.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\device.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\device.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\device.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\device.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\errno.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\errno.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\errno.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\errno.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\fatal.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\fatal.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\fatal.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\fatal.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\init.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\init.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\init.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\init.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\init_static.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\init_static.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\init_static.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\init_static.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\kheap.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\kheap.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\kheap.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\kheap.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mem_slab.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mem_slab.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mem_slab.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\mem_slab.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\float.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\float.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\float.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\float.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\version.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\version.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\version.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\version.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\idle.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\idle.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\idle.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\idle.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mailbox.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mailbox.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mailbox.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\mailbox.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\msg_q.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\msg_q.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\msg_q.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\msg_q.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mutex.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mutex.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mutex.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\mutex.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\queue.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\queue.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\queue.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\queue.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\sem.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\sem.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\sem.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\sem.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\stack.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\stack.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\stack.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\stack.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\system_work_q.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\system_work_q.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\system_work_q.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\system_work_q.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\work.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\work.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\work.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\work.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\condvar.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\condvar.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\condvar.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\condvar.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\priority_queues.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\priority_queues.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\priority_queues.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\priority_queues.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\thread.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\thread.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\thread.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\thread.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\sched.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\sched.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\sched.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\sched.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\xip.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\xip.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\xip.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\xip.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\timeout.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\timeout.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\timeout.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\timeout.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\timer.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\timer.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\timer.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\timer.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mempool.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mempool.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\mempool.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\mempool.c.obj"}, {"directory": "C:/ncs/zy/sw2600/build", "command": "C:\\ncs\\toolchains\\b620d30767\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=32768 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -D_ANSI_SOURCE -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v2.9.0-zigbee/zephyr/kernel/include -IC:/ncs/v2.9.0-zigbee/zephyr/arch/arm/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr -IC:/ncs/v2.9.0-zigbee/zephyr/include -IC:/ncs/zy/sw2600/build/zephyr/include/generated -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic -IC:/ncs/v2.9.0-zigbee/zephyr/lib/libc/newlib/include -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/nrf53/. -IC:/ncs/v2.9.0-zigbee/zephyr/soc/nordic/common/. -IC:/ncs/v2.9.0-zigbee/nrf/include -IC:/ncs/v2.9.0-zigbee/nrf/tests/include -IC:/ncs/v2.9.0-zigbee/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v2.9.0-zigbee/zephyr/modules/cmsis/. -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk -IC:/ncs/v2.9.0-zigbee/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v2.9.0-zigbee/zephyr/lib/libc/common/include -isystem C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/include -Os -DNDEBUG -Wshadow -fno-strict-aliasing -Os -imacros C:/ncs/zy/sw2600/build/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mfp16-format=ieee --sysroot=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/ncs/zy/sw2600=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v2.9.0-zigbee=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=nano.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\dynamic_disabled.c.obj -c C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\dynamic_disabled.c", "file": "C:\\ncs\\v2.9.0-zigbee\\zephyr\\kernel\\dynamic_disabled.c", "output": "zephyr\\kernel\\CMakeFiles\\kernel.dir\\dynamic_disabled.c.obj"}]