/*
 * Simple Power Manager Header
 * 
 * Simplified power management for SW2600 temperature integration
 */

#ifndef SIMPLE_POWER_MANAGER_H
#define SIMPLE_POWER_MANAGER_H

#include <zephyr/kernel.h>

/**
 * @brief Initialize simple power manager
 * @return 0 on success, negative error code on failure
 */
int simple_power_manager_init(void);

/**
 * @brief Enter deep sleep mode
 * @param sleep_duration_seconds Sleep duration in seconds
 * @return 0 on success, negative error code on failure
 */
int simple_power_manager_sleep(uint32_t sleep_duration_seconds);

/**
 * @brief Resume peripherals after wake up
 * @return 0 on success, negative error code on failure
 */
int simple_power_manager_resume(void);

#endif /* SIMPLE_POWER_MANAGER_H */
