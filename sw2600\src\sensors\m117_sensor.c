#include <zephyr/kernel.h>
#include <zephyr/logging/log.h>
#include "zephyr_i2c_driver.h"
#include "m117_sensor.h"

LOG_MODULE_REGISTER(m117_sensor, CONFIG_LOG_DEFAULT_LEVEL);

// M117设备地址
#define M117_ADDR 0x44

// M117命令
#define M117_CMD_CONVERT_T       0xCC44  // 测量温度
#define M117_CMD_READ_STATUS     0xF32D  // 读取状态和配置寄存器
#define M117_CMD_CLEAR_STATUS    0x3041  // 清除状态寄存器
#define M117_CMD_WRITE_CONFIG    0x5206  // 写配置寄存器
#define M117_CMD_BREAK           0x3093  // 中止周期测量

// 配置寄存器位定义
#define CFG_MPS_MASK             0x1C
#define CFG_REPEAT_MASK          0x03

#define CFG_MPS_SINGLE           0x00
#define CFG_MPS_HALF             0x04
#define CFG_MPS_1                0x08
#define CFG_MPS_2                0x0C
#define CFG_MPS_4                0x10
#define CFG_MPS_10               0x14

#define CFG_REP_LOW              0x00
#define CFG_REP_MEDIUM           0x01
#define CFG_REP_HIGH             0x02

// 发送命令（无数据）
static int m117_send_cmd(uint16_t cmd)
{
    uint8_t cmd_bytes[2];
    cmd_bytes[0] = (uint8_t)(cmd >> 8);
    cmd_bytes[1] = (uint8_t)(cmd & 0xFF);
    
    return i2c_write_bytes(M117_ADDR, cmd_bytes, 2);
}

// 发送命令并写数据
static int m117_send_cmd_with_data(uint16_t cmd, uint8_t *data, uint8_t len)
{
    uint8_t buf[2 + len];
    buf[0] = (uint8_t)(cmd >> 8);
    buf[1] = (uint8_t)(cmd & 0xFF);
    
    for (int i = 0; i < len; i++) {
        buf[2 + i] = data[i];
    }
    
    return i2c_write_bytes(M117_ADDR, buf, 2 + len);
}

// 发送命令并读数据
static int m117_send_cmd_with_read(uint16_t cmd, uint8_t *data, uint8_t len)
{
    uint8_t cmd_bytes[2];
    cmd_bytes[0] = (uint8_t)(cmd >> 8);
    cmd_bytes[1] = (uint8_t)(cmd & 0xFF);
    
    return i2c_write_read_bytes(M117_ADDR, cmd_bytes, 2, data, len);
}

// 初始化M117传感器
int m117_init(m117_mps_t mps, m117_repeat_t repeat)
{
    int ret;
    
    // 初始化I2C驱动
    ret = i2c_driver_init();
    if (ret < 0) {
        LOG_ERR("Failed to initialize I2C driver");
        return ret;
    }
    
    // 读取状态和配置
    uint8_t status_config[3];
    ret = m117_send_cmd_with_read(M117_CMD_READ_STATUS, status_config, 3);
    if (ret < 0) {
        LOG_ERR("Failed to read sensor status: %d", ret);
        return ret;
    }
    
    // 验证校验和
    uint8_t crc = calculate_crc8(status_config, 2);
    if (crc != status_config[2]) {
        LOG_ERR("Status read checksum error");
        return -1;
    }
    
    // 设置配置寄存器
    uint8_t config_data[3];
    config_data[0] = (status_config[1] & ~CFG_MPS_MASK & ~CFG_REPEAT_MASK) | mps | repeat;
    config_data[1] = 0xFF; // 保留字节
    config_data[2] = calculate_crc8(config_data, 2); // 计算校验和
    
    ret = m117_send_cmd_with_data(M117_CMD_WRITE_CONFIG, config_data, 3);
    if (ret < 0) {
        LOG_ERR("Failed to set configuration register: %d", ret);
        return ret;
    }
    
    // 清除状态位
    ret = m117_send_cmd(M117_CMD_CLEAR_STATUS);
    if (ret < 0) {
        LOG_ERR("Failed to clear status bits: %d", ret);
        return ret;
    }
    
    LOG_DBG("M117 temperature sensor initialized successfully");
    return 0;
}

// 启动温度转换
int m117_start_conversion(void)
{
    int ret = m117_send_cmd(M117_CMD_CONVERT_T);
    if (ret < 0) {
        LOG_ERR("Failed to start temperature conversion: %d", ret);
        return ret;
    }
    return 0;
}

// 读取温度值（轮询方式）
int m117_read_temperature(float *temp)
{
    int ret;
    uint8_t data[3];
    
    // 等待转换完成
    k_sleep(K_MSEC(15)); // 高重复性最长13ms
    
    // 读取温度数据
    ret = i2c_read_bytes(M117_ADDR, data, 3);
    if (ret < 0) {
        LOG_ERR("Failed to read temperature data: %d", ret);
        return ret;
    }
    
    // 验证校验和
    uint8_t crc = calculate_crc8(data, 2);
    if (crc != data[2]) {
        LOG_ERR("Temperature read checksum error");
        return -1;
    }
    
    // 转换温度值
    int16_t raw_temp = (data[0] << 8) | data[1];
    *temp = ((float)raw_temp / 256.0f) + 40.0f;
    
    return 0;
}

// 一次性读取温度（结合启动转换和读取）
int m117_measure_temperature(float *temp)
{
    int ret = m117_start_conversion();
    if (ret < 0) {
        return ret;
    }
    
    return m117_read_temperature(temp);
}
