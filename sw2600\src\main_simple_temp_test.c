/*
 * SW2600 Simple Temperature Test
 * 
 * Based on working reference implementation
 * Focus: Get M117 working first, then add low power
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(sw2600_simple, CONFIG_LOG_DEFAULT_LEVEL);

/* Configuration */
#define TEMPERATURE_READ_COUNT      3       // Number of temperature readings per cycle
#define TEST_INTERVAL_S             10      // Test every 10 seconds

/* Global state */
static bool m117_initialized = false;

/**
 * @brief Initialize M117 temperature sensor
 */
static int init_temperature_sensor(void)
{
    int ret;
    
    LOG_INF("Initializing M117 temperature sensor...");
    
    ret = m117_init(M117_MPS_1, M117_REPEAT_HIGH);
    if (ret < 0) {
        LOG_ERR("Failed to initialize M117 sensor: %d", ret);
        return ret;
    }
    
    m117_initialized = true;
    LOG_INF("M117 temperature sensor initialized successfully");
    
    return 0;
}

/**
 * @brief Test temperature reading
 */
static int test_temperature_reading(void)
{
    int ret;
    float temperature;
    float temp_sum = 0.0f;
    int valid_readings = 0;
    
    if (!m117_initialized) {
        LOG_ERR("M117 sensor not initialized");
        return -ENODEV;
    }
    
    LOG_INF("=== Temperature Reading Test ===");
    
    /* Collect multiple temperature readings */
    for (int i = 0; i < TEMPERATURE_READ_COUNT; i++) {
        ret = m117_measure_temperature(&temperature);
        if (ret == 0) {
            LOG_INF("Temperature reading %d: %.2f°C", i + 1, (double)temperature);
            temp_sum += temperature;
            valid_readings++;
        } else {
            LOG_WRN("Temperature reading %d failed: %d", i + 1, ret);
        }
        
        /* Small delay between readings */
        k_sleep(K_MSEC(100));
    }
    
    /* Calculate and display average */
    if (valid_readings > 0) {
        float avg_temperature = temp_sum / valid_readings;
        LOG_INF("Average temperature: %.2f°C (%d valid readings)", 
                (double)avg_temperature, valid_readings);
        LOG_INF("Temperature test completed successfully");
        return 0;
    } else {
        LOG_ERR("No valid temperature readings obtained");
        return -EIO;
    }
}

/**
 * @brief Main function
 */
int main(void)
{
    int ret;
    
    LOG_INF("\n=== SW2600 Simple Temperature Test ===");
    LOG_INF("Features:");
    LOG_INF("  - M117 temperature sensor test");
    LOG_INF("  - %d second test interval", TEST_INTERVAL_S);
    LOG_INF("  - No power management (debug mode)");
    LOG_INF("");
    
    /* Initialize temperature sensor */
    ret = init_temperature_sensor();
    if (ret < 0) {
        LOG_ERR("Temperature sensor initialization failed: %d", ret);
        LOG_ERR("System will continue with test loop anyway...");
    }
    
    LOG_INF("=== SW2600 Simple Test Started ===");
    
    /* Main test loop */
    while (1) {
        /* Test temperature reading */
        ret = test_temperature_reading();
        if (ret < 0) {
            LOG_ERR("Temperature test failed: %d", ret);
        }
        
        LOG_INF("Next test in %d seconds...", TEST_INTERVAL_S);
        LOG_INF(""); // Empty line for readability
        
        /* Wait for next test */
        k_sleep(K_SECONDS(TEST_INTERVAL_S));
    }
    
    return 0;
}
