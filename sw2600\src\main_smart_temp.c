/*
 * SW2600 Smart Temperature + Ultra Low Power
 * Dynamic I2C enable/disable for optimal power consumption
 * Target: 3.5µA sleep current with real temperature reading
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/drivers/i2c.h>

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* M117 I2C address and commands */
#define M117_I2C_ADDR           0x48
#define M117_CMD_MEASURE_TEMP   0xF3

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* Smart I2C temperature reading with power management */
static int smart_read_temperature(float *temperature)
{
    LOG_INF("=== Smart Temperature Reading ===");
    
    /* Get I2C device - only when needed */
    const struct device *i2c_dev = DEVICE_DT_GET_OR_NULL(DT_NODELABEL(i2c1));
    if (!i2c_dev) {
        LOG_ERR("I2C device not found");
        return -ENODEV;
    }
    
    /* Resume I2C device */
    int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
    if (ret < 0) {
        LOG_ERR("Failed to resume I2C: %d", ret);
        return ret;
    }
    LOG_INF("I2C resumed");
    
    /* Wait for I2C to stabilize */
    k_msleep(10);
    
    /* Send temperature measurement command */
    uint8_t cmd = M117_CMD_MEASURE_TEMP;
    ret = i2c_write(i2c_dev, &cmd, 1, M117_I2C_ADDR);
    if (ret < 0) {
        LOG_ERR("Failed to send measure command: %d", ret);
        goto cleanup;
    }
    
    /* Wait for measurement (typical 85ms for 14-bit) */
    k_msleep(100);
    
    /* Read temperature data */
    uint8_t data[3];
    ret = i2c_read(i2c_dev, data, 3, M117_I2C_ADDR);
    if (ret < 0) {
        LOG_ERR("Failed to read temperature: %d", ret);
        goto cleanup;
    }
    
    /* Convert raw data to temperature */
    uint16_t raw_temp = (data[0] << 8) | data[1];
    raw_temp &= 0xFFFC; // Clear status bits
    *temperature = -46.85f + 175.72f * (raw_temp / 65536.0f);
    
    LOG_INF("Temperature: %.2f°C", (double)*temperature);
    ret = 0;

cleanup:
    /* Immediately suspend I2C to save power */
    int suspend_ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
    if (suspend_ret < 0) {
        LOG_ERR("Failed to suspend I2C: %d", suspend_ret);
    } else {
        LOG_INF("I2C suspended for power saving");
    }
    
    LOG_INF("=== Temperature Reading Complete ===");
    return ret;
}

int main(void)
{
    LOG_INF("\n=== SW2600 SMART TEMP + ULTRA LOW POWER ===");
    LOG_INF("Target: 3.5µA sleep current with real temperature");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }
    
    /* Smart temperature reading with power management */
    float temperature = 0.0f;
    ret = smart_read_temperature(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature reading failed: %d", ret);
        /* Continue to sleep mode anyway */
    }
    
    /* Wait before sleep */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Entering ultra-low power mode...");
    LOG_INF("Press button to wake up");
    
    /* Small delay for UART output */
    k_msleep(200);
    
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
    
    /* Final delay */
    k_msleep(100);
    
    /* Enter system off mode */
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running!");
    }
    return 0;
}
