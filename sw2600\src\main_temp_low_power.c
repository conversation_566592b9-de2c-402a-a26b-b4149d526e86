/*
 * SW2600 Temperature + Low Power
 * 
 * Features:
 * - M117 temperature sensor (verified working)
 * - Low power mode with 3.5µA sleep current
 * - 1-minute wake cycle for temperature measurement
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/pm/device.h>
#include <zephyr/logging/log.h>
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(sw2600_temp_lp, CONFIG_LOG_DEFAULT_LEVEL);

/* Configuration */
#define DATA_COLLECTION_INTERVAL_S  60      // 1 minute data collection cycle
#define TEMPERATURE_READ_COUNT      3       // Number of temperature readings per cycle
#define COUNTDOWN_DELAY_S           5       // Countdown before sleep

/* Device references */
static const struct device *uart_dev;
static const struct device *i2c_dev;

/* Global state */
static bool m117_initialized = false;

/**
 * @brief Initialize M117 temperature sensor
 */
static int init_temperature_sensor(void)
{
    int ret;
    
    LOG_INF("Initializing M117 temperature sensor...");
    
    ret = m117_init(M117_MPS_1, M117_REPEAT_HIGH);
    if (ret < 0) {
        LOG_ERR("Failed to initialize M117 sensor: %d", ret);
        return ret;
    }
    
    m117_initialized = true;
    LOG_INF("M117 temperature sensor initialized successfully");
    
    return 0;
}

/**
 * @brief Collect temperature data
 */
static int collect_temperature_data(void)
{
    int ret;
    float temperature;
    float temp_sum = 0.0f;
    int valid_readings = 0;
    
    if (!m117_initialized) {
        LOG_ERR("M117 sensor not initialized");
        return -ENODEV;
    }
    
    LOG_INF("=== Temperature Data Collection ===");
    
    /* Collect multiple temperature readings */
    for (int i = 0; i < TEMPERATURE_READ_COUNT; i++) {
        ret = m117_measure_temperature(&temperature);
        if (ret == 0) {
            LOG_INF("Temperature reading %d: %.2f°C", i + 1, (double)temperature);
            temp_sum += temperature;
            valid_readings++;
        } else {
            LOG_WRN("Temperature reading %d failed: %d", i + 1, ret);
        }
        
        /* Small delay between readings */
        k_sleep(K_MSEC(100));
    }
    
    /* Calculate and display average */
    if (valid_readings > 0) {
        float avg_temperature = temp_sum / valid_readings;
        LOG_INF("Average temperature: %.2f°C (%d valid readings)", 
                (double)avg_temperature, valid_readings);
        LOG_INF("Temperature data collection completed");
        return 0;
    } else {
        LOG_ERR("No valid temperature readings obtained");
        return -EIO;
    }
}

/**
 * @brief Resume I2C device after wake up
 */
static int resume_i2c_device(void)
{
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
        if (ret < 0 && ret != -EALREADY) {
            LOG_WRN("Failed to resume I2C device: %d", ret);
            return ret;
        }
        LOG_DBG("I2C device resumed");
    }
    
    return 0;
}

/**
 * @brief Suspend devices for power saving
 */
static void suspend_devices(void)
{
    LOG_INF("Suspending devices for power saving...");
    
    /* Suspend I2C */
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0 && ret != -EALREADY) {
            LOG_WRN("Failed to suspend I2C: %d", ret);
        } else {
            LOG_DBG("I2C suspended");
        }
    }
    
    /* Suspend UART */
    uart_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (uart_dev && device_is_ready(uart_dev)) {
        int ret = pm_device_action_run(uart_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0 && ret != -EALREADY) {
            LOG_WRN("Failed to suspend UART: %d", ret);
        } else {
            LOG_DBG("UART suspended");
        }
    }
}

/**
 * @brief Enter deep sleep mode
 */
static void enter_deep_sleep(void)
{
    LOG_INF("=== ENTERING DEEP SLEEP MODE ===");
    LOG_INF("Expected: Current should drop to ~3.5µA");
    LOG_INF("System will reset after %d seconds", DATA_COLLECTION_INTERVAL_S);
    
    /* Small delay to ensure UART output */
    k_msleep(100);
    
    /* Suspend devices */
    suspend_devices();
    
    /* Enter system off mode */
    sys_poweroff();
}

/**
 * @brief Main data collection and power management cycle
 */
static void data_collection_cycle(void)
{
    int ret;
    
    LOG_INF("=== SW2600 Data Collection Cycle ===");
    LOG_INF("Wake up for temperature data collection");
    
    /* Resume I2C device */
    resume_i2c_device();
    
    /* Collect temperature data */
    ret = collect_temperature_data();
    if (ret < 0) {
        LOG_ERR("Temperature data collection failed: %d", ret);
    }
    
    LOG_INF("Data collection completed, preparing for sleep");
    LOG_INF("Next wake up in %d seconds", DATA_COLLECTION_INTERVAL_S);
}

/**
 * @brief Main function
 */
int main(void)
{
    int ret;
    
    LOG_INF("\n=== SW2600 Temperature + Low Power Application ===");
    LOG_INF("Features:");
    LOG_INF("  - M117 temperature sensor (verified working)");
    LOG_INF("  - %d second data collection cycle", DATA_COLLECTION_INTERVAL_S);
    LOG_INF("  - Low power mode (target: 3.5µA)");
    LOG_INF("");
    
    /* Initialize I2C device reference */
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    if (!i2c_dev || !device_is_ready(i2c_dev)) {
        LOG_ERR("I2C device not ready");
    } else {
        LOG_INF("I2C device ready");
    }
    
    /* Initialize temperature sensor */
    ret = init_temperature_sensor();
    if (ret < 0) {
        LOG_ERR("Temperature sensor initialization failed: %d", ret);
        LOG_ERR("Continuing with power test only...");
    }
    
    LOG_INF("=== SW2600 Application Started Successfully ===");
    
    /* Main application loop */
    while (1) {
        /* Perform data collection cycle */
        data_collection_cycle();
        
        /* Countdown before sleep */
        for (int i = COUNTDOWN_DELAY_S; i > 0; i--) {
            LOG_INF("Deep sleep countdown: %d seconds remaining", i);
            k_sleep(K_SECONDS(1));
        }
        
        /* Enter deep sleep mode */
        enter_deep_sleep();
        
        /* Should not reach here - system will reset after sleep */
        LOG_ERR("ERROR: Should not reach here!");
        k_sleep(K_SECONDS(10));
    }
    
    return 0;
}
