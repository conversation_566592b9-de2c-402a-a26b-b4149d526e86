/*
 * SW2600 Temperature + Low Power
 * 
 * Features:
 * - M117 temperature sensor (verified working)
 * - Low power mode with 3.5µA sleep current
 * - 1-minute wake cycle for temperature measurement
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/pm/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/drivers/gpio.h>
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(sw2600_debug, CONFIG_LOG_DEFAULT_LEVEL);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* Configuration */
#define DATA_COLLECTION_INTERVAL_S  60      // 1 minute data collection cycle
#define TEMPERATURE_READ_COUNT      3       // Number of temperature readings per cycle
#define COUNTDOWN_DELAY_S           5       // Countdown before sleep

/* Device references */
static const struct device *i2c_dev;

/* Global state */
static bool m117_initialized = false;

/**
 * @brief Initialize M117 temperature sensor
 */
static int init_temperature_sensor(void)
{
    int ret;

    ret = m117_init(M117_MPS_1, M117_REPEAT_HIGH);
    if (ret < 0) {
        return ret;
    }

    m117_initialized = true;

    return 0;
}

/**
 * @brief Collect temperature data
 */
static int collect_temperature_data(void)
{
    int ret;
    float temperature;
    float temp_sum = 0.0f;
    int valid_readings = 0;

    if (!m117_initialized) {
        LOG_ERR("M117 not initialized");
        return -ENODEV;
    }

    LOG_INF("Collecting temperature data...");

    /* Collect multiple temperature readings */
    for (int i = 0; i < TEMPERATURE_READ_COUNT; i++) {
        ret = m117_measure_temperature(&temperature);
        if (ret == 0) {
            temp_sum += temperature;
            valid_readings++;
        }

        /* Small delay between readings */
        k_sleep(K_MSEC(100));
    }

    /* Return success if we got any valid readings */
    if (valid_readings > 0) {
        float avg_temp = temp_sum / valid_readings;
        LOG_INF("Temp: %.2f°C (%d readings)", (double)avg_temp, valid_readings);
        return 0;
    } else {
        LOG_ERR("No valid temperature readings");
        return -EIO;
    }
}

/**
 * @brief Resume I2C device after wake up
 */
static int resume_i2c_device(void)
{
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));

    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
        if (ret < 0 && ret != -EALREADY) {
            return ret;
        }
    }

    return 0;
}

/**
 * @brief Suspend devices for power saving
 */
static void suspend_devices(void)
{
    /* Suspend I2C */
    if (i2c_dev && device_is_ready(i2c_dev)) {
        pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
    }
}

/**
 * @brief Enter deep sleep mode
 */
/**
 * @brief Configure GPIO for wake-up
 */
static int configure_wakeup_gpio(void)
{
    int ret;

    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button GPIO not ready");
        return -ENODEV;
    }

    /* Configure button as input with pull-up */
    ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    /* Configure interrupt for wake-up */
    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured (press button to wake)");
    return 0;
}

static void enter_deep_sleep(void)
{
    LOG_INF("Entering deep sleep mode...");
    LOG_INF("Expected: ~3.5µA sleep current");
    LOG_INF("Press button to wake up");

    /* Small delay for UART output */
    k_msleep(50);

    /* Suspend devices */
    suspend_devices();

    /* Enter system off mode */
    sys_poweroff();
}

/**
 * @brief Main data collection and power management cycle
 */
static void data_collection_cycle(void)
{
    /* Resume I2C device */
    resume_i2c_device();

    /* Collect temperature data */
    collect_temperature_data();
}

/**
 * @brief Main function
 */
int main(void)
{
    int ret;

    printk("\n\n=== SW2600 MAIN STARTED ===\n");
    printk("Application: Temperature + Low Power\n");
    printk("Target: 3.5µA sleep current\n");

    LOG_INF("=== SW2600 Temperature + Low Power Debug ===");
    LOG_INF("Target: 3.5µA sleep current");

    /* Initialize I2C device reference */
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    if (!i2c_dev || !device_is_ready(i2c_dev)) {
        LOG_ERR("I2C device not ready");
    } else {
        LOG_INF("I2C device ready");
    }

    /* Initialize temperature sensor */
    ret = init_temperature_sensor();
    if (ret < 0) {
        LOG_ERR("M117 init failed: %d", ret);
    } else {
        LOG_INF("M117 initialized successfully");
    }

    /* Configure wake-up GPIO */
    ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }

    LOG_INF("Starting main loop...");

    /* Main application loop */
    while (1) {
        LOG_INF("=== Wake up cycle ===");

        /* Perform data collection cycle */
        data_collection_cycle();

        /* Brief delay before sleep */
        LOG_INF("Sleep countdown: %d seconds", COUNTDOWN_DELAY_S);
        k_sleep(K_SECONDS(COUNTDOWN_DELAY_S));

        /* Enter deep sleep mode */
        enter_deep_sleep();

        /* Should not reach here - system will reset after sleep */
        LOG_ERR("ERROR: Should not reach here!");
        k_sleep(K_SECONDS(10));
    }

    return 0;
}
