/*
 * SW2600 Temperature + Low Power
 * 
 * Features:
 * - M117 temperature sensor (verified working)
 * - Low power mode with 3.5µA sleep current
 * - 1-minute wake cycle for temperature measurement
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/pm/device.h>
#include "sensors/m117_sensor.h"

/* No logging for ultra low power mode */

/* Configuration */
#define DATA_COLLECTION_INTERVAL_S  60      // 1 minute data collection cycle
#define TEMPERATURE_READ_COUNT      3       // Number of temperature readings per cycle
#define COUNTDOWN_DELAY_S           5       // Countdown before sleep

/* Device references */
static const struct device *i2c_dev;

/* Global state */
static bool m117_initialized = false;

/**
 * @brief Initialize M117 temperature sensor
 */
static int init_temperature_sensor(void)
{
    int ret;

    ret = m117_init(M117_MPS_1, M117_REPEAT_HIGH);
    if (ret < 0) {
        return ret;
    }

    m117_initialized = true;

    return 0;
}

/**
 * @brief Collect temperature data
 */
static int collect_temperature_data(void)
{
    int ret;
    float temperature;
    float temp_sum = 0.0f;
    int valid_readings = 0;

    if (!m117_initialized) {
        return -ENODEV;
    }

    /* Collect multiple temperature readings */
    for (int i = 0; i < TEMPERATURE_READ_COUNT; i++) {
        ret = m117_measure_temperature(&temperature);
        if (ret == 0) {
            temp_sum += temperature;
            valid_readings++;
        }

        /* Small delay between readings */
        k_sleep(K_MSEC(100));
    }

    /* Return success if we got any valid readings */
    if (valid_readings > 0) {
        return 0;
    } else {
        return -EIO;
    }
}

/**
 * @brief Resume I2C device after wake up
 */
static int resume_i2c_device(void)
{
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));

    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
        if (ret < 0 && ret != -EALREADY) {
            return ret;
        }
    }

    return 0;
}

/**
 * @brief Suspend devices for power saving
 */
static void suspend_devices(void)
{
    /* Suspend I2C */
    if (i2c_dev && device_is_ready(i2c_dev)) {
        pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
    }
}

/**
 * @brief Enter deep sleep mode
 */
static void enter_deep_sleep(void)
{
    /* Suspend devices */
    suspend_devices();

    /* Enter system off mode */
    sys_poweroff();
}

/**
 * @brief Main data collection and power management cycle
 */
static void data_collection_cycle(void)
{
    /* Resume I2C device */
    resume_i2c_device();

    /* Collect temperature data */
    collect_temperature_data();
}

/**
 * @brief Main function
 */
int main(void)
{
    /* Initialize I2C device reference */
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));

    /* Initialize temperature sensor */
    init_temperature_sensor();

    /* Main application loop */
    while (1) {
        /* Perform data collection cycle */
        data_collection_cycle();

        /* Brief delay before sleep */
        k_sleep(K_SECONDS(COUNTDOWN_DELAY_S));

        /* Enter deep sleep mode */
        enter_deep_sleep();

        /* Should not reach here - system will reset after sleep */
        k_sleep(K_SECONDS(10));
    }

    return 0;
}
