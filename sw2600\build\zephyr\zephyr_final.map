Archive member included to satisfy reference by file (symbol)

app/libapp.a(main_temperature_working.c.obj)
                              (--whole-archive)
app/libapp.a(zephyr_i2c_driver.c.obj)
                              (--whole-archive)
app/libapp.a(m117_sensor.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc32c_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc32_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc24_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc16_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc8_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc7_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc4_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(heap.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(printk.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(sem.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(thread_entry.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf_complete.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(assert.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(poweroff.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(dec.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(hex.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(rb.c.obj)  (--whole-archive)
zephyr/libzephyr.a(timeutil.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(bitarray.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(onoff.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(notify.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(configs.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(mem_attr.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(device.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(device_system_managed.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(tracing_none.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(banner.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                              (--whole-archive)
zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
                              (--whole-archive)
zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
                              (--whole-archive)
zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
                              (--whole-archive)
zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                              (--whole-archive)
zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
                              (--whole-archive)
zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                              (--whole-archive)
zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
                              (--whole-archive)
zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                              (--whole-archive)
zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                              (--whole-archive)
zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                              (--whole-archive)
zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                              (--whole-archive)
zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                              (--whole-archive)
zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
                              (--whole-archive)
zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                              (--whole-archive)
modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
                              (--whole-archive)
zephyr/kernel/libkernel.a(busy_wait.c.obj)
                              modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj) (z_impl_k_busy_wait)
zephyr/kernel/libkernel.a(device.c.obj)
                              zephyr/libzephyr.a(device.c.obj) (z_device_get_all_static)
zephyr/kernel/libkernel.a(fatal.c.obj)
                              zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj) (z_fatal_error)
zephyr/kernel/libkernel.a(init.c.obj)
                              zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj) (z_sys_post_kernel)
zephyr/kernel/libkernel.a(init_static.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_init_static)
zephyr/kernel/libkernel.a(mem_slab.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (k_mem_slab_init)
zephyr/kernel/libkernel.a(idle.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (idle)
zephyr/kernel/libkernel.a(mutex.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (z_impl_k_mutex_init)
zephyr/kernel/libkernel.a(sem.c.obj)
                              zephyr/libzephyr.a(sem.c.obj) (z_impl_k_sem_init)
zephyr/kernel/libkernel.a(thread.c.obj)
                              zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj) (k_is_in_isr)
zephyr/kernel/libkernel.a(sched.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (_thread_dummy)
zephyr/kernel/libkernel.a(timeslicing.c.obj)
                              zephyr/kernel/libkernel.a(sched.c.obj) (z_reset_time_slice)
zephyr/kernel/libkernel.a(xip.c.obj)
                              zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj) (z_data_copy)
zephyr/kernel/libkernel.a(timeout.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_add_timeout)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                              modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj) (nrf_cc3xx_platform_init_no_rng)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (platform_abort_apis)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (nrf_cc3xx_platform_set_mutexes)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj) (CC_LibInitNoRng)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (CC_HalInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (CC_PalInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalDmaInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj) (CC_PalWaitInterruptRND)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj) (CC_PalMemCopyPlat)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalMutexCreate)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalPowerSaveModeInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (nrf_cc3xx_platform_ctr_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (nrf_cc3xx_platform_hmac_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_platform_zeroize)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (LLF_RND_RunTrngStartupTest)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_ctr_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_entropy_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj) (cc_mbedtls_hmac_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (RNG_PLAT_SetUserRngParameters)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj) (CC_PalTrngParamGet)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj) (mbedtls_mutex_unlock)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj) (LLF_RND_WaitRngInterrupt)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (mbedtls_hardware_poll)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj) (cc_mbedtls_aes_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (cc_mbedtls_sha256_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj) (mbedtls_sha_process_internal)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (cc_mbedtls_sha256)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj) (SetDataBuffersInfo)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj) (InitHashDrv)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj) (ProcessAesDrv)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj) (kmu_validate_slot_and_size)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj) (write_invalid_chacha20_key)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj) (UtilCmacBuildDataForDerivation)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj) (CC_PalDataBufferAttrGet)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
                              zephyr/kernel/libkernel.a(device.c.obj) (strcmp)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
                              zephyr/libzephyr.a(heap.c.obj) (memcpy)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj) (memmove)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
                              zephyr/libzephyr.a(heap.c.obj) (memset)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
                              zephyr/libzephyr.a(cbprintf_packaged.c.obj) (strlen)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (errno)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj) (memcmp)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (strnlen)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
                              (__l_vfprintf)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
                              (__l_vfscanf)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
                              c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o) (strchr)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
                              c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o) (fgetc)
c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
                              c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o) (ungetc)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
                              zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj) (cmse_check_address_range)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_dmul)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
                              app/libapp.a(m117_sensor.c.obj) (__aeabi_fmul)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_dsub)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_ddiv)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_d2f)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
                              app/libapp.a(main_temperature_working.c.obj) (__aeabi_fadd)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
                              app/libapp.a(main_temperature_working.c.obj) (__aeabi_fdiv)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_fcmpeq)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_ldivmod)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (__aeabi_uldivmod)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
                              zephyr/libzephyr.a(bitarray.c.obj) (__popcountsi2)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_d2lz)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o) (__aeabi_d2ulz)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o) (__udivmoddi4)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o) (__aeabi_ldiv0)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o) (__aeabi_dcmplt)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)

Discarded input sections

 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .debug_line    0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .debug_str     0x0000000000000000      0x1cf zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .comment       0x0000000000000000       0x21 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .text          0x0000000000000000        0x0 app/libapp.a(main_temperature_working.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(main_temperature_working.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(main_temperature_working.c.obj)
 .text          0x0000000000000000        0x0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .text          0x0000000000000000        0x0 app/libapp.a(m117_sensor.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(m117_sensor.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(m117_sensor.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .text.crc32_c  0x0000000000000000       0x48 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .rodata.crc32c_table
                0x0000000000000000       0x40 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_info    0x0000000000000000      0x184 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xcd zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_loc     0x0000000000000000      0x104 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x30 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_line    0x0000000000000000      0x21f zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_str     0x0000000000000000      0x2c1 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_frame   0x0000000000000000       0x2c zephyr/libzephyr.a(crc32c_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc32_sw.c.obj)
 .text.crc32_ieee_update
                0x0000000000000000       0x3c zephyr/libzephyr.a(crc32_sw.c.obj)
 .text.crc32_ieee
                0x0000000000000000        0xa zephyr/libzephyr.a(crc32_sw.c.obj)
 .rodata.table.0
                0x0000000000000000       0x40 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_info    0x0000000000000000      0x1cb zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_abbrev  0x0000000000000000      0x117 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_loc     0x0000000000000000      0x160 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x50 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_line    0x0000000000000000      0x254 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_str     0x0000000000000000      0x2ba zephyr/libzephyr.a(crc32_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_frame   0x0000000000000000       0x3c zephyr/libzephyr.a(crc32_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc24_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc24_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc24_sw.c.obj)
 .text.crc24_pgp_update
                0x0000000000000000       0x2c zephyr/libzephyr.a(crc24_sw.c.obj)
 .text.crc24_pgp
                0x0000000000000000       0x18 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_info    0x0000000000000000      0x183 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xd6 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_loc     0x0000000000000000      0x137 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_line    0x0000000000000000      0x208 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_str     0x0000000000000000      0x2ad zephyr/libzephyr.a(crc24_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_frame   0x0000000000000000       0x40 zephyr/libzephyr.a(crc24_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16    0x0000000000000000       0x30 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16_reflect
                0x0000000000000000       0x2a zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16_ccitt
                0x0000000000000000       0x26 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16_itu_t
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_info    0x0000000000000000      0x2d8 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xc8 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_loc     0x0000000000000000      0x3d0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x28 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_line    0x0000000000000000      0x390 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_str     0x0000000000000000      0x2c3 zephyr/libzephyr.a(crc16_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_frame   0x0000000000000000       0x70 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc8_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc8_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc8_sw.c.obj)
 .text.crc8_ccitt
                0x0000000000000000       0x2c zephyr/libzephyr.a(crc8_sw.c.obj)
 .text.crc8     0x0000000000000000       0x54 zephyr/libzephyr.a(crc8_sw.c.obj)
 .rodata.crc8_ccitt_small_table
                0x0000000000000000       0x10 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_info    0x0000000000000000      0x203 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xdc zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_loc     0x0000000000000000      0x26e zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_line    0x0000000000000000      0x2d3 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_str     0x0000000000000000      0x2c7 zephyr/libzephyr.a(crc8_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_frame   0x0000000000000000       0x48 zephyr/libzephyr.a(crc8_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .text.crc7_be  0x0000000000000000       0x20 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_info    0x0000000000000000      0x127 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xac zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_loc     0x0000000000000000       0xc0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_line    0x0000000000000000      0x1c6 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_str     0x0000000000000000      0x285 zephyr/libzephyr.a(crc7_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/libzephyr.a(crc7_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc4_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc4_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc4_sw.c.obj)
 .text.crc4     0x0000000000000000       0x8a zephyr/libzephyr.a(crc4_sw.c.obj)
 .text.crc4_ti  0x0000000000000000       0x40 zephyr/libzephyr.a(crc4_sw.c.obj)
 .rodata.lookup.0
                0x0000000000000000        0x8 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_info    0x0000000000000000      0x22e zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xf2 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_loc     0x0000000000000000      0x386 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x48 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_line    0x0000000000000000      0x30e zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_str     0x0000000000000000      0x2bf zephyr/libzephyr.a(crc4_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_frame   0x0000000000000000       0x4c zephyr/libzephyr.a(crc4_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .text.mem_to_chunkid
                0x0000000000000000       0x14 zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_remove_bidx
                0x0000000000000000       0x5c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_remove
                0x0000000000000000       0x2e zephyr/libzephyr.a(heap.c.obj)
 .text.alloc_chunk
                0x0000000000000000       0x74 zephyr/libzephyr.a(heap.c.obj)
 .text.split_chunks
                0x0000000000000000       0x4e zephyr/libzephyr.a(heap.c.obj)
 .text.merge_chunks
                0x0000000000000000       0x3c zephyr/libzephyr.a(heap.c.obj)
 .text.free_chunk
                0x0000000000000000       0x84 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_free
                0x0000000000000000       0x24 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_usable_size
                0x0000000000000000       0x22 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_alloc
                0x0000000000000000       0x6e zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_aligned_alloc
                0x0000000000000000       0xe8 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_aligned_realloc
                0x0000000000000000      0x144 zephyr/libzephyr.a(heap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbvprintf_package
                0x0000000000000000      0x3a4 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbprintf_package
                0x0000000000000000       0x1e zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbpprintf_external
                0x0000000000000000       0x62 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.is_ptr   0x0000000000000000       0x4a zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbprintf_package_convert
                0x0000000000000000      0x330 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_info    0x0000000000000000     0x1438 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_abbrev  0x0000000000000000      0x4bf zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_loc     0x0000000000000000     0x1ff9 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_aranges
                0x0000000000000000       0x40 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_ranges  0x0000000000000000      0x260 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_line    0x0000000000000000      0xfc6 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_str     0x0000000000000000      0x782 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_frame   0x0000000000000000       0xec zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .text.__printk_get_hook
                0x0000000000000000        0xc zephyr/libzephyr.a(printk.c.obj)
 .text.z_impl_k_str_out
                0x0000000000000000       0x1c zephyr/libzephyr.a(printk.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_init
                0x0000000000000000        0xa zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_give
                0x0000000000000000        0xa zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_take
                0x0000000000000000       0x1e zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_count_get
                0x0000000000000000        0x4 zephyr/libzephyr.a(sem.c.obj)
 .debug_info    0x0000000000000000      0x4dd zephyr/libzephyr.a(sem.c.obj)
 .debug_abbrev  0x0000000000000000      0x22a zephyr/libzephyr.a(sem.c.obj)
 .debug_loc     0x0000000000000000      0x21a zephyr/libzephyr.a(sem.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(sem.c.obj)
 .debug_ranges  0x0000000000000000       0x70 zephyr/libzephyr.a(sem.c.obj)
 .debug_line    0x0000000000000000      0x31e zephyr/libzephyr.a(sem.c.obj)
 .debug_str     0x0000000000000000      0x3cd zephyr/libzephyr.a(sem.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(sem.c.obj)
 .debug_frame   0x0000000000000000       0x68 zephyr/libzephyr.a(sem.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.encode_uint
                0x0000000000000000       0x90 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.outs     0x0000000000000000       0x2e zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .rodata.z_cbvprintf_impl.str1.1
                0x0000000000000000        0x6 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.z_cbvprintf_impl
                0x0000000000000000      0x758 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_info    0x0000000000000000     0x10a3 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_abbrev  0x0000000000000000      0x3f7 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_loc     0x0000000000000000     0x12db zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_aranges
                0x0000000000000000       0x30 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_ranges  0x0000000000000000      0x1c8 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_line    0x0000000000000000      0xd0a zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_str     0x0000000000000000      0x697 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_frame   0x0000000000000000       0x88 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .text.assert_post_action
                0x0000000000000000       0x12 zephyr/libzephyr.a(assert.c.obj)
 .text.assert_print
                0x0000000000000000       0x1a zephyr/libzephyr.a(assert.c.obj)
 .debug_info    0x0000000000000000      0x247 zephyr/libzephyr.a(assert.c.obj)
 .debug_abbrev  0x0000000000000000      0x1ca zephyr/libzephyr.a(assert.c.obj)
 .debug_loc     0x0000000000000000       0x51 zephyr/libzephyr.a(assert.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(assert.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(assert.c.obj)
 .debug_line    0x0000000000000000      0x312 zephyr/libzephyr.a(assert.c.obj)
 .debug_str     0x0000000000000000      0x387 zephyr/libzephyr.a(assert.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(assert.c.obj)
 .debug_frame   0x0000000000000000       0x50 zephyr/libzephyr.a(assert.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(poweroff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(poweroff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(poweroff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .text.u8_to_dec
                0x0000000000000000       0x52 zephyr/libzephyr.a(dec.c.obj)
 .debug_info    0x0000000000000000      0x12b zephyr/libzephyr.a(dec.c.obj)
 .debug_abbrev  0x0000000000000000       0x9c zephyr/libzephyr.a(dec.c.obj)
 .debug_loc     0x0000000000000000      0x128 zephyr/libzephyr.a(dec.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(dec.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/libzephyr.a(dec.c.obj)
 .debug_line    0x0000000000000000      0x1c2 zephyr/libzephyr.a(dec.c.obj)
 .debug_str     0x0000000000000000      0x2a4 zephyr/libzephyr.a(dec.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(dec.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/libzephyr.a(dec.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .text.char2hex
                0x0000000000000000       0x30 zephyr/libzephyr.a(hex.c.obj)
 .text.hex2char
                0x0000000000000000       0x1c zephyr/libzephyr.a(hex.c.obj)
 .text.bin2hex  0x0000000000000000       0x54 zephyr/libzephyr.a(hex.c.obj)
 .text.hex2bin  0x0000000000000000       0x70 zephyr/libzephyr.a(hex.c.obj)
 .debug_info    0x0000000000000000      0x29d zephyr/libzephyr.a(hex.c.obj)
 .debug_abbrev  0x0000000000000000      0x117 zephyr/libzephyr.a(hex.c.obj)
 .debug_loc     0x0000000000000000      0x2f4 zephyr/libzephyr.a(hex.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(hex.c.obj)
 .debug_ranges  0x0000000000000000       0x40 zephyr/libzephyr.a(hex.c.obj)
 .debug_line    0x0000000000000000      0x31d zephyr/libzephyr.a(hex.c.obj)
 .debug_str     0x0000000000000000      0x2ab zephyr/libzephyr.a(hex.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(hex.c.obj)
 .debug_frame   0x0000000000000000       0x74 zephyr/libzephyr.a(hex.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .text.find_and_stack
                0x0000000000000000       0x3a zephyr/libzephyr.a(rb.c.obj)
 .text.stack_left_limb
                0x0000000000000000       0x40 zephyr/libzephyr.a(rb.c.obj)
 .text.set_child
                0x0000000000000000       0x12 zephyr/libzephyr.a(rb.c.obj)
 .text.rotate   0x0000000000000000       0xa8 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_get_minmax
                0x0000000000000000       0x1a zephyr/libzephyr.a(rb.c.obj)
 .text.rb_insert
                0x0000000000000000      0x134 zephyr/libzephyr.a(rb.c.obj)
 .text.rb_remove
                0x0000000000000000      0x344 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_walk
                0x0000000000000000       0x24 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_child
                0x0000000000000000        0xe zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_is_black
                0x0000000000000000        0x8 zephyr/libzephyr.a(rb.c.obj)
 .text.rb_contains
                0x0000000000000000       0x2c zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_foreach_next
                0x0000000000000000       0x56 zephyr/libzephyr.a(rb.c.obj)
 .debug_info    0x0000000000000000     0x24dc zephyr/libzephyr.a(rb.c.obj)
 .debug_abbrev  0x0000000000000000      0x491 zephyr/libzephyr.a(rb.c.obj)
 .debug_loc     0x0000000000000000     0x2796 zephyr/libzephyr.a(rb.c.obj)
 .debug_aranges
                0x0000000000000000       0x78 zephyr/libzephyr.a(rb.c.obj)
 .debug_ranges  0x0000000000000000      0x5d8 zephyr/libzephyr.a(rb.c.obj)
 .debug_line    0x0000000000000000     0x1383 zephyr/libzephyr.a(rb.c.obj)
 .debug_str     0x0000000000000000      0x49b zephyr/libzephyr.a(rb.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(rb.c.obj)
 .debug_frame   0x0000000000000000      0x17c zephyr/libzephyr.a(rb.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_timegm64
                0x0000000000000000       0xe8 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_timegm
                0x0000000000000000       0x20 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_state_update
                0x0000000000000000       0x5c zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_state_set_skew
                0x0000000000000000       0x3a zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_estimate_skew
                0x0000000000000000       0x96 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_ref_from_local
                0x0000000000000000       0xae zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_local_from_ref
                0x0000000000000000       0xa8 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_skew_to_ppb
                0x0000000000000000       0x3c zephyr/libzephyr.a(timeutil.c.obj)
 .debug_info    0x0000000000000000      0x8d5 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_abbrev  0x0000000000000000      0x1f1 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_loc     0x0000000000000000      0x738 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_aranges
                0x0000000000000000       0x58 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_ranges  0x0000000000000000       0xc0 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_line    0x0000000000000000      0x61e zephyr/libzephyr.a(timeutil.c.obj)
 .debug_str     0x0000000000000000      0x574 zephyr/libzephyr.a(timeutil.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_frame   0x0000000000000000      0x11c zephyr/libzephyr.a(timeutil.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .text.setup_bundle_data.constprop.0
                0x0000000000000000       0x3a zephyr/libzephyr.a(bitarray.c.obj)
 .text.set_region
                0x0000000000000000       0x90 zephyr/libzephyr.a(bitarray.c.obj)
 .text.set_clear_region
                0x0000000000000000       0x46 zephyr/libzephyr.a(bitarray.c.obj)
 .text.match_region
                0x0000000000000000       0x8c zephyr/libzephyr.a(bitarray.c.obj)
 .text.is_region_set_clear
                0x0000000000000000       0x4e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_popcount_region
                0x0000000000000000       0x94 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_xor
                0x0000000000000000       0xc2 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_set_bit
                0x0000000000000000       0x3e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_clear_bit
                0x0000000000000000       0x40 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_bit
                0x0000000000000000       0x3e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_set_bit
                0x0000000000000000       0x4e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_clear_bit
                0x0000000000000000       0x50 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_alloc
                0x0000000000000000       0xa4 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_find_nth_set
                0x0000000000000000       0xba zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_free
                0x0000000000000000       0x6c zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_is_region_set
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_is_region_cleared
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_set_region
                0x0000000000000000       0x70 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_set_region
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_clear_region
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_info    0x0000000000000000     0x2562 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_abbrev  0x0000000000000000      0x476 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_loc     0x0000000000000000     0x1e0f zephyr/libzephyr.a(bitarray.c.obj)
 .debug_aranges
                0x0000000000000000       0xb8 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_ranges  0x0000000000000000      0x288 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_line    0x0000000000000000     0x17a6 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_str     0x0000000000000000      0x67b zephyr/libzephyr.a(bitarray.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_frame   0x0000000000000000      0x290 zephyr/libzephyr.a(bitarray.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .text.sys_slist_find_and_remove
                0x0000000000000000       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_release
                0x0000000000000000       0x48 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_reset
                0x0000000000000000       0x62 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_cancel
                0x0000000000000000       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_monitor_register
                0x0000000000000000       0x3a zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_monitor_unregister
                0x0000000000000000       0x34 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_sync_lock
                0x0000000000000000       0x14 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_sync_finalize
                0x0000000000000000       0x40 zephyr/libzephyr.a(onoff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .text.mem_attr_check_buf
                0x0000000000000000        0xe zephyr/libzephyr.a(mem_attr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(device.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(device.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(device.c.obj)
 .rodata.pm_device_state_str.str1.1
                0x0000000000000000        0x1 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_state_str
                0x0000000000000000       0x18 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_power_domain_remove
                0x0000000000000000        0x6 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_power_domain_add
                0x0000000000000000        0x6 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_is_any_busy
                0x0000000000000000       0x34 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_is_busy
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_busy_set
                0x0000000000000000       0x16 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_busy_clear
                0x0000000000000000       0x16 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_wakeup_enable
                0x0000000000000000       0x36 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_wakeup_is_enabled
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_wakeup_is_capable
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_on_power_domain
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .rodata.str1.1
                0x0000000000000000       0x15 zephyr/libzephyr.a(device.c.obj)
 .rodata.CSWTCH.23
                0x0000000000000000       0x10 zephyr/libzephyr.a(device.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(device_system_managed.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(device_system_managed.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(device_system_managed.c.obj)
 .text.pm_resume_devices
                0x0000000000000000        0x2 zephyr/libzephyr.a(device_system_managed.c.obj)
 .text.pm_suspend_devices
                0x0000000000000000        0x4 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_info    0x0000000000000000      0x164 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_abbrev  0x0000000000000000       0xb2 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_line    0x0000000000000000      0x196 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_str     0x0000000000000000      0x346 zephyr/libzephyr.a(device_system_managed.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(device_system_managed.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/libzephyr.a(device_system_managed.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_enter
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_exit
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_exit_to_scheduler
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_idle
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_info    0x0000000000000000       0x56 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_abbrev  0x0000000000000000       0x48 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_ranges  0x0000000000000000       0x28 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_line    0x0000000000000000       0x9f zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_str     0x0000000000000000      0x231 zephyr/libzephyr.a(tracing_none.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_frame   0x0000000000000000       0x50 zephyr/libzephyr.a(tracing_none.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_psa_rngdata_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_psa_globaldata_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_key_slot_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.heap_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.psa_rng_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.psa_globaldata_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.key_slot_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.heap_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .text.z_get_sw_isr_table_idx
                0x0000000000000000        0x2 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_info    0x0000000000000000       0xba zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_abbrev  0x0000000000000000       0x6b zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_loc     0x0000000000000000       0x15 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_line    0x0000000000000000       0x77 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_str     0x0000000000000000      0x295 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .text.arch_syscall_oops
                0x0000000000000000       0x1c zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text.z_arm_save_fp_context
                0x0000000000000000        0x2 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text.z_arm_restore_fp_context
                0x0000000000000000        0x2 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_info    0x0000000000000000      0x146 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_abbrev  0x0000000000000000       0xcd zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_line    0x0000000000000000      0x18b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_str     0x0000000000000000      0x2f6 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .text.sys_arch_reboot
                0x0000000000000000       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .text.arch_irq_disable
                0x0000000000000000       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text.arch_irq_lock_outlined
                0x0000000000000000       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_read_ok
                0x0000000000000000       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_readwrite_ok
                0x0000000000000000       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_range_read_ok
                0x0000000000000000       0x1a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_range_readwrite_ok
                0x0000000000000000       0x1a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .text.z_arm_configure_dynamic_mpu_regions
                0x0000000000000000        0xc zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .bss.dynamic_regions.0
                0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.arm_core_mpu_configure_dynamic_mpu_regions
                0x0000000000000000       0x4c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.cbputc   0x0000000000000000        0xc zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__stdin_hook_install
                0x0000000000000000       0x14 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.cbvprintf
                0x0000000000000000       0x38 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .rodata._exit.str1.1
                0x0000000000000000        0x6 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text._exit    0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_init_recursive
                0x0000000000000000       0x14 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_init
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_close_recursive
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_close
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_acquire_recursive
                0x0000000000000000        0xc zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_acquire
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_try_acquire_recursive
                0x0000000000000000       0x12 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_try_acquire
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_release_recursive
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__retarget_lock_release
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__assert_no_args
                0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .rodata.__chk_fail.str1.1
                0x0000000000000000       0x1e zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.__chk_fail
                0x0000000000000000       0x1c zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 ._k_mutex.static.__lock___libc_recursive_mutex_
                0x0000000000000000       0x14 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .rodata.stdout
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .rodata.stdin  0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .bss.__stdin   0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .rodata.abort.str1.1
                0x0000000000000000        0x9 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .text.abort    0x0000000000000000       0x1c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_info    0x0000000000000000      0x1bb zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_abbrev  0x0000000000000000      0x14d zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_loc     0x0000000000000000       0x2c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_line    0x0000000000000000      0x2e3 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_str     0x0000000000000000      0x366 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_frame   0x0000000000000000       0x28 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.malloc_lock
                0x0000000000000000       0x14 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.malloc_unlock
                0x0000000000000000        0xc zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.malloc   0x0000000000000000       0x34 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.aligned_alloc
                0x0000000000000000       0x34 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.realloc  0x0000000000000000       0x38 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.free     0x0000000000000000       0x1c zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.calloc   0x0000000000000000       0x34 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.reallocarray
                0x0000000000000000       0x24 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .data.z_malloc_heap_mutex
                0x0000000000000000       0x14 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_info    0x0000000000000000       0x79 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_line    0x0000000000000000       0x61 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_str     0x0000000000000000      0x27d zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_info    0x0000000000000000       0x64 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_line    0x0000000000000000       0x62 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_str     0x0000000000000000      0x267 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_info    0x0000000000000000       0x79 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_line    0x0000000000000000       0x64 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_str     0x0000000000000000      0x280 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.nrf53_cpunet_enable
                0x0000000000000000       0x34 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_control_get_onoff
                0x0000000000000000       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_bt_ctlr_hf_request
                0x0000000000000000       0x24 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_bt_ctlr_hf_release
                0x0000000000000000       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text.uart_register_input
                0x0000000000000000        0x2 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .text.i2c_dump_msgs_rw
                0x0000000000000000        0x2 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_info    0x0000000000000000      0x42f zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_abbrev  0x0000000000000000      0x1a8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_line    0x0000000000000000      0x206 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_str     0x0000000000000000      0x489 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text.sys_clock_set_timeout
                0x0000000000000000        0x2 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text.sys_clock_idle_exit
                0x0000000000000000        0x2 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_info    0x0000000000000000       0xda zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_abbrev  0x0000000000000000       0x7e zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_line    0x0000000000000000      0x146 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_str     0x0000000000000000      0x2be zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_evt_address_get
                0x0000000000000000       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_capture_task_address_get
                0x0000000000000000       0x14 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_int_lock
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_int_unlock
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_read
                0x0000000000000000       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_abort
                0x0000000000000000       0x58 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_get_ticks
                0x0000000000000000       0x78 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_set
                0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_exact_set
                0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_chan_alloc
                0x0000000000000000       0x3c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_chan_free
                0x0000000000000000       0x1c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_trigger_overflow
                0x0000000000000000        0x6 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_cycle_get_32
                0x0000000000000000        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_disable
                0x0000000000000000       0x30 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.alloc_mask
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text          0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .data          0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .bss           0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemCoreClockUpdate
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemStoreFICRNS
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemLockFICRNS
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .rodata.nrfx_error_string_get.str1.1
                0x0000000000000000      0x178 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .text.nrfx_error_string_get
                0x0000000000000000       0xd8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .text.nrfx_flag32_is_allocated
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_check
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_disable_all
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_disable
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_trigger
                0x0000000000000000        0xe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_endpoints_clear
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_event_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_fork_endpoint_setup
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_fork_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_group_set
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_include_in_group
                0x0000000000000000       0x16 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_remove_from_group
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_clear
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_enable
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_disable
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_address_get
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_disable_task_get
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_enable_task_get
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_free
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_alloc
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_free
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_edge_connection_setup
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrf53_errata_4
                0x0000000000000000       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_disable
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_uninit
                0x0000000000000000       0x28 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_init_check
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_divider_set
                0x0000000000000000       0xdc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .rodata.CSWTCH.41
                0x0000000000000000        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_free
                0x0000000000000000       0x60 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_free
                0x0000000000000000       0x1c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_enable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_disable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_alloc
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_free
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_include_in_group
                0x0000000000000000       0x70 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_remove_from_group
                0x0000000000000000       0x70 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_clear
                0x0000000000000000       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_enable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_disable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_in_event_get.isra.0
                0x0000000000000000       0x22 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_is_set
                0x0000000000000000       0x1a modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_channels_number_get
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_uninit
                0x0000000000000000       0x6c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_set
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_clear
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_toggle
                0x0000000000000000       0x22 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_enable
                0x0000000000000000       0x1e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_disable
                0x0000000000000000       0x1e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_get
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_address_get
                0x0000000000000000       0x12 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_get
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_address_get
                0x0000000000000000       0x16 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_get
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_address_get
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_force
                0x0000000000000000       0x2a modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_trigger
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_trigger
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_trigger
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_event_get
                0x0000000000000000        0x6 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_event_address_get
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.port_lens.0
                0x0000000000000000        0x2 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_reconfigure
                0x0000000000000000       0x64 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_init_check
                0x0000000000000000       0x1c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_uninit
                0x0000000000000000       0x5c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_is_busy
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_start_task_address_get
                0x0000000000000000        0xe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_stopped_event_address_get
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .text.z_device_get_all_static
                0x0000000000000000       0x18 zephyr/kernel/libkernel.a(device.c.obj)
 .text.z_impl_device_get_binding
                0x0000000000000000       0x54 zephyr/kernel/libkernel.a(device.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .text.k_fatal_halt
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(fatal.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_impl_device_init
                0x0000000000000000       0x28 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_early_rand_get
                0x0000000000000000       0x70 zephyr/kernel/libkernel.a(init.c.obj)
 .data.state.1  0x0000000000000000        0x8 zephyr/kernel/libkernel.a(init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_runtime_stats_get
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .text.arch_spin_relax
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(idle.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .text.z_impl_k_sem_reset
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(sem.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_is_preempt_thread
                0x0000000000000000       0x20 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_priority_get
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_name_set
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_name_get
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_name_copy
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.k_thread_state_str.str1.1
                0x0000000000000000        0x3 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_state_str
                0x0000000000000000       0x90 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_create
                0x0000000000000000       0x58 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_init_thread_base
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_user_mode_enter
                0x0000000000000000       0x1c zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_get
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_all_get
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_cpu_get
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.str1.1
                0x0000000000000000       0x41 zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.state_string.0
                0x0000000000000000       0x40 zephyr/kernel/libkernel.a(thread.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_prio_cmp
                0x0000000000000000       0x12 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_requeue_current
                0x0000000000000000       0x50 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_ready_thread_locked
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_move_thread_to_end_of_prio_q
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_pend_thread
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_thread_no_timeout
                0x0000000000000000       0x22 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend1_no_timeout
                0x0000000000000000       0x2c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_thread
                0x0000000000000000       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_resume
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_swap_next_thread
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_all
                0x0000000000000000       0x26 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.init_ready_q
                0x0000000000000000        0x8 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_priority_set
                0x0000000000000000       0x2c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.k_can_yield
                0x0000000000000000       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_usleep
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_wakeup
                0x0000000000000000       0x58 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_join
                0x0000000000000000       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_wake
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_wait
                0x0000000000000000       0x24 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_waitq_walk
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .text.k_sched_time_slice_set
                0x0000000000000000       0x54 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.timeout_rem
                0x0000000000000000       0x34 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_timeout_remaining
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_timeout_expires
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_get_next_timeout_expiry
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_impl_k_uptime_ticks
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.sys_timepoint_calc
                0x0000000000000000       0x48 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.sys_timepoint_timeout
                0x0000000000000000       0x42 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init_hmac_drbg
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_deinit
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_is_initialized
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_rng_is_initialized
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_nonce_seed
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_boot_seed
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss.nrf_cc3xx_platform_rng_initialized
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .rodata.RndStartupTest.constprop.0.str1.4
                0x0000000000000000       0x6f C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.RndStartupTest.constprop.0
                0x0000000000000000       0xac C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInit
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInit_HMAC_DRBG
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_RandomSeedsFilled
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibFini
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInitRngModule
                0x0000000000000000       0xbc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.rndWorkbuff.0
                0x0000000000000000      0x220 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.random_seed_filled
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.tfm_random_seed_buff
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.eits_random_nonce_buff
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.invalid_chacha_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.invalid_aes_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.random_seed_buffer
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalTerminate
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBit
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBitRNG
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalMaskInterrupt
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalWaitInterrupt
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalWaitInterruptRND
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .data.CCApbFilteringRegMutex
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text.CC_PalWaitInterruptRND
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text.CC_PalWaitInterrupt
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalSecMemCmp
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCmpPlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCopyPlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemMovePlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemSetPlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemSetZeroPlat
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text.CC_PalMutexLock
                0x0000000000000000       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text.CC_PalMutexUnlock
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeStatus
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeSelect
                0x0000000000000000       0x84 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_init
                0x0000000000000000       0x60 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_free
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_init
                0x0000000000000000       0x60 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_free
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss.global_ctx
                0x0000000000000000      0x250 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text.mbedtls_zeroize_internal
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text.cc_mbedtls_platform_zeroize
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.startTrngHW
                0x0000000000000000      0x130 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_StartTrngHW
                0x0000000000000000      0x10c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RepetitionCounterTest
                0x0000000000000000       0x64 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_AdaptiveProportionTest
                0x0000000000000000       0x80 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.getTrngSource
                0x0000000000000000      0x2a8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_GetTrngSource
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RunTrngStartupTest
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.block_cipher_df
                0x0000000000000000      0x1d0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.ctr_drbg_update_internal
                0x0000000000000000      0x174 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.mbedtls_ctr_drbg_reseed_internal
                0x0000000000000000       0xc4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_init
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_free
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_prediction_resistance
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_entropy_len
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_nonce_len
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_reseed_interval
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_update
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_reseed
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_seed
                0x0000000000000000       0xa0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random_with_add
                0x0000000000000000      0x1e4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss.buf.0     0x0000000000000000      0x1a0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss.seed.1    0x0000000000000000      0x180 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.entropy_update
                0x0000000000000000       0x7c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.entropy_gather_internal.part.0
                0x0000000000000000       0x88 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_init
                0x0000000000000000       0x84 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_free
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_add_source
                0x0000000000000000       0x64 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_update_manual
                0x0000000000000000       0x4c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_gather
                0x0000000000000000       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_func
                0x0000000000000000      0x110 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_sha256_starts.constprop.0
                0x0000000000000000      0x108 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_init
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_update
                0x0000000000000000      0x170 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_drbg_reseed_core
                0x0000000000000000       0xac C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed_buf
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_reseed
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed
                0x0000000000000000       0x54 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_prediction_resistance
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_entropy_len
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_reseed_interval
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random_with_add
                0x0000000000000000      0x144 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random
                0x0000000000000000       0x4c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_free
                0x0000000000000000       0x40 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .bss.hmac_ctx  0x0000000000000000       0x80 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .text.RNG_PLAT_SetUserRngParameters
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .text.CC_PalTrngParamGet
                0x0000000000000000       0xac C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_init
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_free
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_lock
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_unlock
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .rodata.mbedtls_threading_set_alt.str1.4
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mbedtls_threading_set_alt
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mbedtls_threading_free_alt
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_unlock
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_lock
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_free
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_init
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.Mult32x32
                0x0000000000000000       0x40 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.Mult48x16
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_EntropyEstimateFull
                0x0000000000000000      0x52c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_WaitRngInterrupt
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetRoscSampleCnt
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetFastestRosc
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetCountRoscs
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_TurnOffTrng
                0x0000000000000000       0x24 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_RndCprngt
                0x0000000000000000       0x58 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .rodata.mbedtls_hardware_poll.str1.4
                0x0000000000000000       0x6e C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .text.mbedtls_hardware_poll
                0x0000000000000000      0x104 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.rndState.0
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.trngParams.1
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.rndWorkBuffer.2
                0x0000000000000000      0x220 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .rodata.cc_mbedtls_aes_init.str1.4
                0x0000000000000000       0x13 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_init
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_free
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_enc
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_dec
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ecb
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cbc
                0x0000000000000000       0x78 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb128
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb8
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ctr
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ofb
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_encrypt
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_decrypt
                0x0000000000000000       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_init.str1.4
                0x0000000000000000        0xe C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_init
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_free
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_clone.str1.4
                0x0000000000000000       0x15 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_clone
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_starts
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_internal_sha256_process
                0x0000000000000000       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_update
                0x0000000000000000       0x54 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_finish
                0x0000000000000000       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_process_internal
                0x0000000000000000       0xe0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_starts_internal
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_finish_internal
                0x0000000000000000       0x5c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_update_internal
                0x0000000000000000      0x1ec C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .text.cc_mbedtls_sha256
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .bss.ctx.0     0x0000000000000000       0xf4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .text.SetDataBuffersInfo
                0x0000000000000000       0x64 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.InitHashDrv
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.ProcessHashDrv.str1.4
                0x0000000000000000       0x6f C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.ProcessHashDrv
                0x0000000000000000      0x210 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.FinishHashDrv
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA256
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA224
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA1
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.InitAes  0x0000000000000000       0xc8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.LoadAesKey
                0x0000000000000000      0x114 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.write_invalid_key
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .rodata.ProcessAesDrv.str1.4
                0x0000000000000000       0x6f C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.ProcessAesDrv
                0x0000000000000000      0x338 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.FinishAesDrv
                0x0000000000000000      0x200 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_write_key_slot
                0x0000000000000000       0xd8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_verify_kdf_input
                0x0000000000000000       0x24 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_convert_keybits_to_keysize
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_slot_and_size
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_slot_and_size_no_kdr
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_kdr_slot_and_size
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_load_key_chacha20
                0x0000000000000000      0x108 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_load_key_aes
                0x0000000000000000      0x200 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_derive_cmac
                0x0000000000000000      0x120 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_shadow_key_derive
                0x0000000000000000       0x98 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.write_invalid_chacha20_key
                0x0000000000000000       0x40 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_prepare_chacha_key
                0x0000000000000000       0x4c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_load_chacha_key
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_cleanup_chacha_key
                0x0000000000000000       0x3c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text.UtilCmacBuildDataForDerivation
                0x0000000000000000       0xc0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text.UtilCmacDeriveKey
                0x0000000000000000       0xf0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .text.CC_PalDataBufferAttrGet
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .text          0x0000000000000000       0x14 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .ARM.extab     0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .ARM.exidx     0x0000000000000000        0x8 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .text.memmove  0x0000000000000000       0x34 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .debug_frame   0x0000000000000000       0x28 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .text          0x0000000000000000       0x10 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .ARM.extab     0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .ARM.exidx     0x0000000000000000        0x8 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .tbss.errno    0x0000000000000000        0x4 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .text.memcmp   0x0000000000000000       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .debug_frame   0x0000000000000000       0x28 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.scanf_getc
                0x0000000000000000       0x14 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.scanf_ungetc
                0x0000000000000000       0x10 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.skip_spaces
                0x0000000000000000       0x2a c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.putval   0x0000000000000000       0x2c c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .rodata.__l_vfscanf.str1.1
                0x0000000000000000        0xc c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.__l_vfscanf
                0x0000000000000000      0x360 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .debug_frame   0x0000000000000000       0x94 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .text.strchr   0x0000000000000000       0x1a c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .text.fgetc    0x0000000000000000       0x46 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .debug_frame   0x0000000000000000       0x28 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .text.ungetc   0x0000000000000000       0x32 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .text          0x0000000000000000       0xc8 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .debug_frame   0x0000000000000000       0x28 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .text          0x0000000000000000      0x254 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x0000000000000000       0x30 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .text          0x0000000000000000      0x168 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .text          0x0000000000000000      0x424 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x0000000000000000       0x50 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .text          0x0000000000000000       0xa0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .text          0x0000000000000000       0xec c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .debug_frame   0x0000000000000000       0xc8 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .text          0x0000000000000000       0xa0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .debug_frame   0x0000000000000000       0x44 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .text          0x0000000000000000       0x30 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x0000000000000000       0x2c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .text          0x0000000000000000       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .debug_frame   0x0000000000000000       0x20 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .text          0x0000000000000000       0x2e c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .debug_frame   0x0000000000000000       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .text          0x0000000000000000       0x3c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x0000000000000000       0x2c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .text          0x0000000000000000      0x29c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.exidx     0x0000000000000000        0x8 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x0000000000000000       0x34 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .text          0x0000000000000000        0x4 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .text          0x0000000000000000      0x110 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x0000000000000000       0xc4 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .text          0x0000000000000000       0x40 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000100000 xr
RAM              0x0000000020000000 0x0000000000070000 xw
IDT_LIST         0x00000000ffff7fff 0x0000000000008000 xw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

                0x00000000000053d4                vfprintf = __l_vfprintf
                0x0000000000000000                vfscanf = __l_vfscanf
LOAD zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
LOAD zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
LOAD zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
LOAD app/libapp.a
LOAD zephyr/libzephyr.a
LOAD zephyr/arch/common/libarch__common.a
LOAD zephyr/arch/arch/arm/core/libarch__arm__core.a
LOAD zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a
LOAD zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a
LOAD zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a
LOAD zephyr/lib/libc/picolibc/liblib__libc__picolibc.a
LOAD zephyr/lib/libc/common/liblib__libc__common.a
LOAD zephyr/soc/soc/nrf5340/libsoc__nordic.a
LOAD zephyr/drivers/clock_control/libdrivers__clock_control.a
LOAD zephyr/drivers/console/libdrivers__console.a
LOAD zephyr/drivers/gpio/libdrivers__gpio.a
LOAD zephyr/drivers/i2c/libdrivers__i2c.a
LOAD zephyr/drivers/pinctrl/libdrivers__pinctrl.a
LOAD zephyr/drivers/serial/libdrivers__serial.a
LOAD zephyr/drivers/timer/libdrivers__timer.a
LOAD modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a
LOAD modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a
LOAD zephyr/kernel/libkernel.a
LOAD zephyr/arch/common/libisr_tables.a
LOAD C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a
LOAD c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a
LOAD c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a
                0x0000000000000020                _region_min_align = 0x20

.rel.plt        0x0000000000000000        0x0
 *(SORT_BY_ALIGNMENT(.rel.plt))
                [!provide]                        PROVIDE (__rel_iplt_start = .)
 *(SORT_BY_ALIGNMENT(.rel.iplt))
 .rel.iplt      0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                [!provide]                        PROVIDE (__rel_iplt_end = .)

.rela.plt       0x0000000000000000        0x0
 *(SORT_BY_ALIGNMENT(.rela.plt))
                [!provide]                        PROVIDE (__rela_iplt_start = .)
 *(SORT_BY_ALIGNMENT(.rela.iplt))
                [!provide]                        PROVIDE (__rela_iplt_end = .)

.rel.dyn
 *(SORT_BY_ALIGNMENT(.rel.*))

.rela.dyn
 *(SORT_BY_ALIGNMENT(.rela.*))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.plt))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.iplt))
                0x0000000000000000                __rom_region_start = 0x0

rom_start       0x0000000000000000      0x154
                0x0000000000000000                __rom_start_address = .
 FILL mask 0x00
                0x0000000000000000                . = (. + (0x0 - (. - __rom_start_address)))
                0x0000000000000000                . = ALIGN (0x4)
                0x0000000000000000                . = ALIGN (0x80)
                0x0000000000000000                . = ALIGN (0x200)
                0x0000000000000000                _vector_start = .
 *(SORT_BY_ALIGNMENT(.exc_vector_table))
 *(SORT_BY_ALIGNMENT(.exc_vector_table.*))
 .exc_vector_table._vector_table_section
                0x0000000000000000       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
                0x0000000000000000                _vector_table
 *(SORT_BY_ALIGNMENT(.vectors))
                0x0000000000000154                _vector_end = .
                0x0000000000000040                . = ALIGN (0x4)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.irq_vector_table*))
 .gnu.linkonce.irq_vector_table
                0x0000000000000040      0x114 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                0x0000000000000040                _irq_vector_table
                0x0000000000000154                _vector_end = .

text            0x0000000000000154     0x69a8
                0x0000000000000154                __text_region_start = .
 *(SORT_BY_ALIGNMENT(.text))
 .text          0x0000000000000154      0x378 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
                0x0000000000000154                __aeabi_drsub
                0x000000000000015c                __aeabi_dsub
                0x000000000000015c                __subdf3
                0x0000000000000160                __aeabi_dadd
                0x0000000000000160                __adddf3
                0x00000000000003d8                __floatunsidf
                0x00000000000003d8                __aeabi_ui2d
                0x00000000000003f8                __floatsidf
                0x00000000000003f8                __aeabi_i2d
                0x000000000000041c                __aeabi_f2d
                0x000000000000041c                __extendsfdf2
                0x0000000000000460                __floatundidf
                0x0000000000000460                __aeabi_ul2d
                0x0000000000000470                __floatdidf
                0x0000000000000470                __aeabi_l2d
 .text          0x00000000000004cc      0x21c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
                0x00000000000004cc                __aeabi_frsub
                0x00000000000004d4                __subsf3
                0x00000000000004d4                __aeabi_fsub
                0x00000000000004d8                __aeabi_fadd
                0x00000000000004d8                __addsf3
                0x0000000000000638                __aeabi_ui2f
                0x0000000000000638                __floatunsisf
                0x0000000000000640                __aeabi_i2f
                0x0000000000000640                __floatsisf
                0x000000000000065c                __aeabi_ul2f
                0x000000000000065c                __floatundisf
                0x000000000000066c                __aeabi_l2f
                0x000000000000066c                __floatdisf
 .text          0x00000000000006e8      0x2a0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
                0x00000000000006e8                __mulsf3
                0x00000000000006e8                __aeabi_fmul
                0x0000000000000850                __aeabi_fdiv
                0x0000000000000850                __divsf3
 *(SORT_BY_ALIGNMENT(.text.*))
 .text.main     0x0000000000000988      0x294 app/libapp.a(main_temperature_working.c.obj)
                0x0000000000000988                main
 .text.i2c_driver_init
                0x0000000000000c1c       0x18 app/libapp.a(zephyr_i2c_driver.c.obj)
                0x0000000000000c1c                i2c_driver_init
 .text.i2c_write_bytes
                0x0000000000000c34       0x2c app/libapp.a(zephyr_i2c_driver.c.obj)
                0x0000000000000c34                i2c_write_bytes
 .text.i2c_read_bytes
                0x0000000000000c60       0x2c app/libapp.a(zephyr_i2c_driver.c.obj)
                0x0000000000000c60                i2c_read_bytes
 .text.i2c_write_read_bytes
                0x0000000000000c8c       0x3c app/libapp.a(zephyr_i2c_driver.c.obj)
                0x0000000000000c8c                i2c_write_read_bytes
 .text.m117_read_temperature
                0x0000000000000cc8       0x58 app/libapp.a(m117_sensor.c.obj)
                0x0000000000000cc8                m117_read_temperature
 .text.char_out
                0x0000000000000d20        0xc zephyr/libzephyr.a(printk.c.obj)
 .text.__printk_hook_install
                0x0000000000000d2c        0xc zephyr/libzephyr.a(printk.c.obj)
                0x0000000000000d2c                __printk_hook_install
 .text.vprintk  0x0000000000000d38       0x30 zephyr/libzephyr.a(printk.c.obj)
                0x0000000000000d38                vprintk
 .text.z_thread_entry
                0x0000000000000d68       0x34 zephyr/libzephyr.a(thread_entry.c.obj)
                0x0000000000000d68                z_thread_entry
 .text.process_event
                0x0000000000000d9c      0x21c zephyr/libzephyr.a(onoff.c.obj)
 .text.mem_attr_get_regions
                0x0000000000000fb8        0xc zephyr/libzephyr.a(mem_attr.c.obj)
                0x0000000000000fb8                mem_attr_get_regions
 .text.pm_device_action_run
                0x0000000000000fc4       0x8c zephyr/libzephyr.a(device.c.obj)
                0x0000000000000fc4                pm_device_action_run
 .text.boot_banner
                0x0000000000001050       0x1c zephyr/libzephyr.a(banner.c.obj)
                0x0000000000001050                boot_banner
 .text.nrf_cc3xx_platform_abort_init
                0x000000000000106c        0xc zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                0x000000000000106c                nrf_cc3xx_platform_abort_init
 .text.mutex_free_platform
                0x0000000000001078       0x54 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_lock_platform
                0x00000000000010cc       0x74 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_unlock_platform
                0x0000000000001140       0x68 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_init_platform
                0x00000000000011a8       0x94 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.nrf_cc3xx_platform_mutex_init
                0x000000000000123c       0x2c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x000000000000123c                nrf_cc3xx_platform_mutex_init
 .text.z_SysNmiOnReset
                0x0000000000001268        0x8 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                0x0000000000001268                z_SysNmiOnReset
 .text.arch_tls_stack_setup
                0x0000000000001270       0x40 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                0x0000000000001270                arch_tls_stack_setup
 .text._HandlerModeExit
                0x00000000000012b0       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                0x00000000000012b0                z_arm_int_exit
                0x00000000000012b0                z_arm_exc_exit
 .text.usage_fault.constprop.0
                0x00000000000012d8       0x5c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.bus_fault.constprop.0
                0x0000000000001334       0x6c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.mem_manage_fault.constprop.0
                0x00000000000013a0       0x78 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.z_arm_fault
                0x0000000000001418      0x124 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                0x0000000000001418                z_arm_fault
 .text.z_arm_fault_init
                0x000000000000153c       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                0x000000000000153c                z_arm_fault_init
 .text.__fault  0x000000000000155c       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                0x000000000000155c                z_arm_usage_fault
                0x000000000000155c                z_arm_mpu_fault
                0x000000000000155c                z_arm_exc_spurious
                0x000000000000155c                z_arm_debug_monitor
                0x000000000000155c                z_arm_hard_fault
                0x000000000000155c                z_arm_bus_fault
 .text._reset_section
                0x0000000000001570       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                0x0000000000001570                __start
                0x0000000000001570                z_arm_reset
 .text.z_arm_clear_arm_mpu_config
                0x00000000000015d0       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                0x00000000000015d0                z_arm_clear_arm_mpu_config
 .text.z_arm_init_arch_hw_at_boot
                0x00000000000015f4       0x4c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                0x00000000000015f4                z_arm_init_arch_hw_at_boot
 .text.z_impl_k_thread_abort
                0x0000000000001640       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                0x0000000000001640                z_impl_k_thread_abort
 .text.arch_swap
                0x000000000000166c       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                0x000000000000166c                arch_swap
 .text.z_arm_pendsv_c
                0x000000000000169c       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                0x000000000000169c                z_arm_pendsv_c
 .text.z_arm_pendsv
                0x00000000000016dc       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                0x00000000000016dc                z_arm_pendsv
 .text.z_arm_svc
                0x000000000000171c       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                0x000000000000171c                z_arm_svc
 .text.arch_irq_enable
                0x000000000000173c       0x1c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x000000000000173c                arch_irq_enable
 .text.arch_irq_is_enabled
                0x0000000000001758       0x1c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x0000000000001758                arch_irq_is_enabled
 .text.z_arm_irq_priority_set
                0x0000000000001774       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x0000000000001774                z_arm_irq_priority_set
 .text.z_prep_c
                0x00000000000017a0       0x38 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                0x00000000000017a0                z_prep_c
 .text.arch_new_thread
                0x00000000000017d8       0x38 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x00000000000017d8                arch_new_thread
 .text.arch_switch_to_main_thread
                0x0000000000001810       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000001810                arch_switch_to_main_thread
 .text.z_arm_cpu_idle_init
                0x0000000000001858        0xc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000001858                z_arm_cpu_idle_init
 .text.z_arm_interrupt_init
                0x0000000000001864       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                0x0000000000001864                z_arm_interrupt_init
 .text._isr_wrapper
                0x000000000000187c       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                0x000000000000187c                _isr_wrapper
 .text.z_arm_configure_static_mpu_regions
                0x00000000000018a0       0x38 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                0x00000000000018a0                z_arm_configure_static_mpu_regions
 .text.region_init
                0x00000000000018d8       0x34 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.region_allocate_and_init
                0x000000000000190c       0x24 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.mpu_configure_regions_and_partition.constprop.0
                0x0000000000001930      0x148 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.arm_core_mpu_enable
                0x0000000000001a78       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000001a78                arm_core_mpu_enable
 .text.arm_core_mpu_disable
                0x0000000000001a90       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000001a90                arm_core_mpu_disable
 .text.arm_core_mpu_configure_static_mpu_regions
                0x0000000000001aa4       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000001aa4                arm_core_mpu_configure_static_mpu_regions
 .text.arm_core_mpu_mark_areas_for_dynamic_regions
                0x0000000000001ab8       0xb8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000001ab8                arm_core_mpu_mark_areas_for_dynamic_regions
 .text.z_arm_mpu_init
                0x0000000000001b70      0x11c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000001b70                z_arm_mpu_init
 .text.z_impl_zephyr_fputc
                0x0000000000001c8c       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
                0x0000000000001c8c                z_impl_zephyr_fputc
 .text.__stdout_hook_install
                0x0000000000001c9c       0x18 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
                0x0000000000001c9c                __stdout_hook_install
 .text.malloc_prepare
                0x0000000000001cb4       0x24 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.nrf_gpio_pin_control_select
                0x0000000000001cd8       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.nordicsemi_nrf53_init
                0x0000000000001d14       0x4c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.rtc_pretick_init
                0x0000000000001d60       0x60 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.arch_busy_wait
                0x0000000000001dc0       0x24 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                0x0000000000001dc0                arch_busy_wait
 .text.nrf53_cpunet_mgmt_init
                0x0000000000001de4       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.onoff_start
                0x0000000000001df4       0x6c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.z_sys_poweroff
                0x0000000000001e60       0x14 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
                0x0000000000001e60                z_sys_poweroff
 .text.onoff_stop
                0x0000000000001e74       0x30 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.onoff_start
                0x0000000000001ea4       0x44 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.generic_hfclk_stop
                0x0000000000001ee8       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clk_init
                0x0000000000001f1c       0x70 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_blocking_start
                0x0000000000001f8c       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clkstarted_handle.constprop.0
                0x0000000000001fc0       0x4c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.generic_hfclk_start
                0x000000000000200c       0x78 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clock_event_handler
                0x0000000000002084       0x30 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_control_lf_on
                0x00000000000020b4       0xf8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x00000000000020b4                z_nrf_clock_control_lf_on
 .text.uart_console_init
                0x00000000000021ac       0x28 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text.console_out
                0x00000000000021d4       0x28 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text.gpio_nrfx_pin_interrupt_configure
                0x00000000000021fc      0x100 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_init
                0x00000000000022fc       0x48 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.nrfx_gpio_handler
                0x0000000000002344       0x4c zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_pin_configure
                0x0000000000002390      0x188 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.event_handler
                0x0000000000002518       0x38 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.i2c_nrfx_twim_transfer
                0x0000000000002550      0x148 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.i2c_nrfx_twim_recover_bus
                0x0000000000002698       0x54 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x0000000000002698                i2c_nrfx_twim_recover_bus
 .text.i2c_nrfx_twim_msg_transfer
                0x00000000000026ec       0x6c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x00000000000026ec                i2c_nrfx_twim_msg_transfer
 .text.i2c_nrfx_twim_common_init
                0x0000000000002758       0x40 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x0000000000002758                i2c_nrfx_twim_common_init
 .text.nrf_gpio_pin_port_decode
                0x0000000000002798       0x24 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .text.pinctrl_configure_pins
                0x00000000000027bc      0x168 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                0x00000000000027bc                pinctrl_configure_pins
 .text.uarte_0_init
                0x0000000000002924       0xd8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_nrfx_poll_out
                0x00000000000029fc       0xc4 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.compare_int_lock
                0x0000000000002ac0       0x40 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_timeout_handler
                0x0000000000002b00       0x48 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.compare_int_unlock
                0x0000000000002b48       0x4c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_read
                0x0000000000002b94       0x44 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000002b94                z_nrf_rtc_timer_read
 .text.compare_set
                0x0000000000002bd8      0x124 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_driver_init
                0x0000000000002cfc       0x90 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.rtc_nrf_isr
                0x0000000000002d8c       0xd0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000002d8c                rtc_nrf_isr
 .text.sys_clock_set_timeout
                0x0000000000002e5c       0x5c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000002e5c                sys_clock_set_timeout
 .text.sys_clock_elapsed
                0x0000000000002eb8       0x14 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000002eb8                sys_clock_elapsed
 .text.nrf53_errata_42
                0x0000000000002ecc       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemInit
                0x0000000000002ef0      0x1c4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                0x0000000000002ef0                SystemInit
 .text.nrfx_flag32_alloc
                0x00000000000030b4       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                0x00000000000030b4                nrfx_flag32_alloc
 .text.nrfx_flag32_free
                0x00000000000030f4       0x38 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                0x00000000000030f4                nrfx_flag32_free
 .text.nrfx_gppi_channels_enable
                0x000000000000312c        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x000000000000312c                nrfx_gppi_channels_enable
 .text.nrfx_gppi_channel_alloc
                0x0000000000003138        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000003138                nrfx_gppi_channel_alloc
 .text.nrfx_clock_init
                0x0000000000003144       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000003144                nrfx_clock_init
 .text.nrfx_clock_enable
                0x0000000000003164       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000003164                nrfx_clock_enable
 .text.nrfx_clock_start
                0x0000000000003194       0xd0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000003194                nrfx_clock_start
 .text.nrfx_power_clock_irq_handler
                0x0000000000003264       0xb0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000003264                nrfx_power_clock_irq_handler
 .text.nrfx_dppi_channel_alloc
                0x0000000000003314       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                0x0000000000003314                nrfx_dppi_channel_alloc
 .text.nrf_gpio_pin_port_decode
                0x0000000000003324       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.get_pin_idx
                0x0000000000003348       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.call_handler.constprop.0
                0x000000000000335c       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_te_get.constprop.0
                0x000000000000339c       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_is_output.constprop.0
                0x00000000000033b4       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_in_use_by_te.constprop.0
                0x00000000000033cc       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.latch_pending_read_and_check
                0x00000000000033e4       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.release_handler.isra.0
                0x0000000000003420       0x58 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.__nrfy_internal_gpiote_events_process.constprop.0
                0x0000000000003478       0x5c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_uninit
                0x00000000000034d4       0x84 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_input_configure
                0x0000000000003558      0x178 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000003558                nrfx_gpiote_input_configure
 .text.nrfx_gpiote_output_configure
                0x00000000000036d0      0x104 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000036d0                nrfx_gpiote_output_configure
 .text.nrfx_gpiote_global_callback_set
                0x00000000000037d4        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000037d4                nrfx_gpiote_global_callback_set
 .text.nrfx_gpiote_channel_get
                0x00000000000037e0       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000037e0                nrfx_gpiote_channel_get
 .text.nrfx_gpiote_init
                0x0000000000003814       0x64 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000003814                nrfx_gpiote_init
 .text.nrfx_gpiote_init_check
                0x0000000000003878       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000003878                nrfx_gpiote_init_check
 .text.nrfx_gpiote_channel_free
                0x000000000000388c        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x000000000000388c                nrfx_gpiote_channel_free
 .text.nrfx_gpiote_channel_alloc
                0x0000000000003898        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000003898                nrfx_gpiote_channel_alloc
 .text.nrfx_gpiote_trigger_enable
                0x00000000000038a4       0xac modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000038a4                nrfx_gpiote_trigger_enable
 .text.nrfx_gpiote_0_irq_handler
                0x0000000000003950      0x1b8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000003950                nrfx_gpiote_0_irq_handler
 .text.nrf_gpio_cfg.constprop.0
                0x0000000000003b08       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.twim_configure
                0x0000000000003b50       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_init
                0x0000000000003b8c       0x74 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000003b8c                nrfx_twim_init
 .text.nrfx_twim_enable
                0x0000000000003c00       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000003c00                nrfx_twim_enable
 .text.nrfx_twim_disable
                0x0000000000003c20       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000003c20                nrfx_twim_disable
 .text.nrfx_twim_xfer
                0x0000000000003c5c      0x350 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000003c5c                nrfx_twim_xfer
 .text.nrfx_twim_1_irq_handler
                0x0000000000003fac      0x188 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000003fac                nrfx_twim_1_irq_handler
 .text.nrf_gpio_pin_port_decode
                0x0000000000004134       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .text.nrfx_twi_twim_bus_recover
                0x0000000000004158       0xd4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
                0x0000000000004158                nrfx_twi_twim_bus_recover
 .text.z_fatal_error
                0x000000000000422c       0x30 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x000000000000422c                z_fatal_error
 .text.z_sys_init_run_level
                0x000000000000425c       0x2c zephyr/kernel/libkernel.a(init.c.obj)
 .text.bg_thread_main
                0x0000000000004288       0xc4 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_bss_zero
                0x000000000000434c       0x18 zephyr/kernel/libkernel.a(init.c.obj)
                0x000000000000434c                z_bss_zero
 .text.z_init_cpu
                0x0000000000004364       0x68 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000004364                z_init_cpu
 .text.z_cstart
                0x00000000000043cc       0xdc zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000000043cc                z_cstart
 .text.init_mem_slab_obj_core_list
                0x00000000000044a8       0x24 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_alloc
                0x00000000000044cc       0x5c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x00000000000044cc                k_mem_slab_alloc
 .text.z_impl_k_mutex_lock
                0x0000000000004528       0xf0 zephyr/kernel/libkernel.a(mutex.c.obj)
                0x0000000000004528                z_impl_k_mutex_lock
 .text.z_impl_k_mutex_unlock
                0x0000000000004618       0x78 zephyr/kernel/libkernel.a(mutex.c.obj)
                0x0000000000004618                z_impl_k_mutex_unlock
 .text.z_impl_k_sem_give
                0x0000000000004690       0x48 zephyr/kernel/libkernel.a(sem.c.obj)
                0x0000000000004690                z_impl_k_sem_give
 .text.z_impl_k_sem_take
                0x00000000000046d8       0x4c zephyr/kernel/libkernel.a(sem.c.obj)
                0x00000000000046d8                z_impl_k_sem_take
 .text.z_setup_new_thread
                0x0000000000004724       0x7c zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000004724                z_setup_new_thread
 .text.update_cache
                0x00000000000047a0       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.unready_thread
                0x00000000000047dc       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.ready_thread
                0x000000000000480c       0x68 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_thread_halt
                0x0000000000004874       0xdc zephyr/kernel/libkernel.a(sched.c.obj)
 .text.move_thread_to_end_of_prio_q
                0x0000000000004950       0x74 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004950                move_thread_to_end_of_prio_q
 .text.z_pend_curr
                0x00000000000049c4       0x58 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000049c4                z_pend_curr
 .text.z_thread_prio_set
                0x0000000000004a1c       0x88 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004a1c                z_thread_prio_set
 .text.z_reschedule
                0x0000000000004aa4       0x24 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004aa4                z_reschedule
 .text.z_sched_start
                0x0000000000004ac8       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004ac8                z_sched_start
 .text.z_reschedule_irqlock
                0x0000000000004b04       0x28 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004b04                z_reschedule_irqlock
 .text.k_sched_lock
                0x0000000000004b2c       0x28 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004b2c                k_sched_lock
 .text.k_sched_unlock
                0x0000000000004b54       0x40 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004b54                k_sched_unlock
 .text.z_sched_init
                0x0000000000004b94       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004b94                z_sched_init
 .text.z_impl_k_yield
                0x0000000000004ba4       0x7c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004ba4                z_impl_k_yield
 .text.z_tick_sleep
                0x0000000000004c20       0x88 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_sleep
                0x0000000000004ca8       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004ca8                z_impl_k_sleep
 .text.z_impl_k_sched_current_thread_query
                0x0000000000004ce4        0xc zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000004ce4                z_impl_k_sched_current_thread_query
 .text.slice_timeout
                0x0000000000004cf0       0x20 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .text.thread_is_sliceable
                0x0000000000004d10       0x3c zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x0000000000004d10                thread_is_sliceable
 .text.z_reset_time_slice
                0x0000000000004d4c       0x50 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x0000000000004d4c                z_reset_time_slice
 .text.z_time_slice
                0x0000000000004d9c       0x60 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x0000000000004d9c                z_time_slice
 .text.z_data_copy
                0x0000000000004dfc       0x34 zephyr/kernel/libkernel.a(xip.c.obj)
                0x0000000000004dfc                z_data_copy
 .text.elapsed  0x0000000000004e30       0x14 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.remove_timeout
                0x0000000000004e44       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.next_timeout
                0x0000000000004e7c       0x40 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_add_timeout
                0x0000000000004ebc       0xf4 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000004ebc                z_add_timeout
 .text.sys_clock_announce
                0x0000000000004fb0       0xd0 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000004fb0                sys_clock_announce
 .text.sys_clock_tick_get
                0x0000000000005080       0x30 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000005080                sys_clock_tick_get
 .text.nrf_cc3xx_platform_init_no_rng
                0x00000000000050b0       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                0x00000000000050b0                nrf_cc3xx_platform_init_no_rng
 .text.nrf_cc3xx_platform_abort
                0x00000000000050e8       0x24 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .text.CC_PalAbort
                0x000000000000510c       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x000000000000510c                CC_PalAbort
 .text.nrf_cc3xx_platform_set_abort
                0x0000000000005150       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x0000000000005150                nrf_cc3xx_platform_set_abort
 .text.mutex_free
                0x0000000000005160       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_lock
                0x0000000000005194       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_unlock
                0x00000000000051dc       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_init
                0x0000000000005214       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.nrf_cc3xx_platform_set_mutexes
                0x0000000000005234       0x78 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000000005234                nrf_cc3xx_platform_set_mutexes
 .text.CC_LibInitNoRng
                0x00000000000052ac       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
                0x00000000000052ac                CC_LibInitNoRng
 .text.CC_HalInit
                0x00000000000052d4        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
                0x00000000000052d4                CC_HalInit
 .text.CC_PalInit
                0x00000000000052d8       0x5c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x00000000000052d8                CC_PalInit
 .text.CC_PalTerminate
                0x0000000000005334       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000000005334                CC_PalTerminate
 .text.CC_PalDmaInit
                0x0000000000005368        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                0x0000000000005368                CC_PalDmaInit
 .text.CC_PalDmaTerminate
                0x000000000000536c        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                0x000000000000536c                CC_PalDmaTerminate
 .text.CC_PalMutexCreate
                0x0000000000005370       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                0x0000000000005370                CC_PalMutexCreate
 .text.CC_PalMutexDestroy
                0x0000000000005384       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                0x0000000000005384                CC_PalMutexDestroy
 .text.CC_PalPowerSaveModeInit
                0x0000000000005398       0x3c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
                0x0000000000005398                CC_PalPowerSaveModeInit
 .text.__l_vfprintf
                0x00000000000053d4      0x4b4 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
                0x00000000000053d4                __l_vfprintf
 .text._OffsetAbsSyms
                0x0000000000005888        0x2 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                0x0000000000005888                _OffsetAbsSyms
 .text.i2c_transfer
                0x000000000000588a       0x1e app/libapp.a(zephyr_i2c_driver.c.obj)
 .text.calculate_crc8
                0x00000000000058a8       0x30 app/libapp.a(zephyr_i2c_driver.c.obj)
                0x00000000000058a8                calculate_crc8
 .text.m117_init
                0x00000000000058d8       0xac app/libapp.a(m117_sensor.c.obj)
                0x00000000000058d8                m117_init
 .text.m117_start_conversion
                0x0000000000005984       0x1e app/libapp.a(m117_sensor.c.obj)
                0x0000000000005984                m117_start_conversion
 .text.m117_measure_temperature
                0x00000000000059a2       0x18 app/libapp.a(m117_sensor.c.obj)
                0x00000000000059a2                m117_measure_temperature
 .text.chunk_field
                0x00000000000059ba       0x16 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_set
                0x00000000000059d0       0x16 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_size
                0x00000000000059e6        0xc zephyr/libzephyr.a(heap.c.obj)
 .text.set_chunk_used
                0x00000000000059f2       0x30 zephyr/libzephyr.a(heap.c.obj)
 .text.set_chunk_size
                0x0000000000005a22        0x8 zephyr/libzephyr.a(heap.c.obj)
 .text.bucket_idx.isra.0
                0x0000000000005a2a       0x1c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_add
                0x0000000000005a46       0x84 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_init
                0x0000000000005aca       0xb0 zephyr/libzephyr.a(heap.c.obj)
                0x0000000000005aca                sys_heap_init
 .text.arch_printk_char_out
                0x0000000000005b7a        0x4 zephyr/libzephyr.a(printk.c.obj)
                0x0000000000005b7a                arch_printk_char_out
 .text.printk   0x0000000000005b7e       0x1a zephyr/libzephyr.a(printk.c.obj)
                0x0000000000005b7e                printk
 .text.sys_poweroff
                0x0000000000005b98       0x14 zephyr/libzephyr.a(poweroff.c.obj)
                0x0000000000005b98                sys_poweroff
 .text.process_recheck
                0x0000000000005bac       0x38 zephyr/libzephyr.a(onoff.c.obj)
 .text.validate_args
                0x0000000000005be4       0x20 zephyr/libzephyr.a(onoff.c.obj)
 .text.notify_one
                0x0000000000005c04       0x2c zephyr/libzephyr.a(onoff.c.obj)
 .text.transition_complete
                0x0000000000005c30       0x1a zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_manager_init
                0x0000000000005c4a       0x26 zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000005c4a                onoff_manager_init
 .text.onoff_request
                0x0000000000005c70       0xae zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000005c70                onoff_request
 .text.sys_notify_validate
                0x0000000000005d1e       0x22 zephyr/libzephyr.a(notify.c.obj)
                0x0000000000005d1e                sys_notify_validate
 .text.sys_notify_finalize
                0x0000000000005d40       0x1a zephyr/libzephyr.a(notify.c.obj)
                0x0000000000005d40                sys_notify_finalize
 .text._ConfigAbsSyms
                0x0000000000005d5a        0x2 zephyr/libzephyr.a(configs.c.obj)
                0x0000000000005d5a                _ConfigAbsSyms
 .text.pm_device_state_get
                0x0000000000005d5c       0x12 zephyr/libzephyr.a(device.c.obj)
                0x0000000000005d5c                pm_device_state_get
 .text.pm_device_is_powered
                0x0000000000005d6e       0x18 zephyr/libzephyr.a(device.c.obj)
                0x0000000000005d6e                pm_device_is_powered
 .text.pm_device_driver_init
                0x0000000000005d86       0x30 zephyr/libzephyr.a(device.c.obj)
                0x0000000000005d86                pm_device_driver_init
 .text.abort_function
                0x0000000000005db6        0x2 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .text.z_arm_fatal_error
                0x0000000000005db8        0xc zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                0x0000000000005db8                z_arm_fatal_error
 .text.z_do_kernel_oops
                0x0000000000005dc4        0x8 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                0x0000000000005dc4                z_do_kernel_oops
 .text.z_arm_nmi
                0x0000000000005dcc        0xe zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                0x0000000000005dcc                z_arm_nmi
 .text.z_irq_spurious
                0x0000000000005dda        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x0000000000005dda                z_irq_spurious
 .text.configure_builtin_stack_guard
                0x0000000000005de2        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000005de2                configure_builtin_stack_guard
 .text.arch_irq_unlock_outlined
                0x0000000000005dea        0xe zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000005dea                arch_irq_unlock_outlined
 .text.arch_cpu_idle
                0x0000000000005df8       0x2a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000005df8                arch_cpu_idle
 .text.arch_cpu_atomic_idle
                0x0000000000005e22       0x2e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000005e22                arch_cpu_atomic_idle
 .text.arm_cmse_mpu_region_get
                0x0000000000005e50       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                0x0000000000005e50                arm_cmse_mpu_region_get
 .text.mpu_configure_region
                0x0000000000005e62       0x32 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.picolibc_put
                0x0000000000005e94        0xa zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .text.onoff_stop
                0x0000000000005e9e       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.get_status
                0x0000000000005eae       0x12 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.stop     0x0000000000005ec0       0x52 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_stop
                0x0000000000005f12        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.async_start
                0x0000000000005f18       0x5c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_start
                0x0000000000005f74        0xe zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.onoff_started_callback
                0x0000000000005f82       0x12 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclkaudio_start
                0x0000000000005f94        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclk192m_start
                0x0000000000005f9a        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.lfclk_start
                0x0000000000005fa0        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclkaudio_stop
                0x0000000000005fa6        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclk192m_stop
                0x0000000000005fac        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.lfclk_stop
                0x0000000000005fb2        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.blocking_start_callback
                0x0000000000005fb8        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.gpio_nrfx_port_get_raw
                0x0000000000005fbe        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_set_masked_raw
                0x0000000000005fca       0x14 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_set_bits_raw
                0x0000000000005fde        0xa zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_clear_bits_raw
                0x0000000000005fe8        0xa zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_toggle_bits
                0x0000000000005ff2       0x14 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_manage_callback
                0x0000000000006006       0x52 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.irq_connect1
                0x0000000000006058        0xa zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.i2c_nrfx_twim_init
                0x0000000000006062       0x26 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.pinctrl_apply_state.isra.0
                0x0000000000006088       0x1e zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .text.twim_nrfx_pm_action
                0x00000000000060a6       0x32 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x00000000000060a6                twim_nrfx_pm_action
 .text.i2c_nrfx_twim_configure
                0x00000000000060d8       0x3a zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x00000000000060d8                i2c_nrfx_twim_configure
 .text.pinctrl_lookup_state
                0x0000000000006112       0x26 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                0x0000000000006112                pinctrl_lookup_state
 .text.uarte_nrfx_err_check
                0x0000000000006138        0xe zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_nrfx_poll_in
                0x0000000000006146       0x26 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.is_tx_ready.isra.0
                0x000000000000616c       0x20 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_nrfx_isr_int
                0x000000000000618c       0x42 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.pinctrl_apply_state.isra.0
                0x00000000000061ce       0x1e zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_nrfx_pm_action
                0x00000000000061ec       0xf4 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.event_clear
                0x00000000000062e0       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.hw_cc3xx_init_internal
                0x00000000000062f8        0x4 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text.hw_cc3xx_init
                0x00000000000062fc       0x12 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text.nrfx_isr
                0x000000000000630e        0x2 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                0x000000000000630e                nrfx_isr
 .text.nrfx_busy_wait
                0x0000000000006310        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                0x0000000000006310                nrfx_busy_wait
 .text.nrfx_gppi_event_endpoint_setup
                0x0000000000006314        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000006314                nrfx_gppi_event_endpoint_setup
 .text.nrfx_gppi_task_endpoint_setup
                0x000000000000631e        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x000000000000631e                nrfx_gppi_task_endpoint_setup
 .text.nrfx_gppi_channel_endpoints_setup
                0x0000000000006328        0xe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000006328                nrfx_gppi_channel_endpoints_setup
 .text.clock_stop
                0x0000000000006336      0x162 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_stop
                0x0000000000006498        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000006498                nrfx_clock_stop
 .text.nrf_gpio_reconfigure
                0x000000000000649c       0x92 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrf_gpio_cfg_sense_set
                0x000000000000652e       0x1e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_trigger_disable
                0x000000000000654c       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_pin_uninit
                0x0000000000006594        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000006594                nrfx_gpiote_pin_uninit
 .text.nrfx_gpiote_trigger_disable
                0x0000000000006598        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000006598                nrfx_gpiote_trigger_disable
 .text.xfer_completeness_check
                0x000000000000659c       0x62 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.pins_configure
                0x00000000000065fe       0x54 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.__nrfy_internal_twim_event_handle.isra.0
                0x0000000000006652       0x22 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.__nrfy_internal_twim_events_process.constprop.0
                0x0000000000006674       0x54 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrf_gpio_pin_set
                0x00000000000066c8       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .text.z_impl_k_busy_wait
                0x00000000000066e0        0x8 zephyr/kernel/libkernel.a(busy_wait.c.obj)
                0x00000000000066e0                z_impl_k_busy_wait
 .text.z_device_state_init
                0x00000000000066e8        0x2 zephyr/kernel/libkernel.a(device.c.obj)
                0x00000000000066e8                z_device_state_init
 .text.z_impl_device_is_ready
                0x00000000000066ea       0x16 zephyr/kernel/libkernel.a(device.c.obj)
                0x00000000000066ea                z_impl_device_is_ready
 .text.arch_system_halt
                0x0000000000006700       0x10 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x0000000000006700                arch_system_halt
 .text.k_sys_fatal_error_handler
                0x0000000000006710        0x6 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x0000000000006710                k_sys_fatal_error_handler
 .text.do_device_init
                0x0000000000006716       0x30 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_early_memset
                0x0000000000006746        0x4 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000006746                z_early_memset
 .text.z_early_memcpy
                0x000000000000674a        0x4 zephyr/kernel/libkernel.a(init.c.obj)
                0x000000000000674a                z_early_memcpy
 .text.z_init_static
                0x000000000000674e        0x2 zephyr/kernel/libkernel.a(init_static.c.obj)
                0x000000000000674e                z_init_static
 .text.create_free_list
                0x0000000000006750       0x34 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_init
                0x0000000000006784       0x1c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x0000000000006784                k_mem_slab_init
 .text.k_mem_slab_free
                0x00000000000067a0       0x4a zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x00000000000067a0                k_mem_slab_free
 .text.idle     0x00000000000067ea       0x16 zephyr/kernel/libkernel.a(idle.c.obj)
                0x00000000000067ea                idle
 .text.adjust_owner_prio.isra.0
                0x0000000000006800       0x10 zephyr/kernel/libkernel.a(mutex.c.obj)
 .text.z_impl_k_mutex_init
                0x0000000000006810        0xe zephyr/kernel/libkernel.a(mutex.c.obj)
                0x0000000000006810                z_impl_k_mutex_init
 .text.z_impl_k_sem_init
                0x000000000000681e       0x18 zephyr/kernel/libkernel.a(sem.c.obj)
                0x000000000000681e                z_impl_k_sem_init
 .text.k_is_in_isr
                0x0000000000006836        0xc zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000006836                k_is_in_isr
 .text.z_impl_k_thread_start
                0x0000000000006842        0x4 zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000006842                z_impl_k_thread_start
 .text.sys_dlist_remove
                0x0000000000006846       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.unpend_thread_no_timeout
                0x0000000000006856       0x14 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.add_to_waitq_locked
                0x000000000000686a       0x50 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_ready_thread
                0x00000000000068ba       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000068ba                z_ready_thread
 .text.z_impl_k_thread_suspend
                0x00000000000068d8       0x34 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000068d8                z_impl_k_thread_suspend
 .text.z_sched_wake_thread
                0x000000000000690c       0x42 zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000000000690c                z_sched_wake_thread
 .text.z_thread_timeout
                0x000000000000694e        0x8 zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000000000694e                z_thread_timeout
 .text.z_unpend_first_thread
                0x0000000000006956       0x36 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006956                z_unpend_first_thread
 .text.z_thread_abort
                0x000000000000698c       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000000000698c                z_thread_abort
 .text.z_abort_timeout
                0x00000000000069d0       0x2a zephyr/kernel/libkernel.a(timeout.c.obj)
                0x00000000000069d0                z_abort_timeout
 .text.sys_clock_tick_get_32
                0x00000000000069fa        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x00000000000069fa                sys_clock_tick_get_32
 .text.memcpy   0x0000000000006a02       0x1a c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
                0x0000000000006a02                __aeabi_memcpy
                0x0000000000006a02                __aeabi_memcpy4
                0x0000000000006a02                __aeabi_memcpy8
                0x0000000000006a02                memcpy
 .text.memset   0x0000000000006a1c       0x10 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
                0x0000000000006a1c                memset
 .text.strnlen  0x0000000000006a2c       0x18 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
                0x0000000000006a2c                strnlen
 .text.__ultoa_invert
                0x0000000000006a44       0xac c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 *(SORT_BY_ALIGNMENT(.TEXT.*))
 .TEXT.__aeabi_read_tp
                0x0000000000006af0        0xc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
                0x0000000000006af0                __aeabi_read_tp
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.t.*))
 *(SORT_BY_ALIGNMENT(.glue_7t))
 .glue_7t       0x0000000000006afc        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.glue_7))
 .glue_7        0x0000000000006afc        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.vfp11_veneer))
 .vfp11_veneer  0x0000000000006afc        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.v4_bx))
 .v4_bx         0x0000000000006afc        0x0 linker stubs
                0x0000000000006afc                . = ALIGN (0x4)
                0x0000000000006afc                __text_region_end = .

.ARM.exidx      0x0000000000006afc        0x0
                0x0000000000006afc                __exidx_start = .
 *(SORT_BY_ALIGNMENT(.ARM.exidx*) SORT_BY_ALIGNMENT(gnu.linkonce.armexidx.*))
                0x0000000000006afc                __exidx_end = .
                0x0000000000006afc                __rodata_region_start = .

initlevel       0x0000000000006afc       0x70
                0x0000000000006afc                __init_start = .
                0x0000000000006afc                __init_EARLY_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_EARLY?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_EARLY??_*)))
                0x0000000000006afc                __init_PRE_KERNEL_1_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_1?_*)))
 .z_init_PRE_KERNEL_10_0_
                0x0000000000006afc        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .z_init_PRE_KERNEL_10_0_
                0x0000000000006b04        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_1??_*)))
 .z_init_PRE_KERNEL_130_00090_
                0x0000000000006b0c        0x8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .z_init_PRE_KERNEL_130_0_
                0x0000000000006b14        0x8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .z_init_PRE_KERNEL_140_00010_
                0x0000000000006b1c        0x8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_init_PRE_KERNEL_140_00014_
                0x0000000000006b24        0x8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_init_PRE_KERNEL_140_0_
                0x0000000000006b2c        0x8 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .z_init_PRE_KERNEL_150_00127_
                0x0000000000006b34        0x8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .z_init_PRE_KERNEL_160_0_
                0x0000000000006b3c        0x8 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
                0x0000000000006b44                __init_PRE_KERNEL_2_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_2?_*)))
 .z_init_PRE_KERNEL_20_0_
                0x0000000000006b44        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_2??_*)))
                0x0000000000006b4c                __init_POST_KERNEL_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_POST_KERNEL?_*)))
 .z_init_POST_KERNEL0_0_
                0x0000000000006b4c        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_POST_KERNEL??_*)))
 .z_init_POST_KERNEL35_0_
                0x0000000000006b54        0x8 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .z_init_POST_KERNEL40_0_
                0x0000000000006b5c        0x8 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .z_init_POST_KERNEL50_00102_
                0x0000000000006b64        0x8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                0x0000000000006b6c                __init_APPLICATION_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_APPLICATION?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_APPLICATION??_*)))
                0x0000000000006b6c                __init_SMP_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_SMP?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_SMP??_*)))
                0x0000000000006b6c                __init_end = .
                0x0000000000006b6c                __deferred_init_list_start = .
 *(SORT_BY_ALIGNMENT(.z_deferred_init*))
                0x0000000000006b6c                __deferred_init_list_end = .

device_area     0x0000000000006b6c       0x78
                0x0000000000006b6c                _device_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._device.static.*_?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._device.static.*_??_*)))
 ._device.static.1_30_
                0x0000000000006b6c       0x18 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000006b6c                __device_dts_ord_90
 ._device.static.1_40_
                0x0000000000006b84       0x30 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                0x0000000000006b84                __device_dts_ord_10
                0x0000000000006b9c                __device_dts_ord_14
 ._device.static.1_50_
                0x0000000000006bb4       0x18 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                0x0000000000006bb4                __device_dts_ord_127
 ._device.static.3_50_
                0x0000000000006bcc       0x18 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                0x0000000000006bcc                __device_dts_ord_102
                0x0000000000006be4                _device_list_end = .

sw_isr_table    0x0000000000006be4      0x228
                0x0000000000006be4                . = ALIGN (0x4)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.sw_isr_table*))
 .gnu.linkonce.sw_isr_table
                0x0000000000006be4      0x228 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                0x0000000000006be4                _sw_isr_table

initlevel_error
                0x0000000000006afc        0x0
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_[_A-Z0-9]*)))
                0x0000000000000001                ASSERT ((SIZEOF (initlevel_error) == 0x0), Undefined initialization levels used.)

app_shmem_regions
                0x0000000000006e0c        0x0
                0x0000000000006e0c                __app_shmem_regions_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.app_regions.*)))
                0x0000000000006e0c                __app_shmem_regions_end = .

k_p4wq_initparam_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _k_p4wq_initparam_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_p4wq_initparam.static.*)))
                0x0000000000006e0c                _k_p4wq_initparam_list_end = .

_static_thread_data_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                __static_thread_data_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.__static_thread_data.static.*)))
                0x0000000000006e0c                __static_thread_data_list_end = .

device_deps     0x0000000000006e0c        0x0
                0x0000000000006e0c                __device_deps_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.__device_deps_pass2*)))
                0x0000000000006e0c                __device_deps_end = .

ztest           0x0000000000006e0c        0x0
                0x0000000000006e0c                _ztest_expected_result_entry_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_expected_result_entry.static.*)))
                0x0000000000006e0c                _ztest_expected_result_entry_list_end = .
                0x0000000000006e0c                _ztest_suite_node_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_suite_node.static.*)))
                0x0000000000006e0c                _ztest_suite_node_list_end = .
                0x0000000000006e0c                _ztest_unit_test_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_unit_test.static.*)))
                0x0000000000006e0c                _ztest_unit_test_list_end = .
                0x0000000000006e0c                _ztest_test_rule_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_test_rule.static.*)))
                0x0000000000006e0c                _ztest_test_rule_list_end = .

init_array      0x0000000000006e0c        0x0
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.ctors*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.init_array*)))
                0x0000000000000001                ASSERT ((SIZEOF (init_array) == 0x0), GNU-style constructors required but STATIC_INIT_GNU not enabled)

bt_l2cap_fixed_chan_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _bt_l2cap_fixed_chan_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_l2cap_fixed_chan.static.*)))
                0x0000000000006e0c                _bt_l2cap_fixed_chan_list_end = .

bt_gatt_service_static_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _bt_gatt_service_static_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_gatt_service_static.static.*)))
                0x0000000000006e0c                _bt_gatt_service_static_list_end = .

log_strings_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _log_strings_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_strings.static.*)))
                0x0000000000006e0c                _log_strings_list_end = .

log_const_area  0x0000000000006e0c        0x0
                0x0000000000006e0c                _log_const_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_const.static.*)))
                0x0000000000006e0c                _log_const_list_end = .

log_backend_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _log_backend_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_backend.static.*)))
                0x0000000000006e0c                _log_backend_list_end = .

log_link_area   0x0000000000006e0c        0x0
                0x0000000000006e0c                _log_link_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_link.static.*)))
                0x0000000000006e0c                _log_link_list_end = .

tracing_backend_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _tracing_backend_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._tracing_backend.static.*)))
                0x0000000000006e0c                _tracing_backend_list_end = .

zephyr_dbg_info
 *(SORT_BY_ALIGNMENT(.dbg_thread_info))

intc_table_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _intc_table_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._intc_table.static.*)))
                0x0000000000006e0c                _intc_table_list_end = .

symbol_to_keep  0x0000000000006e0c        0x0
                0x0000000000006e0c                __symbol_to_keep_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.symbol_to_keep*)))
                0x0000000000006e0c                __symbol_to_keep_end = .

shell_area      0x0000000000006e0c        0x0
                0x0000000000006e0c                _shell_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell.static.*)))
                0x0000000000006e0c                _shell_list_end = .

shell_root_cmds_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _shell_root_cmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_root_cmds.static.*)))
                0x0000000000006e0c                _shell_root_cmds_list_end = .

shell_subcmds_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _shell_subcmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_subcmds.static.*)))
                0x0000000000006e0c                _shell_subcmds_list_end = .

shell_dynamic_subcmds_area
                0x0000000000006e0c        0x0
                0x0000000000006e0c                _shell_dynamic_subcmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_dynamic_subcmds.static.*)))
                0x0000000000006e0c                _shell_dynamic_subcmds_list_end = .

cfb_font_area   0x0000000000006e0c        0x0
                0x0000000000006e0c                _cfb_font_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._cfb_font.static.*)))
                0x0000000000006e0c                _cfb_font_list_end = .

tdata           0x0000000000006e0c        0x0
 *(SORT_BY_ALIGNMENT(.tdata) SORT_BY_ALIGNMENT(.tdata.*) SORT_BY_ALIGNMENT(.gnu.linkonce.td.*))

tbss            0x0000000000006e0c        0x4
 *(SORT_BY_ALIGNMENT(.tbss) SORT_BY_ALIGNMENT(.tbss.*) SORT_BY_ALIGNMENT(.gnu.linkonce.tb.*) SORT_BY_ALIGNMENT(.tcommon))
 .tbss.z_tls_current
                0x0000000000006e0c        0x4 zephyr/libzephyr.a(thread_entry.c.obj)
                0x0000000000006e0c                z_tls_current
                0x0000000000006e0c                PROVIDE (__tdata_start = LOADADDR (tdata))
                0x0000000000000001                PROVIDE (__tdata_align = ALIGNOF (tdata))
                0x0000000000000000                PROVIDE (__tdata_size = (((SIZEOF (tdata) + __tdata_align) - 0x1) & ~ ((__tdata_align - 0x1))))
                [!provide]                        PROVIDE (__tdata_end = (__tdata_start + __tdata_size))
                0x0000000000000004                PROVIDE (__tbss_align = ALIGNOF (tbss))
                [!provide]                        PROVIDE (__tbss_start = ADDR (tbss))
                0x0000000000000004                PROVIDE (__tbss_size = (((SIZEOF (tbss) + __tbss_align) - 0x1) & ~ ((__tbss_align - 0x1))))
                [!provide]                        PROVIDE (__tbss_end = (__tbss_start + __tbss_size))
                [!provide]                        PROVIDE (__tls_start = __tdata_start)
                [!provide]                        PROVIDE (__tls_end = __tbss_end)
                [!provide]                        PROVIDE (__tls_size = (__tbss_end - __tdata_start))

rodata          0x0000000000006e10      0x8ec
 *(SORT_BY_ALIGNMENT(.rodata))
 *(SORT_BY_ALIGNMENT(.rodata.*))
 .rodata.delay_machine_code.1
                0x0000000000006e10        0x6 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 *fill*         0x0000000000006e16        0x2 
 .rodata.mem_attr_region
                0x0000000000006e18        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .rodata.apis   0x0000000000006e18        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .rodata.mutexes
                0x0000000000006e20       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.mutex_apis
                0x0000000000006e34       0x10 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.static_regions
                0x0000000000006e44        0xc zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .rodata.mpu_config
                0x0000000000006e50        0x8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                0x0000000000006e50                mpu_config
 .rodata.mpu_regions
                0x0000000000006e58       0x20 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .rodata.transitions.0
                0x0000000000006e78        0xc zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .rodata.transitions.0
                0x0000000000006e84        0xc zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.config
                0x0000000000006e90       0x20 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.clock_control_api
                0x0000000000006eb0       0x1c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.gpio_nrfx_p1_cfg
                0x0000000000006ecc       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.gpio_nrfx_p0_cfg
                0x0000000000006ee4       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.gpio_nrfx_drv_api_funcs
                0x0000000000006efc       0x24 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.twim_1z_config
                0x0000000000006f20       0x30 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_dev_config__device_dts_ord_102
                0x0000000000006f50        0xc zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_states__device_dts_ord_102
                0x0000000000006f5c       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_state_pins_1__device_dts_ord_102
                0x0000000000006f6c        0x8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_state_pins_0__device_dts_ord_102
                0x0000000000006f74        0x8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.i2c_nrfx_twim_driver_api
                0x0000000000006f7c       0x18 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.uarte_0z_config
                0x0000000000006f94       0x24 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_dev_config__device_dts_ord_127
                0x0000000000006fb8        0xc zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_states__device_dts_ord_127
                0x0000000000006fc4       0x10 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_state_pins_1__device_dts_ord_127
                0x0000000000006fd4        0x8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_state_pins_0__device_dts_ord_127
                0x0000000000006fdc        0x8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.uart_nrfx_uarte_driver_api
                0x0000000000006fe4        0xc zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.levels.0
                0x0000000000006ff0       0x18 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.CSWTCH.10
                0x0000000000007008       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .rodata.mutex_free.str1.4
                0x0000000000007028       0x26 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x000000000000704e        0x2 
 .rodata.mutex_init.str1.4
                0x0000000000007050       0x23 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x0000000000007073        0x1 
 .rodata.CC_PalPowerSaveModeInit.str1.4
                0x0000000000007074       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .rodata.main.str1.1
                0x0000000000007094      0x517 app/libapp.a(main_temperature_working.c.obj)
                                        0x519 (size before relaxing)
 .rodata.action_expected_state
                0x00000000000075ab        0x4 zephyr/libzephyr.a(device.c.obj)
 .rodata.action_target_state
                0x00000000000075af        0x4 zephyr/libzephyr.a(device.c.obj)
 .rodata.boot_banner.str1.1
                0x00000000000075b3       0x63 zephyr/libzephyr.a(banner.c.obj)
 .rodata.mutex_free_platform.str1.1
                0x0000000000007616       0x26 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.mutex_init_platform.str1.1
                0x000000000000763c       0x2d zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.str1.1
                0x0000000000007669        0xf zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .rodata.forwarded_psels.0
                0x0000000000007678        0xc zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .rodata.CSWTCH.4
                0x0000000000007684        0x4 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .rodata.str1.1
                0x0000000000007688        0xb zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.str1.1
                0x0000000000007693       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.str1.1
                0x00000000000076ab        0x9 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.drive_modes
                0x00000000000076b4        0x9 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .rodata.str1.1
                0x00000000000076bd        0xa zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.CSWTCH.17
                0x00000000000076c7        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.5
                0x00000000000076cb        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.3
                0x00000000000076cf        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.port_offset.1
                0x00000000000076d3       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.ports  0x00000000000076e3        0x2 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.z_cstart.str1.1
                0x00000000000076e5        0x5 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.__l_vfprintf.str1.1
                0x00000000000076ea        0xf c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.r.*))
                0x00000000000076fc                . = ALIGN (0x4)
 *fill*         0x00000000000076f9        0x3 
                0x00000000000076fc                __rodata_region_end = .
                0x0000000000007700                . = ALIGN (_region_min_align)
                0x0000000000007700                __rom_region_end = ((__rom_region_start + .) - ADDR (rom_start))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.got.plt))
 *(SORT_BY_ALIGNMENT(.igot.plt))
 *(SORT_BY_ALIGNMENT(.got))
 *(SORT_BY_ALIGNMENT(.igot))
                0x0000000020000000                . = 0x20000000
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                _image_ram_start = .

.ramfunc        0x0000000020000000        0x0 load address 0x00000000000076fc
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                __ramfunc_start = .
 *(SORT_BY_ALIGNMENT(.ramfunc))
 *(SORT_BY_ALIGNMENT(.ramfunc.*))
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                __ramfunc_end = .
                0x0000000000000000                __ramfunc_size = (__ramfunc_end - __ramfunc_start)
                0x00000000000076fc                __ramfunc_load_start = LOADADDR (.ramfunc)

datas           0x0000000020000000      0x15c load address 0x00000000000076fc
                0x0000000020000000                __data_region_start = .
                0x0000000020000000                __data_start = .
 *(SORT_BY_ALIGNMENT(.data))
 *(SORT_BY_ALIGNMENT(.data.*))
 .data._char_out
                0x0000000020000000        0x4 zephyr/libzephyr.a(printk.c.obj)
 .data.power_mutex
                0x0000000020000004        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.rng_mutex
                0x000000002000000c        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.asym_mutex
                0x0000000020000014        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.sym_mutex
                0x000000002000001c        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.__stdout
                0x0000000020000024       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .data.__pm_device_dts_ord_102
                0x0000000020000034       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .data.__pm_device_dts_ord_127
                0x0000000020000044       0x10 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .data.SystemCoreClock
                0x0000000020000054        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                0x0000000020000054                SystemCoreClock
 .data.dppi     0x0000000020000058        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .data.m_cb     0x0000000020000060       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .data.m_cb     0x0000000020000070       0x84 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .data.timeout_list
                0x00000000200000f4        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
 .data.platform_abort_apis
                0x00000000200000fc        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x00000000200000fc                platform_abort_apis
 .data.platform_mutexes
                0x0000000020000104       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000020000104                platform_mutexes
 .data.platform_mutex_apis
                0x0000000020000118       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000020000118                platform_mutex_apis
 .data.power_mutex
                0x0000000020000128        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.rng_mutex
                0x0000000020000130        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.asym_mutex
                0x0000000020000138        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.sym_mutex
                0x0000000020000140        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.pCCRndCryptoMutex
                0x0000000020000148        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000148                pCCRndCryptoMutex
 .data.CCPowerMutex
                0x000000002000014c        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x000000002000014c                CCPowerMutex
 .data.CCRndCryptoMutex
                0x0000000020000150        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000150                CCRndCryptoMutex
 .data.CCAsymCryptoMutex
                0x0000000020000154        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000154                CCAsymCryptoMutex
 .data.CCSymCryptoMutex
                0x0000000020000158        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000158                CCSymCryptoMutex
 *(SORT_BY_ALIGNMENT(.kernel.*))
                0x000000002000015c                __data_end = .
                0x000000000000015c                __data_size = (__data_end - __data_start)
                0x00000000000076fc                __data_load_start = LOADADDR (datas)
                0x00000000000076fc                __data_region_load_start = LOADADDR (datas)

device_states   0x000000002000015c        0xa load address 0x0000000000007858
                0x000000002000015c                __device_states_start = .
 *(SORT_BY_ALIGNMENT(.z_devstate))
 .z_devstate    0x000000002000015c        0x2 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .z_devstate    0x000000002000015e        0x4 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_devstate    0x0000000020000162        0x2 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .z_devstate    0x0000000020000164        0x2 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 *(SORT_BY_ALIGNMENT(.z_devstate.*))
                0x0000000020000166                __device_states_end = .

pm_device_slots_area
                0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _pm_device_slots_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._pm_device_slots.static.*)))
                0x0000000020000166                _pm_device_slots_list_end = .

log_mpsc_pbuf_area
                0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _log_mpsc_pbuf_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_mpsc_pbuf.static.*)))
                0x0000000020000166                _log_mpsc_pbuf_list_end = .

log_msg_ptr_area
                0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _log_msg_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_msg_ptr.static.*)))
                0x0000000020000166                _log_msg_ptr_list_end = .

log_dynamic_area
                0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _log_dynamic_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_dynamic.static.*)))
                0x0000000020000166                _log_dynamic_list_end = .

k_timer_area    0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _k_timer_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_timer.static.*)))
                0x0000000020000166                _k_timer_list_end = .

k_mem_slab_area
                0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _k_mem_slab_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mem_slab.static.*)))
                0x0000000020000166                _k_mem_slab_list_end = .

k_heap_area     0x0000000020000166        0x0 load address 0x0000000000007862
                0x0000000020000166                _k_heap_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_heap.static.*)))
                0x0000000020000166                _k_heap_list_end = .

k_mutex_area    0x0000000020000168       0x50 load address 0x0000000000007864
                0x0000000020000168                _k_mutex_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mutex.static.*)))
 ._k_mutex.static.asym_mutex_int_
                0x0000000020000168       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000168                asym_mutex_int
 ._k_mutex.static.power_mutex_int_
                0x000000002000017c       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x000000002000017c                power_mutex_int
 ._k_mutex.static.rng_mutex_int_
                0x0000000020000190       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000190                rng_mutex_int
 ._k_mutex.static.sym_mutex_int_
                0x00000000200001a4       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200001a4                sym_mutex_int
                0x00000000200001b8                _k_mutex_list_end = .

k_stack_area    0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_stack_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_stack.static.*)))
                0x00000000200001b8                _k_stack_list_end = .

k_msgq_area     0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_msgq_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_msgq.static.*)))
                0x00000000200001b8                _k_msgq_list_end = .

k_mbox_area     0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_mbox_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mbox.static.*)))
                0x00000000200001b8                _k_mbox_list_end = .

k_pipe_area     0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_pipe_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_pipe.static.*)))
                0x00000000200001b8                _k_pipe_list_end = .

k_sem_area      0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_sem_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_sem.static.*)))
                0x00000000200001b8                _k_sem_list_end = .

k_event_area    0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_event_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_event.static.*)))
                0x00000000200001b8                _k_event_list_end = .

k_queue_area    0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_queue_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_queue.static.*)))
                0x00000000200001b8                _k_queue_list_end = .

k_fifo_area     0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_fifo_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_fifo.static.*)))
                0x00000000200001b8                _k_fifo_list_end = .

k_lifo_area     0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_lifo_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_lifo.static.*)))
                0x00000000200001b8                _k_lifo_list_end = .

k_condvar_area  0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _k_condvar_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_condvar.static.*)))
                0x00000000200001b8                _k_condvar_list_end = .

sys_mem_blocks_ptr_area
                0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _sys_mem_blocks_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._sys_mem_blocks_ptr.static.*)))
                0x00000000200001b8                _sys_mem_blocks_ptr_list_end = .

net_buf_pool_area
                0x00000000200001b8        0x0 load address 0x00000000000078b4
                0x00000000200001b8                _net_buf_pool_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._net_buf_pool.static.*)))
                0x00000000200001b8                _net_buf_pool_list_end = .
                0x00000000200001b8                __data_region_end = .

bss             0x00000000200001b8      0xa86
                0x00000000200001b8                . = ALIGN (0x4)
                0x00000000200001b8                __bss_start = .
                0x00000000200001b8                __kernel_ram_start = .
 *(SORT_BY_ALIGNMENT(.bss))
 *(SORT_BY_ALIGNMENT(.bss.*))
 .bss.cc_data   0x00000000200001b8       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.last_count
                0x00000000200001c8        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.anchor    0x00000000200001d0        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.z_idle_threads
                0x00000000200001d8       0x80 zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000200001d8                z_idle_threads
 .bss.z_main_thread
                0x0000000020000258       0x80 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000258                z_main_thread
 .bss._thread_dummy
                0x00000000200002d8       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000200002d8                _thread_dummy
 .bss.slice_timeouts
                0x0000000020000358       0x18 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss.curr_tick
                0x0000000020000370        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss.i2c_dev   0x0000000020000378        0x4 app/libapp.a(main_temperature_working.c.obj)
 .bss.uart_dev  0x000000002000037c        0x4 app/libapp.a(main_temperature_working.c.obj)
 .bss.i2c_dev   0x0000000020000380        0x4 app/libapp.a(zephyr_i2c_driver.c.obj)
 .bss.mutex_slab_buffer
                0x0000000020000384      0x500 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000384                mutex_slab_buffer
 .bss.mutex_slab
                0x0000000020000884       0x1c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000884                mutex_slab
 .bss.z_arm_tls_ptr
                0x00000000200008a0        0x4 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                0x00000000200008a0                z_arm_tls_ptr
 .bss.dyn_reg_info
                0x00000000200008a4       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss._stdout_hook
                0x00000000200008b8        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .bss.z_malloc_heap
                0x00000000200008bc        0xc zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .bss.cpunet_mgr
                0x00000000200008c8       0x1c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .bss.cli.1     0x00000000200008e4       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.on.2      0x00000000200008f4        0x4 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.data      0x00000000200008f8       0xa0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.hfclk_users
                0x0000000020000998        0x4 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.gpio_nrfx_p1_data
                0x000000002000099c        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss.gpio_nrfx_p0_data
                0x00000000200009a8        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss.twim_1_data
                0x00000000200009b4       0x24 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .bss.uarte_0_data
                0x00000000200009d8        0xc zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss.force_isr_mask
                0x00000000200009e4        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.int_mask  0x00000000200009e8        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.overflow_cnt
                0x00000000200009ec        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.m_clock_cb
                0x00000000200009f0        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .bss.m_cb      0x00000000200009f8       0x28 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .bss._kernel   0x0000000020000a20       0x20 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000a20                _kernel
 .bss.pending_current
                0x0000000020000a40        0x4 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x0000000020000a40                pending_current
 .bss.slice_max_prio
                0x0000000020000a44        0x4 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss.slice_ticks
                0x0000000020000a48        0x4 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss.announce_remaining
                0x0000000020000a4c        0x4 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss.nrf_cc3xx_platform_initialized
                0x0000000020000a50        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss.power_mutex_int
                0x0000000020000a54        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.rng_mutex_int
                0x0000000020000a58        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.asym_mutex_int
                0x0000000020000a5c        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.sym_mutex_int
                0x0000000020000a60        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.use_count
                0x0000000020000a64        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .bss.nrf_cc3xx_platform_ctr_drbg_global_ctx
                0x0000000020000a68      0x1c0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                0x0000000020000a68                nrf_cc3xx_platform_ctr_drbg_global_ctx
 .bss.m117_initialized
                0x0000000020000c28        0x1 app/libapp.a(main_temperature_working.c.obj)
 .bss.static_regions_num
                0x0000000020000c29        0x1 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss.twim_1_msg_buf
                0x0000000020000c2a       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .bss.uarte0_poll_in_byte
                0x0000000020000c3a        0x1 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss.uarte0_poll_out_byte
                0x0000000020000c3b        0x1 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss.z_sys_post_kernel
                0x0000000020000c3c        0x1 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000c3c                z_sys_post_kernel
 .bss.lock      0x0000000020000c3d        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .bss.lock      0x0000000020000c3d        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .bss._sched_spinlock
                0x0000000020000c3d        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000020000c3d                _sched_spinlock
 .bss.slice_expired
                0x0000000020000c3d        0x1 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 *(SORT_BY_ALIGNMENT(COMMON))
 *(SORT_BY_ALIGNMENT(.kernel_bss.*))
                0x0000000020000c40                __bss_end = ALIGN (0x4)

noinit          0x0000000020000c40      0xd40
 *(SORT_BY_ALIGNMENT(.noinit))
 *(SORT_BY_ALIGNMENT(.noinit.*))
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".2
                0x0000000020000c40      0x800 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000c40                z_interrupt_stacks
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".1
                0x0000000020001440      0x140 zephyr/kernel/libkernel.a(init.c.obj)
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".0
                0x0000000020001580      0x400 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020001580                z_main_stack
 *(SORT_BY_ALIGNMENT(.kernel_noinit.*))
                0x0000000020070000                __kernel_ram_end = 0x20070000
                0x000000000006fe48                __kernel_ram_size = (__kernel_ram_end - __kernel_ram_start)
                0x0000000000002ef0                PROVIDE (soc_reset_hook = SystemInit)

/DISCARD/
 *(SORT_BY_ALIGNMENT(.irq_info*))
 *(SORT_BY_ALIGNMENT(.intList*))

.last_ram_section
                0x0000000020001980        0x0
                0x0000000020001980                _image_ram_end = .
                0x0000000000001980                _image_ram_size = (_image_ram_end - _image_ram_start)
                0x0000000020001980                _end = .
                0x0000000020001980                z_mapped_end = .

.stab
 *(SORT_BY_ALIGNMENT(.stab))

.stabstr
 *(SORT_BY_ALIGNMENT(.stabstr))

.stab.excl
 *(SORT_BY_ALIGNMENT(.stab.excl))

.stab.exclstr
 *(SORT_BY_ALIGNMENT(.stab.exclstr))

.stab.index
 *(SORT_BY_ALIGNMENT(.stab.index))

.stab.indexstr
 *(SORT_BY_ALIGNMENT(.stab.indexstr))

.gnu.build.attributes
 *(SORT_BY_ALIGNMENT(.gnu.build.attributes) SORT_BY_ALIGNMENT(.gnu.build.attributes.*))

.comment        0x0000000000000000       0x40
 *(SORT_BY_ALIGNMENT(.comment))
 .comment       0x0000000000000000       0x20 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                                         0x21 (size before relaxing)
 .comment       0x0000000000000020       0x21 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .comment       0x0000000000000020       0x21 app/libapp.a(main_temperature_working.c.obj)
 .comment       0x0000000000000020       0x21 app/libapp.a(zephyr_i2c_driver.c.obj)
 .comment       0x0000000000000020       0x21 app/libapp.a(m117_sensor.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(heap.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(printk.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(thread_entry.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(poweroff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(onoff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(notify.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(configs.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(mem_attr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(device.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(banner.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .comment       0x0000000000000020       0x21 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(device.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(fatal.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(init.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(init_static.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(idle.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(mutex.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(sem.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(thread.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(sched.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(xip.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(timeout.c.obj)
 .comment       0x0000000000000020       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                                         0x21 (size before relaxing)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)

.debug
 *(SORT_BY_ALIGNMENT(.debug))

.line
 *(SORT_BY_ALIGNMENT(.line))

.debug_srcinfo
 *(SORT_BY_ALIGNMENT(.debug_srcinfo))

.debug_sfnames
 *(SORT_BY_ALIGNMENT(.debug_sfnames))

.debug_aranges  0x0000000000000000     0x1728
 *(SORT_BY_ALIGNMENT(.debug_aranges))
 .debug_aranges
                0x0000000000000000       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_aranges
                0x0000000000000020       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_aranges
                0x0000000000000040       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_aranges
                0x0000000000000060       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_aranges
                0x0000000000000088       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_aranges
                0x00000000000000a8       0x18 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_aranges
                0x00000000000000c0       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_aranges
                0x00000000000000e0       0x20 app/libapp.a(main_temperature_working.c.obj)
 .debug_aranges
                0x0000000000000100       0x48 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_aranges
                0x0000000000000148       0x38 app/libapp.a(m117_sensor.c.obj)
 .debug_aranges
                0x0000000000000180       0xb8 zephyr/libzephyr.a(heap.c.obj)
 .debug_aranges
                0x0000000000000238       0x50 zephyr/libzephyr.a(printk.c.obj)
 .debug_aranges
                0x0000000000000288       0x20 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_aranges
                0x00000000000002a8       0x20 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_aranges
                0x00000000000002c8       0x90 zephyr/libzephyr.a(onoff.c.obj)
 .debug_aranges
                0x0000000000000358       0x28 zephyr/libzephyr.a(notify.c.obj)
 .debug_aranges
                0x0000000000000380       0x20 zephyr/libzephyr.a(configs.c.obj)
 .debug_aranges
                0x00000000000003a0       0x28 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_aranges
                0x00000000000003c8       0x90 zephyr/libzephyr.a(device.c.obj)
 .debug_aranges
                0x0000000000000458       0x20 zephyr/libzephyr.a(banner.c.obj)
 .debug_aranges
                0x0000000000000478       0x28 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_aranges
                0x00000000000004a0       0x40 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_aranges
                0x00000000000004e0       0x30 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_aranges
                0x0000000000000510       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_aranges
                0x0000000000000530       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_aranges
                0x0000000000000550       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_aranges
                0x0000000000000570       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_aranges
                0x00000000000005b0       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_aranges
                0x00000000000005e0       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_aranges
                0x0000000000000600       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_aranges
                0x0000000000000628       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_aranges
                0x0000000000000668       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_aranges
                0x0000000000000688       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_aranges
                0x00000000000006c8       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_aranges
                0x00000000000006f8       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_aranges
                0x0000000000000718       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_aranges
                0x0000000000000738       0x40 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_aranges
                0x0000000000000778       0x28 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_aranges
                0x00000000000007a0       0x68 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_aranges
                0x0000000000000808       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_aranges
                0x0000000000000820       0xb0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_aranges
                0x00000000000008d0       0x60 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_aranges
                0x0000000000000930       0x38 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_aranges
                0x0000000000000968       0x38 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_aranges
                0x00000000000009a0       0x20 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_aranges
                0x00000000000009c0       0xe0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_aranges
                0x0000000000000aa0       0x30 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_aranges
                0x0000000000000ad0       0x68 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_aranges
                0x0000000000000b38       0x38 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_aranges
                0x0000000000000b70       0x48 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_aranges
                0x0000000000000bb8       0x20 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_aranges
                0x0000000000000bd8       0x28 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_aranges
                0x0000000000000c00       0x58 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_aranges
                0x0000000000000c58       0xd8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_aranges
                0x0000000000000d30       0x28 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_aranges
                0x0000000000000d58       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_aranges
                0x0000000000000d98       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_aranges
                0x0000000000000dc8       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_aranges
                0x0000000000000df8       0xf0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_aranges
                0x0000000000000ee8       0x70 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_aranges
                0x0000000000000f58       0x78 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_aranges
                0x0000000000000fd0      0x188 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_aranges
                0x0000000000001158       0xa0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_aranges
                0x00000000000011f8       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_aranges
                0x0000000000001228       0x20 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_aranges
                0x0000000000001248       0x38 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_aranges
                0x0000000000001280       0x38 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_aranges
                0x00000000000012b8       0x68 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_aranges
                0x0000000000001320       0x20 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_aranges
                0x0000000000001340       0x48 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_aranges
                0x0000000000001388       0x28 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_aranges
                0x00000000000013b0       0x38 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_aranges
                0x00000000000013e8       0x38 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_aranges
                0x0000000000001420       0x90 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_aranges
                0x00000000000014b0      0x188 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_aranges
                0x0000000000001638       0x40 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_aranges
                0x0000000000001678       0x20 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_aranges
                0x0000000000001698       0x90 zephyr/kernel/libkernel.a(timeout.c.obj)

.debug_pubnames
 *(SORT_BY_ALIGNMENT(.debug_pubnames))

.debug_info     0x0000000000000000    0x503c4
 *(SORT_BY_ALIGNMENT(.debug_info) SORT_BY_ALIGNMENT(.gnu.linkonce.wi.*))
 .debug_info    0x0000000000000000      0x189 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_info    0x0000000000000189       0xdc zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_info    0x0000000000000265      0xd4f app/libapp.a(main_temperature_working.c.obj)
 .debug_info    0x0000000000000fb4      0xc21 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_info    0x0000000000001bd5      0xa2c app/libapp.a(m117_sensor.c.obj)
 .debug_info    0x0000000000002601     0x2922 zephyr/libzephyr.a(heap.c.obj)
 .debug_info    0x0000000000004f23      0x579 zephyr/libzephyr.a(printk.c.obj)
 .debug_info    0x000000000000549c      0x750 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_info    0x0000000000005bec      0x1a3 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_info    0x0000000000005d8f     0x28e3 zephyr/libzephyr.a(onoff.c.obj)
 .debug_info    0x0000000000008672      0x31d zephyr/libzephyr.a(notify.c.obj)
 .debug_info    0x000000000000898f       0x38 zephyr/libzephyr.a(configs.c.obj)
 .debug_info    0x00000000000089c7      0x2ba zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_info    0x0000000000008c81      0xdc1 zephyr/libzephyr.a(device.c.obj)
 .debug_info    0x0000000000009a42       0xdc zephyr/libzephyr.a(banner.c.obj)
 .debug_info    0x0000000000009b1e      0x246 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_info    0x0000000000009d64     0x1083 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_info    0x000000000000ade7      0x6b1 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_info    0x000000000000b498       0xb6 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_info    0x000000000000b54e       0x23 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_info    0x000000000000b571      0x72a zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_info    0x000000000000bc9b      0x94a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_info    0x000000000000c5e5     0x16f7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_info    0x000000000000dcdc       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_info    0x000000000000dcff       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_info    0x000000000000dd22      0x958 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_info    0x000000000000e67a      0x9f1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_info    0x000000000000f06b      0xb96 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_info    0x000000000000fc01       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_info    0x000000000000fc23      0xa85 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_info    0x00000000000106a8      0x596 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_info    0x0000000000010c3e      0xb2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_info    0x000000000001176a      0x62b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_info    0x0000000000011d95      0x73a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_info    0x00000000000124cf      0x1a3 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_info    0x0000000000012672       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_info    0x0000000000012695      0x630 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_info    0x0000000000012cc5      0x78e zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_info    0x0000000000013453     0x19a4 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_info    0x0000000000014df7      0x1f1 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_info    0x0000000000014fe8      0xf7d zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_info    0x0000000000015f65      0xe5a zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_info    0x0000000000016dbf     0x13a7 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_info    0x0000000000018166      0xde5 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_info    0x0000000000018f4b      0x2fe zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_info    0x0000000000019249     0x3990 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_info    0x000000000001cbd9      0x7a2 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_info    0x000000000001d37b     0x28dd zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_info    0x000000000001fc58     0x1ad6 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_info    0x000000000002172e     0x1534 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_info    0x0000000000022c62      0x1af zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_info    0x0000000000022e11     0x1caf zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_info    0x0000000000024ac0     0x35d1 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_info    0x0000000000028091     0x3644 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_info    0x000000000002b6d5      0x3cd modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_info    0x000000000002baa2     0x1a52 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_info    0x000000000002d4f4      0x25d modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_info    0x000000000002d751      0x354 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_info    0x000000000002daa5     0x1630 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_info    0x000000000002f0d5     0x2863 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_info    0x0000000000031938     0x1bbe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_info    0x00000000000334f6     0x63a5 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_info    0x000000000003989b     0x4eee modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_info    0x000000000003e789      0xa0f modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_info    0x000000000003f198       0xe3 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_info    0x000000000003f27b      0x39b zephyr/kernel/libkernel.a(device.c.obj)
 .debug_info    0x000000000003f616      0xcd5 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_info    0x00000000000402eb     0x203f zephyr/kernel/libkernel.a(init.c.obj)
 .debug_info    0x000000000004232a       0x38 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_info    0x0000000000042362     0x12a7 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_info    0x0000000000043609      0x2e8 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_info    0x00000000000438f1     0x14d5 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_info    0x0000000000044dc6      0xefc zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_info    0x0000000000045cc2     0x1588 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_info    0x000000000004724a     0x64d4 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_info    0x000000000004d71e      0xf80 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_info    0x000000000004e69e      0x142 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_info    0x000000000004e7e0     0x1be4 zephyr/kernel/libkernel.a(timeout.c.obj)

.debug_abbrev   0x0000000000000000     0xe54f
 *(SORT_BY_ALIGNMENT(.debug_abbrev))
 .debug_abbrev  0x0000000000000000      0x118 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_abbrev  0x0000000000000118       0x62 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_abbrev  0x000000000000017a      0x392 app/libapp.a(main_temperature_working.c.obj)
 .debug_abbrev  0x000000000000050c      0x348 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_abbrev  0x0000000000000854      0x2d6 app/libapp.a(m117_sensor.c.obj)
 .debug_abbrev  0x0000000000000b2a      0x449 zephyr/libzephyr.a(heap.c.obj)
 .debug_abbrev  0x0000000000000f73      0x2e2 zephyr/libzephyr.a(printk.c.obj)
 .debug_abbrev  0x0000000000001255      0x2bc zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_abbrev  0x0000000000001511      0x152 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_abbrev  0x0000000000001663      0x54f zephyr/libzephyr.a(onoff.c.obj)
 .debug_abbrev  0x0000000000001bb2      0x1bf zephyr/libzephyr.a(notify.c.obj)
 .debug_abbrev  0x0000000000001d71       0x2e zephyr/libzephyr.a(configs.c.obj)
 .debug_abbrev  0x0000000000001d9f      0x142 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_abbrev  0x0000000000001ee1      0x480 zephyr/libzephyr.a(device.c.obj)
 .debug_abbrev  0x0000000000002361       0x9c zephyr/libzephyr.a(banner.c.obj)
 .debug_abbrev  0x00000000000023fd      0x13d zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_abbrev  0x000000000000253a      0x406 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_abbrev  0x0000000000002940      0x2ba zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_abbrev  0x0000000000002bfa       0x70 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_abbrev  0x0000000000002c6a       0x14 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_abbrev  0x0000000000002c7e      0x2a1 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_abbrev  0x0000000000002f1f      0x19f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_abbrev  0x00000000000030be      0x420 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_abbrev  0x00000000000034de       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_abbrev  0x00000000000034f2       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_abbrev  0x0000000000003506      0x286 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_abbrev  0x000000000000378c      0x247 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_abbrev  0x00000000000039d3      0x2d0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_abbrev  0x0000000000003ca3       0x12 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_abbrev  0x0000000000003cb5      0x27d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_abbrev  0x0000000000003f32      0x18e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_abbrev  0x00000000000040c0      0x38a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_abbrev  0x000000000000444a      0x180 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_abbrev  0x00000000000045ca      0x159 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_abbrev  0x0000000000004723      0x136 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_abbrev  0x0000000000004859       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_abbrev  0x000000000000486d      0x1b2 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_abbrev  0x0000000000004a1f      0x21f zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_abbrev  0x0000000000004c3e      0x507 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_abbrev  0x0000000000005145      0x112 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_abbrev  0x0000000000005257      0x504 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_abbrev  0x000000000000575b      0x490 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_abbrev  0x0000000000005beb      0x45b zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_abbrev  0x0000000000006046      0x486 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_abbrev  0x00000000000064cc      0x141 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_abbrev  0x000000000000660d      0x7b7 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_abbrev  0x0000000000006dc4      0x3cf zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_abbrev  0x0000000000007193      0x5ec zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_abbrev  0x000000000000777f      0x502 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_abbrev  0x0000000000007c81      0x445 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_abbrev  0x00000000000080c6       0xd0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_abbrev  0x0000000000008196      0x2ca zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_abbrev  0x0000000000008460      0x5d8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_abbrev  0x0000000000008a38      0x71a zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_abbrev  0x0000000000009152      0x246 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_abbrev  0x0000000000009398      0x28f modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_abbrev  0x0000000000009627      0x155 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_abbrev  0x000000000000977c      0x10b modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_abbrev  0x0000000000009887      0x35b modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_abbrev  0x0000000000009be2      0x524 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_abbrev  0x000000000000a106      0x3f4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_abbrev  0x000000000000a4fa      0x5b4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_abbrev  0x000000000000aaae      0x51c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_abbrev  0x000000000000afca      0x2d0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_abbrev  0x000000000000b29a       0x9d zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_abbrev  0x000000000000b337      0x1fb zephyr/kernel/libkernel.a(device.c.obj)
 .debug_abbrev  0x000000000000b532      0x3c3 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_abbrev  0x000000000000b8f5      0x731 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_abbrev  0x000000000000c026       0x2e zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_abbrev  0x000000000000c054      0x576 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_abbrev  0x000000000000c5ca      0x1df zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_abbrev  0x000000000000c7a9      0x437 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_abbrev  0x000000000000cbe0      0x405 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_abbrev  0x000000000000cfe5      0x4de zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_abbrev  0x000000000000d4c3      0x67b zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_abbrev  0x000000000000db3e      0x42f zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_abbrev  0x000000000000df6d       0xbe zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_abbrev  0x000000000000e02b      0x524 zephyr/kernel/libkernel.a(timeout.c.obj)

.debug_line     0x0000000000000000    0x23a1c
 *(SORT_BY_ALIGNMENT(.debug_line) SORT_BY_ALIGNMENT(.debug_line.*) SORT_BY_ALIGNMENT(.debug_line_end))
 .debug_line    0x0000000000000000      0x160 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_line    0x0000000000000160      0x127 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_line    0x0000000000000287      0x5ce app/libapp.a(main_temperature_working.c.obj)
 .debug_line    0x0000000000000855      0x517 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_line    0x0000000000000d6c      0x43e app/libapp.a(m117_sensor.c.obj)
 .debug_line    0x00000000000011aa      0xfa9 zephyr/libzephyr.a(heap.c.obj)
 .debug_line    0x0000000000002153      0x3e8 zephyr/libzephyr.a(printk.c.obj)
 .debug_line    0x000000000000253b      0x3ce zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_line    0x0000000000002909      0x25c zephyr/libzephyr.a(poweroff.c.obj)
 .debug_line    0x0000000000002b65     0x16b4 zephyr/libzephyr.a(onoff.c.obj)
 .debug_line    0x0000000000004219      0x260 zephyr/libzephyr.a(notify.c.obj)
 .debug_line    0x0000000000004479      0x26d zephyr/libzephyr.a(configs.c.obj)
 .debug_line    0x00000000000046e6      0x258 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_line    0x000000000000493e      0x73a zephyr/libzephyr.a(device.c.obj)
 .debug_line    0x0000000000005078       0xb7 zephyr/libzephyr.a(banner.c.obj)
 .debug_line    0x000000000000512f      0x21c zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_line    0x000000000000534b      0x798 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_line    0x0000000000005ae3      0x353 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_line    0x0000000000005e36       0xb1 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_line    0x0000000000005ee7       0x6a zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_line    0x0000000000005f51      0x417 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_line    0x0000000000006368      0x342 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_line    0x00000000000066aa      0x941 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_line    0x0000000000006feb       0x77 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_line    0x0000000000007062       0x91 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_line    0x00000000000070f3      0x497 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_line    0x000000000000758a      0x401 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_line    0x000000000000798b      0x486 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_line    0x0000000000007e11       0xb3 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_line    0x0000000000007ec4      0x3a2 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_line    0x0000000000008266      0x31e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_line    0x0000000000008584      0x5c1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_line    0x0000000000008b45      0x336 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_line    0x0000000000008e7b      0x248 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_line    0x00000000000090c3      0x259 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_line    0x000000000000931c       0x78 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_line    0x0000000000009394      0x31a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_line    0x00000000000096ae      0x444 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_line    0x0000000000009af2      0xe7d zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_line    0x000000000000a96f      0x227 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_line    0x000000000000ab96      0x75b zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_line    0x000000000000b2f1      0x6b1 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_line    0x000000000000b9a2      0x66e zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_line    0x000000000000c010      0x4b0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_line    0x000000000000c4c0      0x23a zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_line    0x000000000000c6fa     0x15c0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_line    0x000000000000dcba      0x3d7 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_line    0x000000000000e091      0xdc9 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_line    0x000000000000ee5a      0x90a zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_line    0x000000000000f764      0x730 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_line    0x000000000000fe94      0x1e6 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_line    0x000000000001007a      0x5f2 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_line    0x000000000001066c     0x1279 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_line    0x00000000000118e5     0x16e0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_line    0x0000000000012fc5      0x25d modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_line    0x0000000000013222      0x595 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_line    0x00000000000137b7      0x27e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_line    0x0000000000013a35      0x2f9 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_line    0x0000000000013d2e      0x88b modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_line    0x00000000000145b9     0x10d0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_line    0x0000000000015689      0xc4d modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_line    0x00000000000162d6     0x230e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_line    0x00000000000185e4     0x1dd6 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_line    0x000000000001a3ba      0x510 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_line    0x000000000001a8ca      0x181 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_line    0x000000000001aa4b      0x395 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_line    0x000000000001ade0      0x64b zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_line    0x000000000001b42b      0xc95 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_line    0x000000000001c0c0       0x63 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_line    0x000000000001c123      0x95c zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_line    0x000000000001ca7f      0x330 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_line    0x000000000001cdaf      0x9e2 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_line    0x000000000001d791      0x7f9 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_line    0x000000000001df8a      0xaaa zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_line    0x000000000001ea34     0x34a9 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_line    0x0000000000021edd      0x711 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_line    0x00000000000225ee      0x22c zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_line    0x000000000002281a     0x1202 zephyr/kernel/libkernel.a(timeout.c.obj)

.debug_frame    0x0000000000000000     0x35ec
 *(SORT_BY_ALIGNMENT(.debug_frame))
 .debug_frame   0x0000000000000000       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_frame   0x0000000000000020       0x34 app/libapp.a(main_temperature_working.c.obj)
 .debug_frame   0x0000000000000054       0xbc app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_frame   0x0000000000000110       0x8c app/libapp.a(m117_sensor.c.obj)
 .debug_frame   0x000000000000019c      0x2a0 zephyr/libzephyr.a(heap.c.obj)
 .debug_frame   0x000000000000043c       0xbc zephyr/libzephyr.a(printk.c.obj)
 .debug_frame   0x00000000000004f8       0x28 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_frame   0x0000000000000520       0x28 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_frame   0x0000000000000548      0x190 zephyr/libzephyr.a(onoff.c.obj)
 .debug_frame   0x00000000000006d8       0x30 zephyr/libzephyr.a(notify.c.obj)
 .debug_frame   0x0000000000000708       0x20 zephyr/libzephyr.a(configs.c.obj)
 .debug_frame   0x0000000000000728       0x30 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_frame   0x0000000000000758      0x128 zephyr/libzephyr.a(device.c.obj)
 .debug_frame   0x0000000000000880       0x2c zephyr/libzephyr.a(banner.c.obj)
 .debug_frame   0x00000000000008ac       0x30 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_frame   0x00000000000008dc       0x90 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_frame   0x000000000000096c       0x48 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_frame   0x00000000000009b4       0x2c zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_frame   0x00000000000009e0       0x30 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_frame   0x0000000000000a10       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_frame   0x0000000000000a30       0x88 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_frame   0x0000000000000ab8       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_frame   0x0000000000000b00       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_frame   0x0000000000000b20       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_frame   0x0000000000000b50       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_frame   0x0000000000000bb0       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_frame   0x0000000000000bd8       0x68 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_frame   0x0000000000000c40       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_frame   0x0000000000000c80       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_frame   0x0000000000000ca0       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_frame   0x0000000000000ccc       0x70 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_frame   0x0000000000000d3c       0x3c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_frame   0x0000000000000d78      0x140 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_frame   0x0000000000000eb8      0x190 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_frame   0x0000000000001048       0xf0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_frame   0x0000000000001138       0x68 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_frame   0x00000000000011a0       0x80 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_frame   0x0000000000001220       0x20 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_frame   0x0000000000001240      0x264 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_frame   0x00000000000014a4       0x54 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_frame   0x00000000000014f8      0x104 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_frame   0x00000000000015fc       0x84 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_frame   0x0000000000001680       0xc4 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_frame   0x0000000000001744       0x2c zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_frame   0x0000000000001770       0x50 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_frame   0x00000000000017c0       0xe0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_frame   0x00000000000018a0      0x268 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_frame   0x0000000000001b08       0x3c modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_frame   0x0000000000001b44       0x68 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_frame   0x0000000000001bac       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_frame   0x0000000000001bf4       0x54 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_frame   0x0000000000001c48      0x1c0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_frame   0x0000000000001e08      0x10c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_frame   0x0000000000001f14      0x140 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_frame   0x0000000000002054      0x4c0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_frame   0x0000000000002514      0x1b8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_frame   0x00000000000026cc       0x5c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_frame   0x0000000000002728       0x20 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_frame   0x0000000000002748       0x5c zephyr/kernel/libkernel.a(device.c.obj)
 .debug_frame   0x00000000000027a4       0x74 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_frame   0x0000000000002818      0x114 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_frame   0x000000000000292c       0x20 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_frame   0x000000000000294c       0xb8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_frame   0x0000000000002a04       0x38 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_frame   0x0000000000002a3c       0x74 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_frame   0x0000000000002ab0       0x8c zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_frame   0x0000000000002b3c      0x15c zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_frame   0x0000000000002c98      0x4e8 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_frame   0x0000000000003180       0x94 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_frame   0x0000000000003214       0x2c zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_frame   0x0000000000003240      0x1ac zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_frame   0x00000000000033ec       0x28 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .debug_frame   0x0000000000003414       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .debug_frame   0x0000000000003434       0x28 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .debug_frame   0x000000000000345c       0x60 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .debug_frame   0x00000000000034bc       0xac c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x0000000000003568       0x4c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .debug_frame   0x00000000000035b4       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)

.debug_str      0x0000000000000000     0xde35
 *(SORT_BY_ALIGNMENT(.debug_str))
 .debug_str     0x0000000000000000      0x30e zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                                        0x351 (size before relaxing)
 .debug_str     0x000000000000030e      0x2d2 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                                        0x384 (size before relaxing)
 .debug_str     0x00000000000005e0      0x546 app/libapp.a(main_temperature_working.c.obj)
                                        0x8fe (size before relaxing)
 .debug_str     0x0000000000000b26      0x40d app/libapp.a(zephyr_i2c_driver.c.obj)
                                        0x7b2 (size before relaxing)
 .debug_str     0x0000000000000f33       0xe0 app/libapp.a(m117_sensor.c.obj)
                                        0x61d (size before relaxing)
 .debug_str     0x0000000000001013      0x3e7 zephyr/libzephyr.a(heap.c.obj)
                                        0x709 (size before relaxing)
 .debug_str     0x00000000000013fa      0x12a zephyr/libzephyr.a(printk.c.obj)
                                        0x608 (size before relaxing)
 .debug_str     0x0000000000001524      0x1fc zephyr/libzephyr.a(thread_entry.c.obj)
                                        0x796 (size before relaxing)
 .debug_str     0x0000000000001720       0x79 zephyr/libzephyr.a(poweroff.c.obj)
                                        0x2dd (size before relaxing)
 .debug_str     0x0000000000001799      0x4a2 zephyr/libzephyr.a(onoff.c.obj)
                                        0x837 (size before relaxing)
 .debug_str     0x0000000000001c3b       0x45 zephyr/libzephyr.a(notify.c.obj)
                                        0x359 (size before relaxing)
 .debug_str     0x0000000000001c80       0x46 zephyr/libzephyr.a(configs.c.obj)
                                        0x1e7 (size before relaxing)
 .debug_str     0x0000000000001cc6       0xab zephyr/libzephyr.a(mem_attr.c.obj)
                                        0x556 (size before relaxing)
 .debug_str     0x0000000000001d71      0x27c zephyr/libzephyr.a(device.c.obj)
                                        0x819 (size before relaxing)
 .debug_str     0x0000000000001fed       0x3e zephyr/libzephyr.a(banner.c.obj)
                                        0x281 (size before relaxing)
 .debug_str     0x000000000000202b      0x11d zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                                        0x591 (size before relaxing)
 .debug_str     0x0000000000002148      0x487 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                                        0xcbe (size before relaxing)
 .debug_str     0x00000000000025cf       0xe7 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                                        0x519 (size before relaxing)
 .debug_str     0x00000000000026b6       0x59 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                                        0x295 (size before relaxing)
 .debug_str     0x000000000000270f       0x45 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                                         0x5c (size before relaxing)
 .debug_str     0x0000000000002754       0xa4 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                                        0x78c (size before relaxing)
 .debug_str     0x00000000000027f8      0x1a2 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                                        0x676 (size before relaxing)
 .debug_str     0x000000000000299a      0x675 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                                        0xca3 (size before relaxing)
 .debug_str     0x000000000000300f       0x3d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                                         0x60 (size before relaxing)
 .debug_str     0x000000000000304c       0x3b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                                         0x5e (size before relaxing)
 .debug_str     0x0000000000003087      0x186 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                                        0x5aa (size before relaxing)
 .debug_str     0x000000000000320d       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                                        0x6c0 (size before relaxing)
 .debug_str     0x000000000000326d       0x5b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                                        0x6f3 (size before relaxing)
 .debug_str     0x00000000000032c8       0x41 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                                         0x64 (size before relaxing)
 .debug_str     0x0000000000003309      0x34c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                                        0x81d (size before relaxing)
 .debug_str     0x0000000000003655       0xb8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                                        0x4d0 (size before relaxing)
 .debug_str     0x000000000000370d      0x13f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                                        0x71c (size before relaxing)
 .debug_str     0x000000000000384c       0x75 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                                        0x450 (size before relaxing)
 .debug_str     0x00000000000038c1       0x3e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                                        0x6dc (size before relaxing)
 .debug_str     0x00000000000038ff       0x41 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                                        0x301 (size before relaxing)
 .debug_str     0x0000000000003940       0x45 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
                                         0x68 (size before relaxing)
 .debug_str     0x0000000000003985      0x18c zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                                        0x419 (size before relaxing)
 .debug_str     0x0000000000003b11      0x1b8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                                        0x6f1 (size before relaxing)
 .debug_str     0x0000000000003cc9      0x3c9 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                                        0xb03 (size before relaxing)
 .debug_str     0x0000000000004092       0x40 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                                        0x3c0 (size before relaxing)
 .debug_str     0x00000000000040d2      0x239 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
                                        0xa7c (size before relaxing)
 .debug_str     0x000000000000430b      0x2eb zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
                                        0x824 (size before relaxing)
 .debug_str     0x00000000000045f6      0xb4c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                                        0xf7d (size before relaxing)
 .debug_str     0x0000000000005142      0x270 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
                                        0x9a8 (size before relaxing)
 .debug_str     0x00000000000053b2       0x53 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
                                        0x395 (size before relaxing)
 .debug_str     0x0000000000005405     0x10e9 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                                       0x1d02 (size before relaxing)
 .debug_str     0x00000000000064ee      0x165 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
                                        0x5b1 (size before relaxing)
 .debug_str     0x0000000000006653      0xc04 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                                       0x1505 (size before relaxing)
 .debug_str     0x0000000000007257      0x837 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                                       0x14e2 (size before relaxing)
 .debug_str     0x0000000000007a8e      0x15a zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                                        0xf2b (size before relaxing)
 .debug_str     0x0000000000007be8       0x35 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                                        0x320 (size before relaxing)
 .debug_str     0x0000000000007c1d      0x418 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                                        0xd62 (size before relaxing)
 .debug_str     0x0000000000008035      0xb89 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                                       0x19f4 (size before relaxing)
 .debug_str     0x0000000000008bbe      0xafc zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                                       0x1742 (size before relaxing)
 .debug_str     0x00000000000096ba       0x87 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                                        0x5f9 (size before relaxing)
 .debug_str     0x0000000000009741      0x4d1 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                                        0xde7 (size before relaxing)
 .debug_str     0x0000000000009c12       0x89 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                                        0x49c (size before relaxing)
 .debug_str     0x0000000000009c9b       0xb1 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                                        0x4e7 (size before relaxing)
 .debug_str     0x0000000000009d4c      0xacf modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                                       0x1088 (size before relaxing)
 .debug_str     0x000000000000a81b      0x24c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                                       0x162f (size before relaxing)
 .debug_str     0x000000000000aa67      0x190 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                                        0xbe8 (size before relaxing)
 .debug_str     0x000000000000abf7     0x11ed modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                                       0x24a4 (size before relaxing)
 .debug_str     0x000000000000bde4      0xa14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                                       0x1d5a (size before relaxing)
 .debug_str     0x000000000000c7f8       0x49 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
                                        0x7db (size before relaxing)
 .debug_str     0x000000000000c841       0x2f zephyr/kernel/libkernel.a(busy_wait.c.obj)
                                        0x2ae (size before relaxing)
 .debug_str     0x000000000000c870       0x93 zephyr/kernel/libkernel.a(device.c.obj)
                                        0x3ab (size before relaxing)
 .debug_str     0x000000000000c903       0x88 zephyr/kernel/libkernel.a(fatal.c.obj)
                                        0x7ea (size before relaxing)
 .debug_str     0x000000000000c98b      0x556 zephyr/kernel/libkernel.a(init.c.obj)
                                       0x1172 (size before relaxing)
 .debug_str     0x000000000000cee1       0x31 zephyr/kernel/libkernel.a(init_static.c.obj)
                                        0x1e0 (size before relaxing)
 .debug_str     0x000000000000cf12      0x176 zephyr/kernel/libkernel.a(mem_slab.c.obj)
                                        0xaef (size before relaxing)
 .debug_str     0x000000000000d088       0x4e zephyr/kernel/libkernel.a(idle.c.obj)
                                        0x39f (size before relaxing)
 .debug_str     0x000000000000d0d6      0x150 zephyr/kernel/libkernel.a(mutex.c.obj)
                                        0x973 (size before relaxing)
 .debug_str     0x000000000000d226       0x4f zephyr/kernel/libkernel.a(sem.c.obj)
                                        0x8d9 (size before relaxing)
 .debug_str     0x000000000000d275      0x272 zephyr/kernel/libkernel.a(thread.c.obj)
                                        0xb26 (size before relaxing)
 .debug_str     0x000000000000d4e7      0x6f0 zephyr/kernel/libkernel.a(sched.c.obj)
                                       0x11f9 (size before relaxing)
 .debug_str     0x000000000000dbd7       0xba zephyr/kernel/libkernel.a(timeslicing.c.obj)
                                        0xa1c (size before relaxing)
 .debug_str     0x000000000000dc91       0x7d zephyr/kernel/libkernel.a(xip.c.obj)
                                        0x310 (size before relaxing)
 .debug_str     0x000000000000dd0e      0x127 zephyr/kernel/libkernel.a(timeout.c.obj)
                                        0x8c4 (size before relaxing)

.debug_loc      0x0000000000000000    0x2202d
 *(SORT_BY_ALIGNMENT(.debug_loc))
 .debug_loc     0x0000000000000000      0x332 app/libapp.a(main_temperature_working.c.obj)
 .debug_loc     0x0000000000000332      0x65d app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_loc     0x000000000000098f      0x308 app/libapp.a(m117_sensor.c.obj)
 .debug_loc     0x0000000000000c97     0x2282 zephyr/libzephyr.a(heap.c.obj)
 .debug_loc     0x0000000000002f19      0x15e zephyr/libzephyr.a(printk.c.obj)
 .debug_loc     0x0000000000003077       0x9d zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_loc     0x0000000000003114       0x17 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_loc     0x000000000000312b     0x2057 zephyr/libzephyr.a(onoff.c.obj)
 .debug_loc     0x0000000000005182      0x135 zephyr/libzephyr.a(notify.c.obj)
 .debug_loc     0x00000000000052b7       0x6f zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_loc     0x0000000000005326      0x7c0 zephyr/libzephyr.a(device.c.obj)
 .debug_loc     0x0000000000005ae6       0x15 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_loc     0x0000000000005afb      0x340 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_loc     0x0000000000005e3b      0x11d zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_loc     0x0000000000005f58       0x85 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_loc     0x0000000000005fdd      0x742 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_loc     0x000000000000671f       0xbc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_loc     0x00000000000067db       0x3a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_loc     0x0000000000006815       0xbc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_loc     0x00000000000068d1      0x1a1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_loc     0x0000000000006a72      0x1b7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_loc     0x0000000000006c29       0x56 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_loc     0x0000000000006c7f       0x6a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_loc     0x0000000000006ce9       0x3f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_loc     0x0000000000006d28      0x5f9 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_loc     0x0000000000007321       0x25 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_loc     0x0000000000007346      0xcda zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_loc     0x0000000000008020      0x405 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_loc     0x0000000000008425      0x45a zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_loc     0x000000000000887f      0x3fa zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_loc     0x0000000000008c79      0x2b3 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_loc     0x0000000000008f2c     0x18ca zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_loc     0x000000000000a7f6       0x85 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_loc     0x000000000000a87b     0x189e zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_loc     0x000000000000c119      0x53c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_loc     0x000000000000c655      0x507 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_loc     0x000000000000cb5c       0x42 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_loc     0x000000000000cb9e      0x7dc zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_loc     0x000000000000d37a     0x1779 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_loc     0x000000000000eaf3     0x1c42 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_loc     0x0000000000010735      0x182 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_loc     0x00000000000108b7      0x258 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_loc     0x0000000000010b0f      0x221 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_loc     0x0000000000010d30      0x996 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_loc     0x00000000000116c6     0x166c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_loc     0x0000000000012d32      0xf8f modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_loc     0x0000000000013cc1     0x3eaa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_loc     0x0000000000017b6b     0x2874 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_loc     0x000000000001a3df      0x3e5 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_loc     0x000000000001a7c4       0x32 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_loc     0x000000000001a7f6       0xd2 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_loc     0x000000000001a8c8      0x194 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_loc     0x000000000001aa5c      0x825 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_loc     0x000000000001b281      0x51d zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_loc     0x000000000001b79e       0x86 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_loc     0x000000000001b824      0x769 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_loc     0x000000000001bf8d      0x526 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_loc     0x000000000001c4b3      0xa3f zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_loc     0x000000000001cef2     0x3da6 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_loc     0x0000000000020c98      0x3fe zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_loc     0x0000000000021096      0xf97 zephyr/kernel/libkernel.a(timeout.c.obj)

.debug_macinfo
 *(SORT_BY_ALIGNMENT(.debug_macinfo))

.debug_weaknames
 *(SORT_BY_ALIGNMENT(.debug_weaknames))

.debug_funcnames
 *(SORT_BY_ALIGNMENT(.debug_funcnames))

.debug_typenames
 *(SORT_BY_ALIGNMENT(.debug_typenames))

.debug_varnames
 *(SORT_BY_ALIGNMENT(.debug_varnames))

.debug_pubtypes
 *(SORT_BY_ALIGNMENT(.debug_pubtypes))

.debug_ranges   0x0000000000000000     0x5e70
 *(SORT_BY_ALIGNMENT(.debug_ranges))
 .debug_ranges  0x0000000000000000       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_ranges  0x0000000000000020       0x10 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_ranges  0x0000000000000030      0x130 app/libapp.a(main_temperature_working.c.obj)
 .debug_ranges  0x0000000000000160       0xc0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_ranges  0x0000000000000220       0x90 app/libapp.a(m117_sensor.c.obj)
 .debug_ranges  0x00000000000002b0      0x420 zephyr/libzephyr.a(heap.c.obj)
 .debug_ranges  0x00000000000006d0       0x58 zephyr/libzephyr.a(printk.c.obj)
 .debug_ranges  0x0000000000000728       0x28 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_ranges  0x0000000000000750       0x40 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_ranges  0x0000000000000790      0x498 zephyr/libzephyr.a(onoff.c.obj)
 .debug_ranges  0x0000000000000c28       0x30 zephyr/libzephyr.a(notify.c.obj)
 .debug_ranges  0x0000000000000c58       0x10 zephyr/libzephyr.a(configs.c.obj)
 .debug_ranges  0x0000000000000c68       0x18 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_ranges  0x0000000000000c80       0xc8 zephyr/libzephyr.a(device.c.obj)
 .debug_ranges  0x0000000000000d48       0x10 zephyr/libzephyr.a(banner.c.obj)
 .debug_ranges  0x0000000000000d58       0x18 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_ranges  0x0000000000000d70       0x78 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_ranges  0x0000000000000de8       0x38 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_ranges  0x0000000000000e20       0x10 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_ranges  0x0000000000000e30       0x50 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_ranges  0x0000000000000e80       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_ranges  0x0000000000000e90      0x110 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_ranges  0x0000000000000fa0       0x68 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_ranges  0x0000000000001008       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_ranges  0x0000000000001018       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_ranges  0x0000000000001030       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_ranges  0x0000000000001078       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_ranges  0x00000000000010a0       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_ranges  0x00000000000010d0       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_ranges  0x00000000000010f0       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_ranges  0x0000000000001120       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_ranges  0x0000000000001148       0xd8 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_ranges  0x0000000000001220       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_ranges  0x0000000000001238      0x3d0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_ranges  0x0000000000001608       0xe8 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .debug_ranges  0x00000000000016f0       0x80 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_ranges  0x0000000000001770       0xd0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_ranges  0x0000000000001840       0xb0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_ranges  0x00000000000018f0       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_ranges  0x0000000000001900      0x488 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_ranges  0x0000000000001d88       0x38 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_ranges  0x0000000000001dc0      0x2e8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_ranges  0x00000000000020a8       0xa8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_ranges  0x0000000000002150       0xa0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_ranges  0x00000000000021f0       0x10 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_ranges  0x0000000000002200      0x110 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_ranges  0x0000000000002310      0x1f8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_ranges  0x0000000000002508      0x5e8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_ranges  0x0000000000002af0       0x30 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_ranges  0x0000000000002b20       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_ranges  0x0000000000002b50       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_ranges  0x0000000000002b70       0x50 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_ranges  0x0000000000002bc0       0xf8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_ranges  0x0000000000002cb8      0x1f0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_ranges  0x0000000000002ea8      0x328 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_ranges  0x00000000000031d0      0xb68 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_ranges  0x0000000000003d38      0x588 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_ranges  0x00000000000042c0      0x188 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_ranges  0x0000000000004448       0x10 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_ranges  0x0000000000004458       0xa0 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_ranges  0x00000000000044f8       0x70 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_ranges  0x0000000000004568      0x278 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_ranges  0x00000000000047e0       0x10 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_ranges  0x00000000000047f0       0xd8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_ranges  0x00000000000048c8       0x48 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_ranges  0x0000000000004910       0xe8 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_ranges  0x00000000000049f8       0xd0 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_ranges  0x0000000000004ac8      0x1a8 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_ranges  0x0000000000004c70      0xcd8 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_ranges  0x0000000000005948       0xa8 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_ranges  0x00000000000059f0       0x10 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_ranges  0x0000000000005a00      0x470 zephyr/kernel/libkernel.a(timeout.c.obj)

.debug_addr
 *(SORT_BY_ALIGNMENT(.debug_addr))

.debug_line_str
 *(SORT_BY_ALIGNMENT(.debug_line_str))

.debug_loclists
 *(SORT_BY_ALIGNMENT(.debug_loclists))

.debug_macro
 *(SORT_BY_ALIGNMENT(.debug_macro))

.debug_names
 *(SORT_BY_ALIGNMENT(.debug_names))

.debug_rnglists
 *(SORT_BY_ALIGNMENT(.debug_rnglists))

.debug_str_offsets
 *(SORT_BY_ALIGNMENT(.debug_str_offsets))

.debug_sup
 *(SORT_BY_ALIGNMENT(.debug_sup))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.note.GNU-stack))

.ARM.attributes
                0x0000000000000000       0x38
 *(SORT_BY_ALIGNMENT(.ARM.attributes))
 .ARM.attributes
                0x0000000000000000       0x36 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .ARM.attributes
                0x0000000000000036       0x36 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .ARM.attributes
                0x000000000000006c       0x36 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .ARM.attributes
                0x00000000000000a2       0x36 app/libapp.a(main_temperature_working.c.obj)
 .ARM.attributes
                0x00000000000000d8       0x36 app/libapp.a(zephyr_i2c_driver.c.obj)
 .ARM.attributes
                0x000000000000010e       0x36 app/libapp.a(m117_sensor.c.obj)
 .ARM.attributes
                0x0000000000000144       0x36 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .ARM.attributes
                0x000000000000017a       0x36 zephyr/libzephyr.a(crc32_sw.c.obj)
 .ARM.attributes
                0x00000000000001b0       0x36 zephyr/libzephyr.a(crc24_sw.c.obj)
 .ARM.attributes
                0x00000000000001e6       0x36 zephyr/libzephyr.a(crc16_sw.c.obj)
 .ARM.attributes
                0x000000000000021c       0x36 zephyr/libzephyr.a(crc8_sw.c.obj)
 .ARM.attributes
                0x0000000000000252       0x36 zephyr/libzephyr.a(crc7_sw.c.obj)
 .ARM.attributes
                0x0000000000000288       0x36 zephyr/libzephyr.a(crc4_sw.c.obj)
 .ARM.attributes
                0x00000000000002be       0x36 zephyr/libzephyr.a(heap.c.obj)
 .ARM.attributes
                0x00000000000002f4       0x36 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .ARM.attributes
                0x000000000000032a       0x36 zephyr/libzephyr.a(printk.c.obj)
 .ARM.attributes
                0x0000000000000360       0x36 zephyr/libzephyr.a(sem.c.obj)
 .ARM.attributes
                0x0000000000000396       0x36 zephyr/libzephyr.a(thread_entry.c.obj)
 .ARM.attributes
                0x00000000000003cc       0x36 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .ARM.attributes
                0x0000000000000402       0x36 zephyr/libzephyr.a(assert.c.obj)
 .ARM.attributes
                0x0000000000000438       0x36 zephyr/libzephyr.a(poweroff.c.obj)
 .ARM.attributes
                0x000000000000046e       0x36 zephyr/libzephyr.a(dec.c.obj)
 .ARM.attributes
                0x00000000000004a4       0x36 zephyr/libzephyr.a(hex.c.obj)
 .ARM.attributes
                0x00000000000004da       0x36 zephyr/libzephyr.a(rb.c.obj)
 .ARM.attributes
                0x0000000000000510       0x36 zephyr/libzephyr.a(timeutil.c.obj)
 .ARM.attributes
                0x0000000000000546       0x36 zephyr/libzephyr.a(bitarray.c.obj)
 .ARM.attributes
                0x000000000000057c       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .ARM.attributes
                0x00000000000005b2       0x36 zephyr/libzephyr.a(notify.c.obj)
 .ARM.attributes
                0x00000000000005e8       0x36 zephyr/libzephyr.a(configs.c.obj)
 .ARM.attributes
                0x000000000000061e       0x36 zephyr/libzephyr.a(mem_attr.c.obj)
 .ARM.attributes
                0x0000000000000654       0x36 zephyr/libzephyr.a(device.c.obj)
 .ARM.attributes
                0x000000000000068a       0x36 zephyr/libzephyr.a(device_system_managed.c.obj)
 .ARM.attributes
                0x00000000000006c0       0x36 zephyr/libzephyr.a(tracing_none.c.obj)
 .ARM.attributes
                0x00000000000006f6       0x36 zephyr/libzephyr.a(banner.c.obj)
 .ARM.attributes
                0x000000000000072c       0x36 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .ARM.attributes
                0x0000000000000762       0x36 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .ARM.attributes
                0x0000000000000798       0x36 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .ARM.attributes
                0x00000000000007ce       0x36 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .ARM.attributes
                0x0000000000000804       0x36 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .ARM.attributes
                0x000000000000083a       0x22 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .ARM.attributes
                0x000000000000085c       0x36 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .ARM.attributes
                0x0000000000000892       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .ARM.attributes
                0x00000000000008c8       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .ARM.attributes
                0x00000000000008fe       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .ARM.attributes
                0x0000000000000920       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .ARM.attributes
                0x0000000000000956       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .ARM.attributes
                0x0000000000000978       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .ARM.attributes
                0x00000000000009ae       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .ARM.attributes
                0x00000000000009e4       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .ARM.attributes
                0x0000000000000a08       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .ARM.attributes
                0x0000000000000a3e       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .ARM.attributes
                0x0000000000000a60       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .ARM.attributes
                0x0000000000000a96       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .ARM.attributes
                0x0000000000000acc       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .ARM.attributes
                0x0000000000000b02       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .ARM.attributes
                0x0000000000000b38       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .ARM.attributes
                0x0000000000000b6e       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .ARM.attributes
                0x0000000000000ba4       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .ARM.attributes
                0x0000000000000bc6       0x36 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .ARM.attributes
                0x0000000000000bfc       0x36 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .ARM.attributes
                0x0000000000000c32       0x36 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .ARM.attributes
                0x0000000000000c68       0x36 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .ARM.attributes
                0x0000000000000c9e       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(libc-hooks.c.obj)
 .ARM.attributes
                0x0000000000000cd4       0x36 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .ARM.attributes
                0x0000000000000d0a       0x36 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .ARM.attributes
                0x0000000000000d40       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .ARM.attributes
                0x0000000000000d76       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .ARM.attributes
                0x0000000000000dac       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .ARM.attributes
                0x0000000000000de2       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .ARM.attributes
                0x0000000000000e18       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .ARM.attributes
                0x0000000000000e4e       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .ARM.attributes
                0x0000000000000e84       0x36 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .ARM.attributes
                0x0000000000000eba       0x36 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .ARM.attributes
                0x0000000000000ef0       0x36 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .ARM.attributes
                0x0000000000000f26       0x36 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .ARM.attributes
                0x0000000000000f5c       0x36 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .ARM.attributes
                0x0000000000000f92       0x36 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .ARM.attributes
                0x0000000000000fc8       0x36 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .ARM.attributes
                0x0000000000000ffe       0x36 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .ARM.attributes
                0x0000000000001034       0x36 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .ARM.attributes
                0x000000000000106a       0x36 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .ARM.attributes
                0x00000000000010a0       0x36 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .ARM.attributes
                0x00000000000010d6       0x36 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .ARM.attributes
                0x000000000000110c       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .ARM.attributes
                0x0000000000001142       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .ARM.attributes
                0x0000000000001178       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .ARM.attributes
                0x00000000000011ae       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .ARM.attributes
                0x00000000000011e4       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .ARM.attributes
                0x000000000000121a       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .ARM.attributes
                0x0000000000001250       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .ARM.attributes
                0x0000000000001286       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .ARM.attributes
                0x00000000000012bc       0x36 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .ARM.attributes
                0x00000000000012f2       0x36 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .ARM.attributes
                0x0000000000001328       0x36 zephyr/kernel/libkernel.a(device.c.obj)
 .ARM.attributes
                0x000000000000135e       0x36 zephyr/kernel/libkernel.a(fatal.c.obj)
 .ARM.attributes
                0x0000000000001394       0x36 zephyr/kernel/libkernel.a(init.c.obj)
 .ARM.attributes
                0x00000000000013ca       0x36 zephyr/kernel/libkernel.a(init_static.c.obj)
 .ARM.attributes
                0x0000000000001400       0x36 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .ARM.attributes
                0x0000000000001436       0x36 zephyr/kernel/libkernel.a(idle.c.obj)
 .ARM.attributes
                0x000000000000146c       0x36 zephyr/kernel/libkernel.a(mutex.c.obj)
 .ARM.attributes
                0x00000000000014a2       0x36 zephyr/kernel/libkernel.a(sem.c.obj)
 .ARM.attributes
                0x00000000000014d8       0x36 zephyr/kernel/libkernel.a(thread.c.obj)
 .ARM.attributes
                0x000000000000150e       0x36 zephyr/kernel/libkernel.a(sched.c.obj)
 .ARM.attributes
                0x0000000000001544       0x36 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .ARM.attributes
                0x000000000000157a       0x36 zephyr/kernel/libkernel.a(xip.c.obj)
 .ARM.attributes
                0x00000000000015b0       0x36 zephyr/kernel/libkernel.a(timeout.c.obj)
 .ARM.attributes
                0x00000000000015e6       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .ARM.attributes
                0x000000000000161a       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .ARM.attributes
                0x000000000000164e       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .ARM.attributes
                0x0000000000001682       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .ARM.attributes
                0x00000000000016b6       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .ARM.attributes
                0x00000000000016ea       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .ARM.attributes
                0x000000000000171e       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .ARM.attributes
                0x0000000000001752       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .ARM.attributes
                0x0000000000001786       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .ARM.attributes
                0x00000000000017ba       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .ARM.attributes
                0x00000000000017ee       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .ARM.attributes
                0x0000000000001822       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000001856       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .ARM.attributes
                0x000000000000188a       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .ARM.attributes
                0x00000000000018be       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .ARM.attributes
                0x00000000000018f2       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000001926       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .ARM.attributes
                0x000000000000195a       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .ARM.attributes
                0x000000000000198e       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .ARM.attributes
                0x00000000000019c2       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .ARM.attributes
                0x00000000000019f6       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .ARM.attributes
                0x0000000000001a2a       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .ARM.attributes
                0x0000000000001a5e       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .ARM.attributes
                0x0000000000001a92       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .ARM.attributes
                0x0000000000001ac6       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .ARM.attributes
                0x0000000000001afa       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .ARM.attributes
                0x0000000000001b2e       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .ARM.attributes
                0x0000000000001b62       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .ARM.attributes
                0x0000000000001b96       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .ARM.attributes
                0x0000000000001bca       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .ARM.attributes
                0x0000000000001bfe       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .ARM.attributes
                0x0000000000001c32       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .ARM.attributes
                0x0000000000001c66       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .ARM.attributes
                0x0000000000001c9a       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .ARM.attributes
                0x0000000000001cce       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .ARM.attributes
                0x0000000000001cee       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .ARM.attributes
                0x0000000000001d24       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .ARM.attributes
                0x0000000000001d5a       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .ARM.attributes
                0x0000000000001d90       0x20 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .ARM.attributes
                0x0000000000001db0       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .ARM.attributes
                0x0000000000001de6       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .ARM.attributes
                0x0000000000001e1c       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .ARM.attributes
                0x0000000000001e52       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .ARM.attributes
                0x0000000000001e88       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .ARM.attributes
                0x0000000000001ebe       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .ARM.attributes
                0x0000000000001ef4       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .ARM.attributes
                0x0000000000001f2a       0x36 c:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .ARM.attributes
                0x0000000000001f60       0x32 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .ARM.attributes
                0x0000000000001f92       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x0000000000001fb4       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .ARM.attributes
                0x0000000000001fd6       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x0000000000001ff8       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x000000000000201a       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .ARM.attributes
                0x000000000000203c       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .ARM.attributes
                0x000000000000205e       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .ARM.attributes
                0x0000000000002080       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .ARM.attributes
                0x00000000000020a2       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .ARM.attributes
                0x00000000000020c4       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000000000020e6       0x32 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .ARM.attributes
                0x0000000000002118       0x32 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .ARM.attributes
                0x000000000000214a       0x32 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x000000000000217c       0x32 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x00000000000021ae       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000000000021d0       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x00000000000021f2       0x22 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 *(SORT_BY_ALIGNMENT(.gnu.attributes))

.last_section   0x00000000000078b4        0x4
                0x00000000000078b4        0x4 LONG 0xe015e015
                0x00000000000078b8                _flash_used = ((LOADADDR (.last_section) + SIZEOF (.last_section)) - __rom_region_start)
OUTPUT(zephyr\zephyr.elf elf32-littlearm)
LOAD linker stubs
