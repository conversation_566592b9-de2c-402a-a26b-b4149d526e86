Archive member included to satisfy reference by file (symbol)

app/libapp.a(main_temp_low_power.c.obj)
                              (--whole-archive)
app/libapp.a(zephyr_i2c_driver.c.obj)
                              (--whole-archive)
app/libapp.a(m117_sensor.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc32c_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc32_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc24_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc16_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc8_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc7_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(crc4_sw.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(heap.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(printk.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(sem.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(thread_entry.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf_complete.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(assert.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(poweroff.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(dec.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(hex.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(rb.c.obj)  (--whole-archive)
zephyr/libzephyr.a(timeutil.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(bitarray.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(onoff.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(notify.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(configs.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(mem_attr.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(device.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(device_runtime.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(tracing_none.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(banner.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                              (--whole-archive)
zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                              (--whole-archive)
zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                              (--whole-archive)
zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
                              (--whole-archive)
zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                              (--whole-archive)
zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                              (--whole-archive)
zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
                              (--whole-archive)
zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                              (--whole-archive)
zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                              (--whole-archive)
zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                              (--whole-archive)
zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                              (--whole-archive)
zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
                              (--whole-archive)
zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                              (--whole-archive)
modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                              (--whole-archive)
modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
                              (--whole-archive)
zephyr/kernel/libkernel.a(busy_wait.c.obj)
                              modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj) (z_impl_k_busy_wait)
zephyr/kernel/libkernel.a(device.c.obj)
                              zephyr/libzephyr.a(device.c.obj) (z_device_get_all_static)
zephyr/kernel/libkernel.a(errno.c.obj)
                              zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj) (z_impl_z_errno)
zephyr/kernel/libkernel.a(fatal.c.obj)
                              zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj) (z_fatal_error)
zephyr/kernel/libkernel.a(init.c.obj)
                              zephyr/libzephyr.a(device_runtime.c.obj) (z_sys_post_kernel)
zephyr/kernel/libkernel.a(init_static.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_init_static)
zephyr/kernel/libkernel.a(mem_slab.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (k_mem_slab_init)
zephyr/kernel/libkernel.a(idle.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (idle)
zephyr/kernel/libkernel.a(mutex.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (z_impl_k_mutex_init)
zephyr/kernel/libkernel.a(sem.c.obj)
                              zephyr/libzephyr.a(sem.c.obj) (z_impl_k_sem_init)
zephyr/kernel/libkernel.a(work.c.obj)
                              zephyr/libzephyr.a(device_runtime.c.obj) (k_work_init_delayable)
zephyr/kernel/libkernel.a(thread.c.obj)
                              zephyr/libzephyr.a(device_runtime.c.obj) (k_is_in_isr)
zephyr/kernel/libkernel.a(sched.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (_thread_dummy)
zephyr/kernel/libkernel.a(xip.c.obj)
                              zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj) (z_data_copy)
zephyr/kernel/libkernel.a(timeout.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_add_timeout)
zephyr/kernel/libkernel.a(events.c.obj)
                              zephyr/libzephyr.a(device_runtime.c.obj) (z_impl_k_event_set)
zephyr/kernel/libkernel.a(system_work_q.c.obj)
                              zephyr/kernel/libkernel.a(work.c.obj) (k_sys_work_q)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                              modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj) (nrf_cc3xx_platform_init_no_rng)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (platform_abort_apis)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (nrf_cc3xx_platform_set_mutexes)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj) (CC_LibInitNoRng)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (CC_HalInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (CC_PalInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalDmaInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj) (CC_PalWaitInterruptRND)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj) (CC_PalMemCopyPlat)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalMutexCreate)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalPowerSaveModeInit)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (nrf_cc3xx_platform_ctr_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (nrf_cc3xx_platform_hmac_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_platform_zeroize)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (LLF_RND_RunTrngStartupTest)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_ctr_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_entropy_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj) (cc_mbedtls_hmac_drbg_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (RNG_PLAT_SetUserRngParameters)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj) (CC_PalTrngParamGet)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj) (mbedtls_mutex_unlock)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj) (LLF_RND_WaitRngInterrupt)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (mbedtls_hardware_poll)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj) (cc_mbedtls_aes_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (cc_mbedtls_sha256_init)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj) (mbedtls_sha_process_internal)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (cc_mbedtls_sha256)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj) (SetDataBuffersInfo)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj) (InitHashDrv)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj) (ProcessAesDrv)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj) (kmu_validate_slot_and_size)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj) (write_invalid_chacha20_key)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj) (UtilCmacBuildDataForDerivation)
C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj) (CC_PalDataBufferAttrGet)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-ctype_.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (_ctype_)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
                              zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj) (malloc)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj) (memcmp)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
                              zephyr/libzephyr.a(heap.c.obj) (memcpy)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
                              C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj) (memmove)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
                              zephyr/libzephyr.a(heap.c.obj) (memset)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o) (_free_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o) (_malloc_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
                              (_printf_float)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o) (_printf_common)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o) (_sbrk_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strcmp.o)
                              zephyr/kernel/libkernel.a(device.c.obj) (strcmp)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strlen.o)
                              zephyr/libzephyr.a(cbprintf_packaged.c.obj) (strlen)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (strnlen)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o) (_dtoa_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o) (_impure_ptr)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o) (_localeconv_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o) (memchr)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o) (__malloc_lock)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o) (_Balloc)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o) (_calloc_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o) (errno)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o) (__assert_func)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o) (fiprintf)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o) (__global_locale)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o) (__ascii_mbtowc)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o) (_vfprintf_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o) (__swbuf_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o) (__ascii_wctomb)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o) (__swsetup_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o) (_fflush_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o) (__sinit)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o) (__sfvwrite_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o) (_fwalk)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o) (__smakebuf_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o) (_realloc_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o) (__sread)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o) (_write_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o) (_close_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o) (_fstat_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o) (_isatty_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o) (_lseek_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o) (_malloc_usable_size_r)
C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o) (_read_r)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(cmse.o)
                              zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj) (cmse_check_address_range)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_dmul)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_dsub)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_ddiv)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o) (__aeabi_dcmpeq)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o) (__aeabi_dcmpun)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
                              C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o) (__aeabi_d2iz)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_d2f)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_ldivmod)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (__aeabi_uldivmod)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_popcountsi2.o)
                              zephyr/libzephyr.a(bitarray.c.obj) (__popcountsi2)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_d2lz)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o) (__aeabi_d2ulz)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o) (__udivmoddi4)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o) (__aeabi_ldiv0)
c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
                              c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)

Discarded input sections

 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .debug_line    0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .debug_str     0x0000000000000000      0x193 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .comment       0x0000000000000000       0x21 zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .text          0x0000000000000000        0x0 app/libapp.a(main_temp_low_power.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(main_temp_low_power.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(main_temp_low_power.c.obj)
 .text          0x0000000000000000        0x0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .text          0x0000000000000000        0x0 app/libapp.a(m117_sensor.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(m117_sensor.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(m117_sensor.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .text.crc32_c  0x0000000000000000       0x48 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .rodata.crc32c_table
                0x0000000000000000       0x40 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_info    0x0000000000000000      0x184 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xcd zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_loc     0x0000000000000000      0x104 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x30 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_line    0x0000000000000000      0x235 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_str     0x0000000000000000      0x285 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc32c_sw.c.obj)
 .debug_frame   0x0000000000000000       0x2c zephyr/libzephyr.a(crc32c_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc32_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc32_sw.c.obj)
 .text.crc32_ieee_update
                0x0000000000000000       0x3c zephyr/libzephyr.a(crc32_sw.c.obj)
 .text.crc32_ieee
                0x0000000000000000        0xa zephyr/libzephyr.a(crc32_sw.c.obj)
 .rodata.table.0
                0x0000000000000000       0x40 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_info    0x0000000000000000      0x1cb zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_abbrev  0x0000000000000000      0x117 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_loc     0x0000000000000000      0x160 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x50 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_line    0x0000000000000000      0x26a zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_str     0x0000000000000000      0x27e zephyr/libzephyr.a(crc32_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc32_sw.c.obj)
 .debug_frame   0x0000000000000000       0x3c zephyr/libzephyr.a(crc32_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc24_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc24_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc24_sw.c.obj)
 .text.crc24_pgp_update
                0x0000000000000000       0x2c zephyr/libzephyr.a(crc24_sw.c.obj)
 .text.crc24_pgp
                0x0000000000000000       0x18 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_info    0x0000000000000000      0x183 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xd6 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_loc     0x0000000000000000      0x137 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_line    0x0000000000000000      0x21e zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_str     0x0000000000000000      0x271 zephyr/libzephyr.a(crc24_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc24_sw.c.obj)
 .debug_frame   0x0000000000000000       0x40 zephyr/libzephyr.a(crc24_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16    0x0000000000000000       0x30 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16_reflect
                0x0000000000000000       0x2a zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16_ccitt
                0x0000000000000000       0x26 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text.crc16_itu_t
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_info    0x0000000000000000      0x2d8 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xc8 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_loc     0x0000000000000000      0x3d0 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x28 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_line    0x0000000000000000      0x3a6 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_str     0x0000000000000000      0x287 zephyr/libzephyr.a(crc16_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc16_sw.c.obj)
 .debug_frame   0x0000000000000000       0x70 zephyr/libzephyr.a(crc16_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc8_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc8_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc8_sw.c.obj)
 .text.crc8_ccitt
                0x0000000000000000       0x2c zephyr/libzephyr.a(crc8_sw.c.obj)
 .text.crc8     0x0000000000000000       0x54 zephyr/libzephyr.a(crc8_sw.c.obj)
 .rodata.crc8_ccitt_small_table
                0x0000000000000000       0x10 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_info    0x0000000000000000      0x203 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xdc zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_loc     0x0000000000000000      0x26e zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_line    0x0000000000000000      0x2e9 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_str     0x0000000000000000      0x28b zephyr/libzephyr.a(crc8_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc8_sw.c.obj)
 .debug_frame   0x0000000000000000       0x48 zephyr/libzephyr.a(crc8_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .text.crc7_be  0x0000000000000000       0x20 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_info    0x0000000000000000      0x127 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xac zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_loc     0x0000000000000000       0xc0 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_line    0x0000000000000000      0x1dc zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_str     0x0000000000000000      0x249 zephyr/libzephyr.a(crc7_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc7_sw.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/libzephyr.a(crc7_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(crc4_sw.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(crc4_sw.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(crc4_sw.c.obj)
 .text.crc4     0x0000000000000000       0x8a zephyr/libzephyr.a(crc4_sw.c.obj)
 .text.crc4_ti  0x0000000000000000       0x40 zephyr/libzephyr.a(crc4_sw.c.obj)
 .rodata.lookup.0
                0x0000000000000000        0x8 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_info    0x0000000000000000      0x22e zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_abbrev  0x0000000000000000       0xf2 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_loc     0x0000000000000000      0x386 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_ranges  0x0000000000000000       0x48 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_line    0x0000000000000000      0x324 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_str     0x0000000000000000      0x283 zephyr/libzephyr.a(crc4_sw.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(crc4_sw.c.obj)
 .debug_frame   0x0000000000000000       0x4c zephyr/libzephyr.a(crc4_sw.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_field
                0x0000000000000000       0x16 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_set
                0x0000000000000000       0x16 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_size
                0x0000000000000000        0xc zephyr/libzephyr.a(heap.c.obj)
 .text.set_chunk_used
                0x0000000000000000       0x30 zephyr/libzephyr.a(heap.c.obj)
 .text.set_chunk_size
                0x0000000000000000        0x8 zephyr/libzephyr.a(heap.c.obj)
 .text.mem_to_chunkid
                0x0000000000000000       0x14 zephyr/libzephyr.a(heap.c.obj)
 .text.bucket_idx.isra.0
                0x0000000000000000       0x1c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_remove_bidx
                0x0000000000000000       0x5c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_remove
                0x0000000000000000       0x2e zephyr/libzephyr.a(heap.c.obj)
 .text.alloc_chunk
                0x0000000000000000       0x74 zephyr/libzephyr.a(heap.c.obj)
 .text.split_chunks
                0x0000000000000000       0x4e zephyr/libzephyr.a(heap.c.obj)
 .text.merge_chunks
                0x0000000000000000       0x3c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_add
                0x0000000000000000       0x84 zephyr/libzephyr.a(heap.c.obj)
 .text.free_chunk
                0x0000000000000000       0x84 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_free
                0x0000000000000000       0x24 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_usable_size
                0x0000000000000000       0x22 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_alloc
                0x0000000000000000       0x6e zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_aligned_alloc
                0x0000000000000000       0xe8 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_aligned_realloc
                0x0000000000000000      0x144 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_init
                0x0000000000000000       0xb0 zephyr/libzephyr.a(heap.c.obj)
 .debug_info    0x0000000000000000     0x2929 zephyr/libzephyr.a(heap.c.obj)
 .debug_abbrev  0x0000000000000000      0x449 zephyr/libzephyr.a(heap.c.obj)
 .debug_loc     0x0000000000000000     0x2282 zephyr/libzephyr.a(heap.c.obj)
 .debug_aranges
                0x0000000000000000       0xb8 zephyr/libzephyr.a(heap.c.obj)
 .debug_ranges  0x0000000000000000      0x420 zephyr/libzephyr.a(heap.c.obj)
 .debug_line    0x0000000000000000      0xfca zephyr/libzephyr.a(heap.c.obj)
 .debug_str     0x0000000000000000      0x6d3 zephyr/libzephyr.a(heap.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(heap.c.obj)
 .debug_frame   0x0000000000000000      0x2a0 zephyr/libzephyr.a(heap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbvprintf_package
                0x0000000000000000      0x3a4 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbprintf_package
                0x0000000000000000       0x1e zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbpprintf_external
                0x0000000000000000       0x62 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.is_ptr   0x0000000000000000       0x4a zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbprintf_package_convert
                0x0000000000000000      0x330 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_info    0x0000000000000000     0x143f zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_abbrev  0x0000000000000000      0x4bf zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_loc     0x0000000000000000     0x1ff9 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_aranges
                0x0000000000000000       0x40 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_ranges  0x0000000000000000      0x260 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_line    0x0000000000000000      0xfe9 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_str     0x0000000000000000      0x74c zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_frame   0x0000000000000000       0xec zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .text.str_out  0x0000000000000000       0x24 zephyr/libzephyr.a(printk.c.obj)
 .text.__printk_hook_install
                0x0000000000000000        0xc zephyr/libzephyr.a(printk.c.obj)
 .text.__printk_get_hook
                0x0000000000000000        0xc zephyr/libzephyr.a(printk.c.obj)
 .text.z_impl_k_str_out
                0x0000000000000000       0x1c zephyr/libzephyr.a(printk.c.obj)
 .text.vsnprintk
                0x0000000000000000       0x2c zephyr/libzephyr.a(printk.c.obj)
 .text.snprintk
                0x0000000000000000       0x1a zephyr/libzephyr.a(printk.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_init
                0x0000000000000000        0xa zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_give
                0x0000000000000000        0xa zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_take
                0x0000000000000000       0x1e zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_count_get
                0x0000000000000000        0x4 zephyr/libzephyr.a(sem.c.obj)
 .debug_info    0x0000000000000000      0x4e4 zephyr/libzephyr.a(sem.c.obj)
 .debug_abbrev  0x0000000000000000      0x22a zephyr/libzephyr.a(sem.c.obj)
 .debug_loc     0x0000000000000000      0x21a zephyr/libzephyr.a(sem.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(sem.c.obj)
 .debug_ranges  0x0000000000000000       0x70 zephyr/libzephyr.a(sem.c.obj)
 .debug_line    0x0000000000000000      0x334 zephyr/libzephyr.a(sem.c.obj)
 .debug_str     0x0000000000000000      0x397 zephyr/libzephyr.a(sem.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(sem.c.obj)
 .debug_frame   0x0000000000000000       0x68 zephyr/libzephyr.a(sem.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf.c.obj)
 .text.cbprintf
                0x0000000000000000       0x1e zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_info    0x0000000000000000      0x245 zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_abbrev  0x0000000000000000      0x17e zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_loc     0x0000000000000000       0xcc zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_ranges  0x0000000000000000       0x30 zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_line    0x0000000000000000      0x23f zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_str     0x0000000000000000      0x2a5 zephyr/libzephyr.a(cbprintf.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(cbprintf.c.obj)
 .debug_frame   0x0000000000000000       0x3c zephyr/libzephyr.a(cbprintf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .text.assert_post_action
                0x0000000000000000       0x12 zephyr/libzephyr.a(assert.c.obj)
 .text.assert_print
                0x0000000000000000       0x1a zephyr/libzephyr.a(assert.c.obj)
 .debug_info    0x0000000000000000      0x24e zephyr/libzephyr.a(assert.c.obj)
 .debug_abbrev  0x0000000000000000      0x1ca zephyr/libzephyr.a(assert.c.obj)
 .debug_loc     0x0000000000000000       0x51 zephyr/libzephyr.a(assert.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(assert.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(assert.c.obj)
 .debug_line    0x0000000000000000      0x328 zephyr/libzephyr.a(assert.c.obj)
 .debug_str     0x0000000000000000      0x351 zephyr/libzephyr.a(assert.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(assert.c.obj)
 .debug_frame   0x0000000000000000       0x50 zephyr/libzephyr.a(assert.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(poweroff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(poweroff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(poweroff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .text.u8_to_dec
                0x0000000000000000       0x52 zephyr/libzephyr.a(dec.c.obj)
 .debug_info    0x0000000000000000      0x12b zephyr/libzephyr.a(dec.c.obj)
 .debug_abbrev  0x0000000000000000       0x9c zephyr/libzephyr.a(dec.c.obj)
 .debug_loc     0x0000000000000000      0x128 zephyr/libzephyr.a(dec.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(dec.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/libzephyr.a(dec.c.obj)
 .debug_line    0x0000000000000000      0x1d8 zephyr/libzephyr.a(dec.c.obj)
 .debug_str     0x0000000000000000      0x268 zephyr/libzephyr.a(dec.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(dec.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/libzephyr.a(dec.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .text.char2hex
                0x0000000000000000       0x30 zephyr/libzephyr.a(hex.c.obj)
 .text.hex2char
                0x0000000000000000       0x1c zephyr/libzephyr.a(hex.c.obj)
 .text.bin2hex  0x0000000000000000       0x54 zephyr/libzephyr.a(hex.c.obj)
 .text.hex2bin  0x0000000000000000       0x70 zephyr/libzephyr.a(hex.c.obj)
 .debug_info    0x0000000000000000      0x29d zephyr/libzephyr.a(hex.c.obj)
 .debug_abbrev  0x0000000000000000      0x117 zephyr/libzephyr.a(hex.c.obj)
 .debug_loc     0x0000000000000000      0x2f4 zephyr/libzephyr.a(hex.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(hex.c.obj)
 .debug_ranges  0x0000000000000000       0x40 zephyr/libzephyr.a(hex.c.obj)
 .debug_line    0x0000000000000000      0x333 zephyr/libzephyr.a(hex.c.obj)
 .debug_str     0x0000000000000000      0x26f zephyr/libzephyr.a(hex.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(hex.c.obj)
 .debug_frame   0x0000000000000000       0x74 zephyr/libzephyr.a(hex.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .text.find_and_stack
                0x0000000000000000       0x3a zephyr/libzephyr.a(rb.c.obj)
 .text.stack_left_limb
                0x0000000000000000       0x40 zephyr/libzephyr.a(rb.c.obj)
 .text.set_child
                0x0000000000000000       0x12 zephyr/libzephyr.a(rb.c.obj)
 .text.rotate   0x0000000000000000       0xa8 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_get_minmax
                0x0000000000000000       0x1a zephyr/libzephyr.a(rb.c.obj)
 .text.rb_insert
                0x0000000000000000      0x134 zephyr/libzephyr.a(rb.c.obj)
 .text.rb_remove
                0x0000000000000000      0x344 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_walk
                0x0000000000000000       0x24 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_child
                0x0000000000000000        0xe zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_is_black
                0x0000000000000000        0x8 zephyr/libzephyr.a(rb.c.obj)
 .text.rb_contains
                0x0000000000000000       0x2c zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_foreach_next
                0x0000000000000000       0x56 zephyr/libzephyr.a(rb.c.obj)
 .debug_info    0x0000000000000000     0x24e3 zephyr/libzephyr.a(rb.c.obj)
 .debug_abbrev  0x0000000000000000      0x491 zephyr/libzephyr.a(rb.c.obj)
 .debug_loc     0x0000000000000000     0x2796 zephyr/libzephyr.a(rb.c.obj)
 .debug_aranges
                0x0000000000000000       0x78 zephyr/libzephyr.a(rb.c.obj)
 .debug_ranges  0x0000000000000000      0x5e0 zephyr/libzephyr.a(rb.c.obj)
 .debug_line    0x0000000000000000     0x139f zephyr/libzephyr.a(rb.c.obj)
 .debug_str     0x0000000000000000      0x465 zephyr/libzephyr.a(rb.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(rb.c.obj)
 .debug_frame   0x0000000000000000      0x17c zephyr/libzephyr.a(rb.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_timegm64
                0x0000000000000000       0xe8 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_timegm
                0x0000000000000000       0x18 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_state_update
                0x0000000000000000       0x5c zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_state_set_skew
                0x0000000000000000       0x36 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_estimate_skew
                0x0000000000000000       0xa0 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_ref_from_local
                0x0000000000000000       0xae zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_local_from_ref
                0x0000000000000000       0xa2 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_skew_to_ppb
                0x0000000000000000       0x3c zephyr/libzephyr.a(timeutil.c.obj)
 .debug_info    0x0000000000000000      0x88a zephyr/libzephyr.a(timeutil.c.obj)
 .debug_abbrev  0x0000000000000000      0x1ec zephyr/libzephyr.a(timeutil.c.obj)
 .debug_loc     0x0000000000000000      0x7b4 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_aranges
                0x0000000000000000       0x58 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_ranges  0x0000000000000000       0xc0 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_line    0x0000000000000000      0x61c zephyr/libzephyr.a(timeutil.c.obj)
 .debug_str     0x0000000000000000      0x518 zephyr/libzephyr.a(timeutil.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_frame   0x0000000000000000      0x13c zephyr/libzephyr.a(timeutil.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .text.setup_bundle_data.constprop.0
                0x0000000000000000       0x3a zephyr/libzephyr.a(bitarray.c.obj)
 .text.set_region
                0x0000000000000000       0x90 zephyr/libzephyr.a(bitarray.c.obj)
 .text.set_clear_region
                0x0000000000000000       0x46 zephyr/libzephyr.a(bitarray.c.obj)
 .text.match_region
                0x0000000000000000       0x8c zephyr/libzephyr.a(bitarray.c.obj)
 .text.is_region_set_clear
                0x0000000000000000       0x4e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_popcount_region
                0x0000000000000000       0x94 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_xor
                0x0000000000000000       0xc4 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_set_bit
                0x0000000000000000       0x3e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_clear_bit
                0x0000000000000000       0x40 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_bit
                0x0000000000000000       0x3e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_set_bit
                0x0000000000000000       0x4e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_clear_bit
                0x0000000000000000       0x50 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_alloc
                0x0000000000000000       0xa4 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_find_nth_set
                0x0000000000000000       0xba zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_free
                0x0000000000000000       0x6c zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_is_region_set
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_is_region_cleared
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_set_region
                0x0000000000000000       0x70 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_set_region
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_clear_region
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_info    0x0000000000000000     0x2569 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_abbrev  0x0000000000000000      0x476 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_loc     0x0000000000000000     0x1e0f zephyr/libzephyr.a(bitarray.c.obj)
 .debug_aranges
                0x0000000000000000       0xb8 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_ranges  0x0000000000000000      0x288 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_line    0x0000000000000000     0x17bc zephyr/libzephyr.a(bitarray.c.obj)
 .debug_str     0x0000000000000000      0x645 zephyr/libzephyr.a(bitarray.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_frame   0x0000000000000000      0x290 zephyr/libzephyr.a(bitarray.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .text.sys_slist_find_and_remove
                0x0000000000000000       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_release
                0x0000000000000000       0x48 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_reset
                0x0000000000000000       0x62 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_cancel
                0x0000000000000000       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_monitor_register
                0x0000000000000000       0x3a zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_monitor_unregister
                0x0000000000000000       0x34 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_sync_lock
                0x0000000000000000       0x14 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_sync_finalize
                0x0000000000000000       0x40 zephyr/libzephyr.a(onoff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .text.mem_attr_check_buf
                0x0000000000000000        0xe zephyr/libzephyr.a(mem_attr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(device.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(device.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(device.c.obj)
 .rodata.pm_device_state_str.str1.1
                0x0000000000000000        0x1 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_state_str
                0x0000000000000000       0x18 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_power_domain_remove
                0x0000000000000000        0x6 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_power_domain_add
                0x0000000000000000        0x6 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_is_any_busy
                0x0000000000000000       0x34 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_busy_set
                0x0000000000000000       0x16 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_busy_clear
                0x0000000000000000       0x16 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_wakeup_enable
                0x0000000000000000       0x36 zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_wakeup_is_enabled
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_wakeup_is_capable
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .text.pm_device_on_power_domain
                0x0000000000000000        0xe zephyr/libzephyr.a(device.c.obj)
 .rodata.str1.1
                0x0000000000000000       0x15 zephyr/libzephyr.a(device.c.obj)
 .rodata.CSWTCH.24
                0x0000000000000000       0x10 zephyr/libzephyr.a(device.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(device_runtime.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(device_runtime.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_put_async
                0x0000000000000000       0x3a zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_disable
                0x0000000000000000      0x11c zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_is_enabled
                0x0000000000000000       0x12 zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_usage
                0x0000000000000000       0x56 zephyr/libzephyr.a(device_runtime.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_enter
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_exit
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_exit_to_scheduler
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_idle
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_info    0x0000000000000000       0x56 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_abbrev  0x0000000000000000       0x48 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_ranges  0x0000000000000000       0x28 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_line    0x0000000000000000       0x9f zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_str     0x0000000000000000      0x1f5 zephyr/libzephyr.a(tracing_none.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_frame   0x0000000000000000       0x50 zephyr/libzephyr.a(tracing_none.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_psa_rngdata_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_psa_globaldata_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_key_slot_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.heap_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.psa_rng_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.psa_globaldata_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.key_slot_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.heap_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .text.z_get_sw_isr_table_idx
                0x0000000000000000        0x2 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_info    0x0000000000000000       0xc1 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_abbrev  0x0000000000000000       0x6b zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_loc     0x0000000000000000       0x15 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_line    0x0000000000000000       0x77 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_str     0x0000000000000000      0x25f zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .text.arch_syscall_oops
                0x0000000000000000       0x1c zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text.z_arm_save_fp_context
                0x0000000000000000       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text.z_arm_restore_fp_context
                0x0000000000000000       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_info    0x0000000000000000      0x2c1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_abbrev  0x0000000000000000      0x1b7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_loc     0x0000000000000000       0x7c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_ranges  0x0000000000000000       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_line    0x0000000000000000      0x2b8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_str     0x0000000000000000      0x311 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .text.sys_arch_reboot
                0x0000000000000000       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .text.arch_irq_disable
                0x0000000000000000       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text.arch_float_enable
                0x0000000000000000        0x6 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text.arch_irq_lock_outlined
                0x0000000000000000       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_read_ok
                0x0000000000000000       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_readwrite_ok
                0x0000000000000000       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_range_read_ok
                0x0000000000000000       0x1a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_range_readwrite_ok
                0x0000000000000000       0x1a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .text.z_arm_configure_dynamic_mpu_regions
                0x0000000000000000        0xc zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .bss.dynamic_regions.0
                0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.arm_core_mpu_configure_dynamic_mpu_regions
                0x0000000000000000       0x4c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__stdout_hook_install
                0x0000000000000000        0xc zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__stdin_hook_install
                0x0000000000000000        0xc zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._open    0x0000000000000000        0x6 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._kill    0x0000000000000000        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._getpid  0x0000000000000000        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .rodata._exit.str1.1
                0x0000000000000000        0x6 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._exit    0x0000000000000000       0x10 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_init
                0x0000000000000000       0x18 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_close
                0x0000000000000000        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_close_recursive
                0x0000000000000000        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_acquire
                0x0000000000000000        0xc zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_try_acquire
                0x0000000000000000       0x12 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_try_acquire_recursive
                0x0000000000000000       0x12 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__retarget_lock_release
                0x0000000000000000        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__errno  0x0000000000000000        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.__chk_fail
                0x0000000000000000       0x20 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._gettimeofday
                0x0000000000000000        0x6 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .rodata.chk_fail_msg.0
                0x0000000000000000       0x1e zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 ._k_sem.static.__lock___arc4random_mutex_
                0x0000000000000000       0x10 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 ._k_sem.static.__lock___dd_hash_mutex_
                0x0000000000000000       0x10 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 ._k_sem.static.__lock___tz_mutex_
                0x0000000000000000       0x10 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 ._k_sem.static.__lock___at_quick_exit_mutex_
                0x0000000000000000       0x10 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 ._k_mutex.static.__lock___env_recursive_mutex_
                0x0000000000000000       0x14 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 ._k_mutex.static.__lock___atexit_recursive_mutex_
                0x0000000000000000       0x14 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_info    0x0000000000000000       0x80 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_line    0x0000000000000000       0x61 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_str     0x0000000000000000      0x247 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_info    0x0000000000000000       0x64 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_line    0x0000000000000000       0x62 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_str     0x0000000000000000      0x22b zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_info    0x0000000000000000       0x80 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_line    0x0000000000000000       0x64 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_str     0x0000000000000000      0x24a zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.nrf53_cpunet_enable
                0x0000000000000000       0x34 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_control_get_onoff
                0x0000000000000000       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_bt_ctlr_hf_request
                0x0000000000000000       0x24 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_bt_ctlr_hf_release
                0x0000000000000000       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .text.i2c_dump_msgs_rw
                0x0000000000000000        0x2 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_info    0x0000000000000000      0x436 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_abbrev  0x0000000000000000      0x1a8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_line    0x0000000000000000      0x21c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_str     0x0000000000000000      0x453 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text.sys_clock_set_timeout
                0x0000000000000000        0x2 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text.sys_clock_idle_exit
                0x0000000000000000        0x2 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_info    0x0000000000000000       0xe1 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_abbrev  0x0000000000000000       0x7e zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_line    0x0000000000000000      0x15c zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_str     0x0000000000000000      0x288 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_evt_address_get
                0x0000000000000000       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_capture_task_address_get
                0x0000000000000000       0x14 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_int_lock
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_int_unlock
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_read
                0x0000000000000000       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_abort
                0x0000000000000000       0x58 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_get_ticks
                0x0000000000000000       0x78 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_set
                0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_exact_set
                0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_chan_alloc
                0x0000000000000000       0x3c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_chan_free
                0x0000000000000000       0x1c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_trigger_overflow
                0x0000000000000000        0x6 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_cycle_get_32
                0x0000000000000000        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_disable
                0x0000000000000000       0x30 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.alloc_mask
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text          0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .data          0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .bss           0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemCoreClockUpdate
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemStoreFICRNS
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemLockFICRNS
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .rodata.nrfx_error_string_get.str1.1
                0x0000000000000000      0x178 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .text.nrfx_error_string_get
                0x0000000000000000       0xd8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .text.nrfx_flag32_is_allocated
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_check
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_disable_all
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_disable
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_trigger
                0x0000000000000000        0xe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_endpoints_setup
                0x0000000000000000        0xe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_endpoints_clear
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_event_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_fork_endpoint_setup
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_fork_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_group_set
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_include_in_group
                0x0000000000000000       0x16 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_remove_from_group
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_clear
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_enable
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_disable
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_address_get
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_disable_task_get
                0x0000000000000000        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_enable_task_get
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_free
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_alloc
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_free
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_edge_connection_setup
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrf53_errata_4
                0x0000000000000000       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_disable
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_uninit
                0x0000000000000000       0x28 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_init_check
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_divider_set
                0x0000000000000000       0xdc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .rodata.CSWTCH.41
                0x0000000000000000        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_free
                0x0000000000000000       0x60 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_free
                0x0000000000000000       0x1c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_enable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_disable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_alloc
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_free
                0x0000000000000000       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_include_in_group
                0x0000000000000000       0x70 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_remove_from_group
                0x0000000000000000       0x70 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_clear
                0x0000000000000000       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_enable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_disable
                0x0000000000000000       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_in_event_get.isra.0
                0x0000000000000000       0x22 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_is_set
                0x0000000000000000       0x1a modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_channels_number_get
                0x0000000000000000        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_uninit
                0x0000000000000000       0x6c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_set
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_clear
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_toggle
                0x0000000000000000       0x22 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_enable
                0x0000000000000000       0x1e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_disable
                0x0000000000000000       0x1e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_get
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_address_get
                0x0000000000000000       0x12 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_get
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_address_get
                0x0000000000000000       0x16 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_get
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_address_get
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_force
                0x0000000000000000       0x2a modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_trigger
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_trigger
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_trigger
                0x0000000000000000       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_event_get
                0x0000000000000000        0x6 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_event_address_get
                0x0000000000000000       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.port_lens.0
                0x0000000000000000        0x2 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_reconfigure
                0x0000000000000000       0x64 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_init_check
                0x0000000000000000       0x1c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_uninit
                0x0000000000000000       0x5c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_is_busy
                0x0000000000000000       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_start_task_address_get
                0x0000000000000000        0xe modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_stopped_event_address_get
                0x0000000000000000        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .text.z_device_get_all_static
                0x0000000000000000       0x18 zephyr/kernel/libkernel.a(device.c.obj)
 .text.z_impl_device_get_binding
                0x0000000000000000       0x54 zephyr/kernel/libkernel.a(device.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(errno.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(errno.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(errno.c.obj)
 .text.z_impl_z_errno
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(errno.c.obj)
 .rodata._k_neg_eagain
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_info    0x0000000000000000      0x85a zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_abbrev  0x0000000000000000      0x1c0 zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_line    0x0000000000000000      0x2fa zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_str     0x0000000000000000      0x7c4 zephyr/kernel/libkernel.a(errno.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/kernel/libkernel.a(errno.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/kernel/libkernel.a(errno.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .text.k_fatal_halt
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(fatal.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_impl_device_init
                0x0000000000000000       0x28 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_early_rand_get
                0x0000000000000000       0x70 zephyr/kernel/libkernel.a(init.c.obj)
 .data.state.1  0x0000000000000000        0x8 zephyr/kernel/libkernel.a(init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_runtime_stats_get
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .text.arch_spin_relax
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(idle.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .text.z_impl_k_sem_reset
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(sem.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(work.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(work.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(work.c.obj)
 .text.handle_flush
                0x0000000000000000        0x2 zephyr/kernel/libkernel.a(work.c.obj)
 .text.cancel_sync_locked
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_init
                0x0000000000000000       0x12 zephyr/kernel/libkernel.a(work.c.obj)
 .text.work_flush_locked
                0x0000000000000000       0x8c zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_busy_get
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(work.c.obj)
 .text.z_work_submit_to_queue
                0x0000000000000000       0x26 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_submit_to_queue
                0x0000000000000000       0x20 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_submit
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_flush
                0x0000000000000000       0x36 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_cancel
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_cancel_sync
                0x0000000000000000       0x5a zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_queue_init
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_queue_drain
                0x0000000000000000       0x78 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_queue_unplug
                0x0000000000000000       0x2c zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_delayable_busy_get
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_reschedule_for_queue
                0x0000000000000000       0x5c zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_reschedule
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_cancel_delayable_sync
                0x0000000000000000       0x60 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_flush_delayable
                0x0000000000000000       0x64 zephyr/kernel/libkernel.a(work.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_is_preempt_thread
                0x0000000000000000       0x20 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_priority_get
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_name_get
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_name_copy
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.k_thread_state_str.str1.1
                0x0000000000000000        0x3 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_state_str
                0x0000000000000000       0x90 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_init_thread_base
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_user_mode_enter
                0x0000000000000000       0x1c zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_get
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_all_get
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_cpu_get
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.str1.1
                0x0000000000000000       0x41 zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.state_string.0
                0x0000000000000000       0x40 zephyr/kernel/libkernel.a(thread.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_prio_cmp
                0x0000000000000000       0x12 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_requeue_current
                0x0000000000000000       0x50 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.move_thread_to_end_of_prio_q
                0x0000000000000000       0x74 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_ready_thread_locked
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_move_thread_to_end_of_prio_q
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_pend_thread
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_thread_no_timeout
                0x0000000000000000       0x22 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend1_no_timeout
                0x0000000000000000       0x2c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_thread
                0x0000000000000000       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_resume
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_swap_next_thread
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_all
                0x0000000000000000       0x26 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.init_ready_q
                0x0000000000000000        0x8 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_priority_set
                0x0000000000000000       0x2c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.k_can_yield
                0x0000000000000000       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_usleep
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_wakeup
                0x0000000000000000       0x58 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_join
                0x0000000000000000       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.timeout_rem
                0x0000000000000000       0x34 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_timeout_remaining
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_timeout_expires
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_get_next_timeout_expiry
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_impl_k_uptime_ticks
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.sys_timepoint_calc
                0x0000000000000000       0x48 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.sys_timepoint_timeout
                0x0000000000000000       0x42 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(events.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(events.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(events.c.obj)
 .text.z_impl_k_event_init
                0x0000000000000000        0xa zephyr/kernel/libkernel.a(events.c.obj)
 .text.z_impl_k_event_post
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(events.c.obj)
 .text.z_impl_k_event_set_masked
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(events.c.obj)
 .text.z_impl_k_event_wait_all
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(events.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init_hmac_drbg
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_deinit
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_is_initialized
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_rng_is_initialized
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_nonce_seed
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_boot_seed
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss.nrf_cc3xx_platform_rng_initialized
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .rodata.RndStartupTest.constprop.0.str1.4
                0x0000000000000000       0x6f C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.RndStartupTest.constprop.0
                0x0000000000000000       0xac C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInit
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInit_HMAC_DRBG
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_RandomSeedsFilled
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibFini
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInitRngModule
                0x0000000000000000       0xbc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.rndWorkbuff.0
                0x0000000000000000      0x220 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.random_seed_filled
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.tfm_random_seed_buff
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.eits_random_nonce_buff
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.invalid_chacha_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.invalid_aes_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.random_seed_buffer
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalTerminate
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBit
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBitRNG
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalMaskInterrupt
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalWaitInterrupt
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalWaitInterruptRND
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .data.CCApbFilteringRegMutex
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text.CC_PalWaitInterruptRND
                0x0000000000000000       0x3c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text.CC_PalWaitInterrupt
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalSecMemCmp
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCmpPlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCopyPlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemMovePlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemSetPlat
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemSetZeroPlat
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text.CC_PalMutexLock
                0x0000000000000000       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text.CC_PalMutexUnlock
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeStatus
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeSelect
                0x0000000000000000       0x84 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_init
                0x0000000000000000       0x60 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_free
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_init
                0x0000000000000000       0x60 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_free
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss.global_ctx
                0x0000000000000000      0x250 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text.mbedtls_zeroize_internal
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text.cc_mbedtls_platform_zeroize
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.startTrngHW
                0x0000000000000000      0x130 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_StartTrngHW
                0x0000000000000000      0x10c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RepetitionCounterTest
                0x0000000000000000       0x64 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_AdaptiveProportionTest
                0x0000000000000000       0x80 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.getTrngSource
                0x0000000000000000      0x2a8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_GetTrngSource
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RunTrngStartupTest
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.block_cipher_df
                0x0000000000000000      0x1d0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.ctr_drbg_update_internal
                0x0000000000000000      0x174 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.mbedtls_ctr_drbg_reseed_internal
                0x0000000000000000       0xc4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_init
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_free
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_prediction_resistance
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_entropy_len
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_nonce_len
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_reseed_interval
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_update
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_reseed
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_seed
                0x0000000000000000       0xa0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random_with_add
                0x0000000000000000      0x1e4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss.buf.0     0x0000000000000000      0x1a0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss.seed.1    0x0000000000000000      0x180 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.entropy_update
                0x0000000000000000       0x7c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.entropy_gather_internal.part.0
                0x0000000000000000       0x88 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_init
                0x0000000000000000       0x84 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_free
                0x0000000000000000       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_add_source
                0x0000000000000000       0x64 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_update_manual
                0x0000000000000000       0x4c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_gather
                0x0000000000000000       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_func
                0x0000000000000000      0x110 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_sha256_starts.constprop.0
                0x0000000000000000      0x108 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_init
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_update
                0x0000000000000000      0x170 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_drbg_reseed_core
                0x0000000000000000       0xac C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed_buf
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_reseed
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed
                0x0000000000000000       0x54 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_prediction_resistance
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_entropy_len
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_reseed_interval
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random_with_add
                0x0000000000000000      0x144 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random
                0x0000000000000000       0x4c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_free
                0x0000000000000000       0x40 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .bss.hmac_ctx  0x0000000000000000       0x80 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .text.RNG_PLAT_SetUserRngParameters
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .text.CC_PalTrngParamGet
                0x0000000000000000       0xac C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_init
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_free
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_lock
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_unlock
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .rodata.mbedtls_threading_set_alt.str1.4
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mbedtls_threading_set_alt
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mbedtls_threading_free_alt
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_unlock
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_lock
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_free
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_init
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.Mult32x32
                0x0000000000000000       0x40 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.Mult48x16
                0x0000000000000000       0x1c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_EntropyEstimateFull
                0x0000000000000000      0x524 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_WaitRngInterrupt
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetRoscSampleCnt
                0x0000000000000000       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetFastestRosc
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetCountRoscs
                0x0000000000000000       0x18 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_TurnOffTrng
                0x0000000000000000       0x24 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_RndCprngt
                0x0000000000000000       0x58 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .rodata.mbedtls_hardware_poll.str1.4
                0x0000000000000000       0x6e C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .text.mbedtls_hardware_poll
                0x0000000000000000      0x104 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.rndState.0
                0x0000000000000000        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.trngParams.1
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.rndWorkBuffer.2
                0x0000000000000000      0x220 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .rodata.cc_mbedtls_aes_init.str1.4
                0x0000000000000000       0x13 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_init
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_free
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_enc
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_dec
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ecb
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cbc
                0x0000000000000000       0x78 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb128
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb8
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ctr
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ofb
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_encrypt
                0x0000000000000000       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_decrypt
                0x0000000000000000       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_init.str1.4
                0x0000000000000000        0xe C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_init
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_free
                0x0000000000000000        0xc C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_clone.str1.4
                0x0000000000000000       0x15 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_clone
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_starts
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_internal_sha256_process
                0x0000000000000000       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_update
                0x0000000000000000       0x54 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_finish
                0x0000000000000000       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_process_internal
                0x0000000000000000       0xe0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_starts_internal
                0x0000000000000000       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_finish_internal
                0x0000000000000000       0x5c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_update_internal
                0x0000000000000000      0x1ec C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .text.cc_mbedtls_sha256
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .bss.ctx.0     0x0000000000000000       0xf4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .text.SetDataBuffersInfo
                0x0000000000000000       0x64 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.InitHashDrv
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.ProcessHashDrv.str1.4
                0x0000000000000000       0x6f C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.ProcessHashDrv
                0x0000000000000000      0x210 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.FinishHashDrv
                0x0000000000000000       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA256
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA224
                0x0000000000000000       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA1
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.InitAes  0x0000000000000000       0xc8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.LoadAesKey
                0x0000000000000000      0x114 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.write_invalid_key
                0x0000000000000000       0x50 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .rodata.ProcessAesDrv.str1.4
                0x0000000000000000       0x6f C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.ProcessAesDrv
                0x0000000000000000      0x338 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.FinishAesDrv
                0x0000000000000000      0x200 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_write_key_slot
                0x0000000000000000       0xd8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_verify_kdf_input
                0x0000000000000000       0x24 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_convert_keybits_to_keysize
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_slot_and_size
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_slot_and_size_no_kdr
                0x0000000000000000       0x2c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_kdr_slot_and_size
                0x0000000000000000       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_load_key_chacha20
                0x0000000000000000      0x108 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_load_key_aes
                0x0000000000000000      0x200 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_derive_cmac
                0x0000000000000000      0x120 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_shadow_key_derive
                0x0000000000000000       0x98 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.write_invalid_chacha20_key
                0x0000000000000000       0x40 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_prepare_chacha_key
                0x0000000000000000       0x4c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_load_chacha_key
                0x0000000000000000       0x68 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_cleanup_chacha_key
                0x0000000000000000       0x3c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text.UtilCmacBuildDataForDerivation
                0x0000000000000000       0xc0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text.UtilCmacDeriveKey
                0x0000000000000000       0xf0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .text.CC_PalDataBufferAttrGet
                0x0000000000000000        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-ctype_.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-ctype_.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-ctype_.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
 .text.free     0x0000000000000000       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
 .text.memcmp   0x0000000000000000       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
 .debug_frame   0x0000000000000000       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
 .text.memmove  0x0000000000000000       0x34 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
 .debug_frame   0x0000000000000000       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
 .text          0x0000000000000000       0x14 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strcmp.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strcmp.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strcmp.o)
 .debug_frame   0x0000000000000000       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strcmp.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strlen.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strlen.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .text.__localeconv_l
                0x0000000000000000        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .text.localeconv
                0x0000000000000000        0x8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text.__s2b    0x0000000000000000       0x94 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text.__ulp    0x0000000000000000       0x4c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text.__b2d    0x0000000000000000       0x9c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text.__ratio  0x0000000000000000       0x64 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text._mprec_log10
                0x0000000000000000       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text.__copybits
                0x0000000000000000       0x46 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text.__any_on
                0x0000000000000000       0x42 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .rodata.__mprec_tinytens
                0x0000000000000000       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .text.cleanup_glue
                0x0000000000000000       0x1a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .text._reclaim_reent
                0x0000000000000000       0xb8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
 .text.__assert
                0x0000000000000000        0xa C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
 .text._fprintf_r
                0x0000000000000000       0x1a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .text._setlocale_r
                0x0000000000000000       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x0000000000000000        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .text.setlocale
                0x0000000000000000       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .bss._PathLocale
                0x0000000000000000        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x0000000000000000       0x14 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .text.__sprint_r
                0x0000000000000000       0x1a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .text.vfprintf
                0x0000000000000000       0x14 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
 .text.__swbuf  0x0000000000000000       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x0000000000000000       0x14 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
 .text.fflush   0x0000000000000000       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text.__fp_lock
                0x0000000000000000       0x18 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text.__fp_unlock
                0x0000000000000000       0x18 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text._cleanup
                0x0000000000000000        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text.__fp_lock_all
                0x0000000000000000       0x1c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text.__fp_unlock_all
                0x0000000000000000       0x1c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
 .text.__sfvwrite_r
                0x0000000000000000      0x2a0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
 .debug_frame   0x0000000000000000       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
 .text._fwalk   0x0000000000000000       0x3a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
 .text._realloc_r
                0x0000000000000000       0x4a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
 .debug_frame   0x0000000000000000       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
 .text.__seofread
                0x0000000000000000        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
 .text._malloc_usable_size_r
                0x0000000000000000       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
 .debug_frame   0x0000000000000000       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
 .text          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
 .data          0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
 .bss           0x0000000000000000        0x0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
 .text          0x0000000000000000       0xc8 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(cmse.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(cmse.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(cmse.o)
 .debug_frame   0x0000000000000000       0x28 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(cmse.o)
 .text          0x0000000000000000      0x254 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x0000000000000000       0x30 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .text          0x0000000000000000       0xa0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .text          0x0000000000000000       0xa0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .debug_frame   0x0000000000000000       0x44 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .text          0x0000000000000000       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_popcountsi2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_popcountsi2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_popcountsi2.o)
 .debug_frame   0x0000000000000000       0x20 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_popcountsi2.o)
 .text          0x0000000000000000       0x2e c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .debug_frame   0x0000000000000000       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .text          0x0000000000000000       0x3c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x0000000000000000       0x2c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
 .text          0x0000000000000000       0x40 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000100000 xr
RAM              0x0000000020000000 0x0000000000070000 xw
IDT_LIST         0x00000000ffff7fff 0x0000000000008000 xw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
LOAD zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
LOAD zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
LOAD app/libapp.a
LOAD zephyr/libzephyr.a
LOAD zephyr/arch/common/libarch__common.a
LOAD zephyr/arch/arch/arm/core/libarch__arm__core.a
LOAD zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a
LOAD zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a
LOAD zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a
LOAD zephyr/lib/libc/newlib/liblib__libc__newlib.a
LOAD zephyr/lib/libc/common/liblib__libc__common.a
LOAD zephyr/soc/soc/nrf5340/libsoc__nordic.a
LOAD zephyr/drivers/clock_control/libdrivers__clock_control.a
LOAD zephyr/drivers/gpio/libdrivers__gpio.a
LOAD zephyr/drivers/i2c/libdrivers__i2c.a
LOAD zephyr/drivers/pinctrl/libdrivers__pinctrl.a
LOAD zephyr/drivers/timer/libdrivers__timer.a
LOAD modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a
LOAD modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a
LOAD zephyr/kernel/libkernel.a
LOAD zephyr/arch/common/libisr_tables.a
LOAD C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a
LOAD C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a
LOAD C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libm_nano.a
LOAD C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a
LOAD c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a
LOAD C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a
                0x0000000000000020                _region_min_align = 0x20

.rel.plt        0x0000000000000000        0x0
 *(SORT_BY_ALIGNMENT(.rel.plt))
                [!provide]                        PROVIDE (__rel_iplt_start = .)
 *(SORT_BY_ALIGNMENT(.rel.iplt))
 .rel.iplt      0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                [!provide]                        PROVIDE (__rel_iplt_end = .)

.rela.plt       0x0000000000000000        0x0
 *(SORT_BY_ALIGNMENT(.rela.plt))
                [!provide]                        PROVIDE (__rela_iplt_start = .)
 *(SORT_BY_ALIGNMENT(.rela.iplt))
                [!provide]                        PROVIDE (__rela_iplt_end = .)

.rel.dyn
 *(SORT_BY_ALIGNMENT(.rel.*))

.rela.dyn
 *(SORT_BY_ALIGNMENT(.rela.*))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.plt))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.iplt))
                0x0000000000000000                __rom_region_start = 0x0

rom_start       0x0000000000000000      0x154
                0x0000000000000000                __rom_start_address = .
 FILL mask 0x00
                0x0000000000000000                . = (. + (0x0 - (. - __rom_start_address)))
                0x0000000000000000                . = ALIGN (0x4)
                0x0000000000000000                . = ALIGN (0x80)
                0x0000000000000000                . = ALIGN (0x200)
                0x0000000000000000                _vector_start = .
 *(SORT_BY_ALIGNMENT(.exc_vector_table))
 *(SORT_BY_ALIGNMENT(.exc_vector_table.*))
 .exc_vector_table._vector_table_section
                0x0000000000000000       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
                0x0000000000000000                _vector_table
 *(SORT_BY_ALIGNMENT(.vectors))
                0x0000000000000154                _vector_end = .
                0x0000000000000040                . = ALIGN (0x4)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.irq_vector_table*))
 .gnu.linkonce.irq_vector_table
                0x0000000000000040      0x114 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                0x0000000000000040                _irq_vector_table
                0x0000000000000154                _vector_end = .

text            0x0000000000000158     0xa2bc
                0x0000000000000158                __text_region_start = .
 *(SORT_BY_ALIGNMENT(.text))
 .text          0x0000000000000158      0x378 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
                0x0000000000000158                __aeabi_drsub
                0x0000000000000160                __aeabi_dsub
                0x0000000000000160                __subdf3
                0x0000000000000164                __aeabi_dadd
                0x0000000000000164                __adddf3
                0x00000000000003dc                __floatunsidf
                0x00000000000003dc                __aeabi_ui2d
                0x00000000000003fc                __floatsidf
                0x00000000000003fc                __aeabi_i2d
                0x0000000000000420                __aeabi_f2d
                0x0000000000000420                __extendsfdf2
                0x0000000000000464                __floatundidf
                0x0000000000000464                __aeabi_ul2d
                0x0000000000000474                __floatdidf
                0x0000000000000474                __aeabi_l2d
 .text          0x00000000000004d0      0x424 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
                0x00000000000004d0                __aeabi_dmul
                0x00000000000004d0                __muldf3
                0x0000000000000724                __divdf3
                0x0000000000000724                __aeabi_ddiv
 .text          0x00000000000008f4      0x110 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
                0x00000000000008f4                __gtdf2
                0x00000000000008f4                __gedf2
                0x00000000000008fc                __ltdf2
                0x00000000000008fc                __ledf2
                0x0000000000000904                __nedf2
                0x0000000000000904                __eqdf2
                0x0000000000000904                __cmpdf2
                0x0000000000000980                __aeabi_cdrcmple
                0x0000000000000990                __aeabi_cdcmpeq
                0x0000000000000990                __aeabi_cdcmple
                0x00000000000009a0                __aeabi_dcmpeq
                0x00000000000009b4                __aeabi_dcmplt
                0x00000000000009c8                __aeabi_dcmple
                0x00000000000009dc                __aeabi_dcmpge
                0x00000000000009f0                __aeabi_dcmpgt
 .text          0x0000000000000a04       0x2c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
                0x0000000000000a04                __unorddf2
                0x0000000000000a04                __aeabi_dcmpun
 .text          0x0000000000000a30       0x50 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
                0x0000000000000a30                __aeabi_d2iz
                0x0000000000000a30                __fixdfsi
 .text          0x0000000000000a80       0x30 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
                0x0000000000000a80                __aeabi_uldivmod
 .text          0x0000000000000ab0        0x4 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
                0x0000000000000ab0                __aeabi_idiv0
                0x0000000000000ab0                __aeabi_ldiv0
 .text          0x0000000000000ab4       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strlen.o)
                0x0000000000000ab4                strlen
 .text          0x0000000000000ac4      0x29c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
                0x0000000000000ac4                __udivmoddi4
 *(SORT_BY_ALIGNMENT(.text.*))
 .text.z_cbvprintf_impl
                0x0000000000000d60      0xd1c zephyr/libzephyr.a(cbprintf_complete.c.obj)
                0x0000000000000d60                z_cbvprintf_impl
 *fill*         0x0000000000001a7c        0x4 
 .text._dtoa_r  0x0000000000001a80      0xc54 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
                0x0000000000001a80                _dtoa_r
 .text.main     0x00000000000026d4       0x74 app/libapp.a(main_temp_low_power.c.obj)
                0x00000000000026d4                main
 .text.i2c_driver_init
                0x0000000000002748       0x18 app/libapp.a(zephyr_i2c_driver.c.obj)
                0x0000000000002748                i2c_driver_init
 .text.i2c_write_bytes
                0x0000000000002760       0x2c app/libapp.a(zephyr_i2c_driver.c.obj)
                0x0000000000002760                i2c_write_bytes
 .text.i2c_read_bytes
                0x000000000000278c       0x2c app/libapp.a(zephyr_i2c_driver.c.obj)
                0x000000000000278c                i2c_read_bytes
 .text.i2c_write_read_bytes
                0x00000000000027b8       0x3c app/libapp.a(zephyr_i2c_driver.c.obj)
                0x00000000000027b8                i2c_write_read_bytes
 .text.m117_read_temperature
                0x00000000000027f4       0x58 app/libapp.a(m117_sensor.c.obj)
                0x00000000000027f4                m117_read_temperature
 .text.char_out
                0x000000000000284c        0xc zephyr/libzephyr.a(printk.c.obj)
 .text.vprintk  0x0000000000002858       0x1c zephyr/libzephyr.a(printk.c.obj)
                0x0000000000002858                vprintk
 .text.extract_decimal
                0x0000000000002874       0x2c zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.encode_uint
                0x00000000000028a0       0x9c zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.process_event
                0x000000000000293c      0x21c zephyr/libzephyr.a(onoff.c.obj)
 .text.mem_attr_get_regions
                0x0000000000002b58        0xc zephyr/libzephyr.a(mem_attr.c.obj)
                0x0000000000002b58                mem_attr_get_regions
 .text.pm_device_action_run
                0x0000000000002b64       0x8c zephyr/libzephyr.a(device.c.obj)
                0x0000000000002b64                pm_device_action_run
 .text.runtime_suspend.isra.0
                0x0000000000002bf0       0x98 zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_get
                0x0000000000002c88      0x180 zephyr/libzephyr.a(device_runtime.c.obj)
                0x0000000000002c88                pm_device_runtime_get
 .text.pm_device_runtime_enable
                0x0000000000002e08       0xe0 zephyr/libzephyr.a(device_runtime.c.obj)
                0x0000000000002e08                pm_device_runtime_enable
 .text.boot_banner
                0x0000000000002ee8       0x1c zephyr/libzephyr.a(banner.c.obj)
                0x0000000000002ee8                boot_banner
 .text.nrf_cc3xx_platform_abort_init
                0x0000000000002f04        0xc zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                0x0000000000002f04                nrf_cc3xx_platform_abort_init
 .text.mutex_free_platform
                0x0000000000002f10       0x54 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_lock_platform
                0x0000000000002f64       0x74 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_unlock_platform
                0x0000000000002fd8       0x68 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_init_platform
                0x0000000000003040       0x94 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.nrf_cc3xx_platform_mutex_init
                0x00000000000030d4       0x2c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000000030d4                nrf_cc3xx_platform_mutex_init
 .text.z_SysNmiOnReset
                0x0000000000003100        0x8 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                0x0000000000003100                z_SysNmiOnReset
 .text._HandlerModeExit
                0x0000000000003108       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                0x0000000000003108                z_arm_int_exit
                0x0000000000003108                z_arm_exc_exit
 .text.usage_fault.constprop.0
                0x0000000000003130       0x5c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.bus_fault.constprop.0
                0x000000000000318c       0x6c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.mem_manage_fault.constprop.0
                0x00000000000031f8       0x78 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.z_arm_fault
                0x0000000000003270      0x118 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                0x0000000000003270                z_arm_fault
 .text.z_arm_fault_init
                0x0000000000003388       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                0x0000000000003388                z_arm_fault_init
 .text.__fault  0x00000000000033a8       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                0x00000000000033a8                z_arm_usage_fault
                0x00000000000033a8                z_arm_mpu_fault
                0x00000000000033a8                z_arm_exc_spurious
                0x00000000000033a8                z_arm_debug_monitor
                0x00000000000033a8                z_arm_hard_fault
                0x00000000000033a8                z_arm_bus_fault
 .text._reset_section
                0x00000000000033bc       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                0x00000000000033bc                __start
                0x00000000000033bc                z_arm_reset
 .text.z_arm_clear_arm_mpu_config
                0x000000000000341c       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                0x000000000000341c                z_arm_clear_arm_mpu_config
 .text.z_arm_init_arch_hw_at_boot
                0x0000000000003440       0x4c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                0x0000000000003440                z_arm_init_arch_hw_at_boot
 .text.z_impl_k_thread_abort
                0x000000000000348c       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                0x000000000000348c                z_impl_k_thread_abort
 .text.arch_swap
                0x00000000000034b8       0x34 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                0x00000000000034b8                arch_swap
 .text.z_arm_pendsv_c
                0x00000000000034ec       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                0x00000000000034ec                z_arm_pendsv_c
 .text.z_arm_pendsv
                0x0000000000003534       0x7c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                0x0000000000003534                z_arm_pendsv
 .text.z_arm_svc
                0x00000000000035b0       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                0x00000000000035b0                z_arm_svc
 .text.arch_irq_enable
                0x00000000000035d0       0x1c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x00000000000035d0                arch_irq_enable
 .text.arch_irq_is_enabled
                0x00000000000035ec       0x1c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x00000000000035ec                arch_irq_is_enabled
 .text.z_arm_irq_priority_set
                0x0000000000003608       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x0000000000003608                z_arm_irq_priority_set
 .text.z_prep_c
                0x0000000000003634       0x5c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                0x0000000000003634                z_prep_c
 .text.arch_new_thread
                0x0000000000003690       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000003690                arch_new_thread
 .text.arch_float_disable
                0x00000000000036d0       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x00000000000036d0                arch_float_disable
 .text.arch_switch_to_main_thread
                0x0000000000003718       0x58 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000003718                arch_switch_to_main_thread
 .text.z_arm_cpu_idle_init
                0x0000000000003770        0xc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000003770                z_arm_cpu_idle_init
 .text.z_arm_interrupt_init
                0x000000000000377c       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                0x000000000000377c                z_arm_interrupt_init
 .text._isr_wrapper
                0x0000000000003794       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                0x0000000000003794                _isr_wrapper
 .text.z_arm_configure_static_mpu_regions
                0x00000000000037b8       0x38 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                0x00000000000037b8                z_arm_configure_static_mpu_regions
 .text.region_init
                0x00000000000037f0       0x34 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.region_allocate_and_init
                0x0000000000003824       0x24 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.mpu_configure_regions_and_partition.constprop.0
                0x0000000000003848      0x148 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.arm_core_mpu_enable
                0x0000000000003990       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000003990                arm_core_mpu_enable
 .text.arm_core_mpu_disable
                0x00000000000039a8       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x00000000000039a8                arm_core_mpu_disable
 .text.arm_core_mpu_configure_static_mpu_regions
                0x00000000000039bc       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x00000000000039bc                arm_core_mpu_configure_static_mpu_regions
 .text.arm_core_mpu_mark_areas_for_dynamic_regions
                0x00000000000039d0       0xb8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x00000000000039d0                arm_core_mpu_mark_areas_for_dynamic_regions
 .text.z_arm_mpu_init
                0x0000000000003a88      0x11c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000003a88                z_arm_mpu_init
 .text.z_impl_zephyr_read_stdin
                0x0000000000003ba4       0x28 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000003ba4                z_impl_zephyr_read_stdin
 .text.z_impl_zephyr_write_stdout
                0x0000000000003bcc       0x30 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000003bcc                z_impl_zephyr_write_stdout
 .text._sbrk    0x0000000000003bfc       0x28 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000003bfc                sbrk
                0x0000000000003bfc                _sbrk
 .text.abort    0x0000000000003c24       0x1c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
                0x0000000000003c24                abort
 .text.nrf_gpio_pin_control_select
                0x0000000000003c40       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.nordicsemi_nrf53_init
                0x0000000000003c7c       0x4c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.rtc_pretick_init
                0x0000000000003cc8       0x60 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.arch_busy_wait
                0x0000000000003d28       0x24 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                0x0000000000003d28                arch_busy_wait
 .text.nrf53_cpunet_mgmt_init
                0x0000000000003d4c       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.onoff_start
                0x0000000000003d5c       0x6c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.z_sys_poweroff
                0x0000000000003dc8       0x14 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
                0x0000000000003dc8                z_sys_poweroff
 .text.onoff_stop
                0x0000000000003ddc       0x30 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.onoff_start
                0x0000000000003e0c       0x44 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.generic_hfclk_stop
                0x0000000000003e50       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clk_init
                0x0000000000003e84       0x70 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_blocking_start
                0x0000000000003ef4       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clkstarted_handle.constprop.0
                0x0000000000003f28       0x4c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.generic_hfclk_start
                0x0000000000003f74       0x7c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clock_event_handler
                0x0000000000003ff0       0x30 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_control_lf_on
                0x0000000000004020       0xf8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000004020                z_nrf_clock_control_lf_on
 .text.gpio_nrfx_pin_interrupt_configure
                0x0000000000004118      0x100 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_init
                0x0000000000004218       0x48 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.nrfx_gpio_handler
                0x0000000000004260       0x4c zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_pin_configure
                0x00000000000042ac      0x188 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.event_handler
                0x0000000000004434       0x38 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.i2c_nrfx_twim_transfer
                0x000000000000446c      0x154 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.i2c_nrfx_twim_recover_bus
                0x00000000000045c0       0x54 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x00000000000045c0                i2c_nrfx_twim_recover_bus
 .text.i2c_nrfx_twim_msg_transfer
                0x0000000000004614       0x6c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x0000000000004614                i2c_nrfx_twim_msg_transfer
 .text.i2c_nrfx_twim_common_init
                0x0000000000004680       0x40 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x0000000000004680                i2c_nrfx_twim_common_init
 .text.nrf_gpio_pin_port_decode
                0x00000000000046c0       0x24 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .text.pinctrl_configure_pins
                0x00000000000046e4      0x168 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                0x00000000000046e4                pinctrl_configure_pins
 .text.compare_int_lock
                0x000000000000484c       0x40 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_timeout_handler
                0x000000000000488c       0x48 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.compare_int_unlock
                0x00000000000048d4       0x4c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_read
                0x0000000000004920       0x44 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000004920                z_nrf_rtc_timer_read
 .text.compare_set
                0x0000000000004964      0x124 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_driver_init
                0x0000000000004a88       0x90 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.rtc_nrf_isr
                0x0000000000004b18       0xd0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000004b18                rtc_nrf_isr
 .text.sys_clock_set_timeout
                0x0000000000004be8       0x5c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000004be8                sys_clock_set_timeout
 .text.sys_clock_elapsed
                0x0000000000004c44       0x14 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000004c44                sys_clock_elapsed
 .text.nrf53_errata_42
                0x0000000000004c58       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemInit
                0x0000000000004c7c      0x1d8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                0x0000000000004c7c                SystemInit
 .text.nrfx_flag32_alloc
                0x0000000000004e54       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                0x0000000000004e54                nrfx_flag32_alloc
 .text.nrfx_flag32_free
                0x0000000000004e94       0x38 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                0x0000000000004e94                nrfx_flag32_free
 .text.nrfx_gppi_channels_enable
                0x0000000000004ecc        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000004ecc                nrfx_gppi_channels_enable
 .text.nrfx_gppi_channel_alloc
                0x0000000000004ed8        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000004ed8                nrfx_gppi_channel_alloc
 .text.nrfx_clock_init
                0x0000000000004ee4       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000004ee4                nrfx_clock_init
 .text.nrfx_clock_enable
                0x0000000000004f04       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000004f04                nrfx_clock_enable
 .text.nrfx_clock_start
                0x0000000000004f34       0xd0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000004f34                nrfx_clock_start
 .text.nrfx_power_clock_irq_handler
                0x0000000000005004       0xb0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000005004                nrfx_power_clock_irq_handler
 .text.nrfx_dppi_channel_alloc
                0x00000000000050b4       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                0x00000000000050b4                nrfx_dppi_channel_alloc
 .text.nrf_gpio_pin_port_decode
                0x00000000000050c4       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.get_pin_idx
                0x00000000000050e8       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.call_handler.constprop.0
                0x00000000000050fc       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_te_get.constprop.0
                0x000000000000513c       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_is_output.constprop.0
                0x0000000000005154       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_in_use_by_te.constprop.0
                0x000000000000516c       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.latch_pending_read_and_check
                0x0000000000005184       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.release_handler.isra.0
                0x00000000000051c0       0x58 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.__nrfy_internal_gpiote_events_process.constprop.0
                0x0000000000005218       0x5c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_uninit
                0x0000000000005274       0x84 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_input_configure
                0x00000000000052f8      0x178 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000052f8                nrfx_gpiote_input_configure
 .text.nrfx_gpiote_output_configure
                0x0000000000005470      0x104 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000005470                nrfx_gpiote_output_configure
 .text.nrfx_gpiote_global_callback_set
                0x0000000000005574        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000005574                nrfx_gpiote_global_callback_set
 .text.nrfx_gpiote_channel_get
                0x0000000000005580       0x34 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000005580                nrfx_gpiote_channel_get
 .text.nrfx_gpiote_init
                0x00000000000055b4       0x64 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000055b4                nrfx_gpiote_init
 .text.nrfx_gpiote_init_check
                0x0000000000005618       0x14 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000005618                nrfx_gpiote_init_check
 .text.nrfx_gpiote_channel_free
                0x000000000000562c        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x000000000000562c                nrfx_gpiote_channel_free
 .text.nrfx_gpiote_channel_alloc
                0x0000000000005638        0xc modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000005638                nrfx_gpiote_channel_alloc
 .text.nrfx_gpiote_trigger_enable
                0x0000000000005644       0xac modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000005644                nrfx_gpiote_trigger_enable
 .text.nrfx_gpiote_0_irq_handler
                0x00000000000056f0      0x1b8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000056f0                nrfx_gpiote_0_irq_handler
 .text.nrf_gpio_cfg.constprop.0
                0x00000000000058a8       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.twim_configure
                0x00000000000058f0       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrfx_twim_init
                0x0000000000005930       0x74 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000005930                nrfx_twim_init
 .text.nrfx_twim_enable
                0x00000000000059a4       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x00000000000059a4                nrfx_twim_enable
 .text.nrfx_twim_disable
                0x00000000000059c4       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x00000000000059c4                nrfx_twim_disable
 .text.nrfx_twim_xfer
                0x0000000000005a00      0x350 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000005a00                nrfx_twim_xfer
 .text.nrfx_twim_1_irq_handler
                0x0000000000005d50      0x188 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                0x0000000000005d50                nrfx_twim_1_irq_handler
 .text.nrf_gpio_pin_port_decode
                0x0000000000005ed8       0x24 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .text.nrfx_twi_twim_bus_recover
                0x0000000000005efc       0xd4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
                0x0000000000005efc                nrfx_twi_twim_bus_recover
 .text.z_fatal_error
                0x0000000000005fd0       0x30 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x0000000000005fd0                z_fatal_error
 .text.z_sys_init_run_level
                0x0000000000006000       0x2c zephyr/kernel/libkernel.a(init.c.obj)
 .text.bg_thread_main
                0x000000000000602c       0xc4 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_bss_zero
                0x00000000000060f0       0x18 zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000000060f0                z_bss_zero
 .text.z_init_cpu
                0x0000000000006108       0x68 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000006108                z_init_cpu
 .text.z_cstart
                0x0000000000006170       0xdc zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000006170                z_cstart
 .text.init_mem_slab_obj_core_list
                0x000000000000624c       0x24 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_alloc
                0x0000000000006270       0x5c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x0000000000006270                k_mem_slab_alloc
 .text.z_impl_k_mutex_lock
                0x00000000000062cc       0xf0 zephyr/kernel/libkernel.a(mutex.c.obj)
                0x00000000000062cc                z_impl_k_mutex_lock
 .text.z_impl_k_mutex_unlock
                0x00000000000063bc       0x78 zephyr/kernel/libkernel.a(mutex.c.obj)
                0x00000000000063bc                z_impl_k_mutex_unlock
 .text.z_impl_k_sem_give
                0x0000000000006434       0x48 zephyr/kernel/libkernel.a(sem.c.obj)
                0x0000000000006434                z_impl_k_sem_give
 .text.z_impl_k_sem_take
                0x000000000000647c       0x4c zephyr/kernel/libkernel.a(sem.c.obj)
                0x000000000000647c                z_impl_k_sem_take
 .text.work_queue_main
                0x00000000000064c8      0x138 zephyr/kernel/libkernel.a(work.c.obj)
 .text.submit_to_queue_locked
                0x0000000000006600       0xb4 zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_queue_start
                0x00000000000066b4       0x7c zephyr/kernel/libkernel.a(work.c.obj)
                0x00000000000066b4                k_work_queue_start
 .text.k_work_schedule_for_queue
                0x0000000000006730       0x54 zephyr/kernel/libkernel.a(work.c.obj)
                0x0000000000006730                k_work_schedule_for_queue
 .text.k_work_schedule
                0x0000000000006784        0xc zephyr/kernel/libkernel.a(work.c.obj)
                0x0000000000006784                k_work_schedule
 .text.z_setup_new_thread
                0x0000000000006790       0x68 zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000006790                z_setup_new_thread
 .text.z_impl_k_thread_create
                0x00000000000067f8       0x58 zephyr/kernel/libkernel.a(thread.c.obj)
                0x00000000000067f8                z_impl_k_thread_create
 .text.update_cache
                0x0000000000006850       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.unready_thread
                0x0000000000006880       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.ready_thread
                0x00000000000068b0       0x68 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_thread_halt
                0x0000000000006918       0xf0 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_pend_curr
                0x0000000000006a08       0x50 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006a08                z_pend_curr
 .text.z_thread_prio_set
                0x0000000000006a58       0x88 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006a58                z_thread_prio_set
 .text.z_reschedule
                0x0000000000006ae0       0x24 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006ae0                z_reschedule
 .text.z_sched_start
                0x0000000000006b04       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006b04                z_sched_start
 .text.z_reschedule_irqlock
                0x0000000000006b40       0x28 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006b40                z_reschedule_irqlock
 .text.k_sched_lock
                0x0000000000006b68       0x28 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006b68                k_sched_lock
 .text.k_sched_unlock
                0x0000000000006b90       0x40 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006b90                k_sched_unlock
 .text.z_sched_init
                0x0000000000006bd0       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006bd0                z_sched_init
 .text.z_impl_k_yield
                0x0000000000006be0       0x7c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006be0                z_impl_k_yield
 .text.z_tick_sleep
                0x0000000000006c5c       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_sleep
                0x0000000000006cdc       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006cdc                z_impl_k_sleep
 .text.z_impl_k_sched_current_thread_query
                0x0000000000006d18        0xc zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006d18                z_impl_k_sched_current_thread_query
 .text.z_sched_wait
                0x0000000000006d24       0x24 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006d24                z_sched_wait
 .text.z_data_copy
                0x0000000000006d48       0x34 zephyr/kernel/libkernel.a(xip.c.obj)
                0x0000000000006d48                z_data_copy
 .text.elapsed  0x0000000000006d7c       0x14 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.remove_timeout
                0x0000000000006d90       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.next_timeout
                0x0000000000006dc8       0x40 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_add_timeout
                0x0000000000006e08       0xf4 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000006e08                z_add_timeout
 .text.sys_clock_announce
                0x0000000000006efc       0xcc zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000006efc                sys_clock_announce
 .text.sys_clock_tick_get
                0x0000000000006fc8       0x30 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000006fc8                sys_clock_tick_get
 .text.k_event_post_internal
                0x0000000000006ff8       0x5c zephyr/kernel/libkernel.a(events.c.obj)
 .text.k_sys_work_q_init
                0x0000000000007054       0x34 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .text.nrf_cc3xx_platform_init_no_rng
                0x0000000000007088       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                0x0000000000007088                nrf_cc3xx_platform_init_no_rng
 .text.nrf_cc3xx_platform_abort
                0x00000000000070c0       0x24 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .text.CC_PalAbort
                0x00000000000070e4       0x44 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x00000000000070e4                CC_PalAbort
 .text.nrf_cc3xx_platform_set_abort
                0x0000000000007128       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x0000000000007128                nrf_cc3xx_platform_set_abort
 .text.mutex_free
                0x0000000000007138       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_lock
                0x000000000000716c       0x48 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_unlock
                0x00000000000071b4       0x38 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_init
                0x00000000000071ec       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.nrf_cc3xx_platform_set_mutexes
                0x000000000000720c       0x74 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x000000000000720c                nrf_cc3xx_platform_set_mutexes
 .text.CC_LibInitNoRng
                0x0000000000007280       0x28 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
                0x0000000000007280                CC_LibInitNoRng
 .text.CC_HalInit
                0x00000000000072a8        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
                0x00000000000072a8                CC_HalInit
 .text.CC_PalInit
                0x00000000000072ac       0x5c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x00000000000072ac                CC_PalInit
 .text.CC_PalTerminate
                0x0000000000007308       0x34 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000000007308                CC_PalTerminate
 .text.CC_PalDmaInit
                0x000000000000733c        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                0x000000000000733c                CC_PalDmaInit
 .text.CC_PalDmaTerminate
                0x0000000000007340        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                0x0000000000007340                CC_PalDmaTerminate
 .text.CC_PalMutexCreate
                0x0000000000007344       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                0x0000000000007344                CC_PalMutexCreate
 .text.CC_PalMutexDestroy
                0x0000000000007358       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                0x0000000000007358                CC_PalMutexDestroy
 .text.CC_PalPowerSaveModeInit
                0x000000000000736c       0x3c C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
                0x000000000000736c                CC_PalPowerSaveModeInit
 .text.malloc   0x00000000000073a8       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
                0x00000000000073a8                malloc
 .text._free_r  0x00000000000073b8       0x94 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
                0x00000000000073b8                _free_r
 .text._malloc_r
                0x000000000000744c       0xb4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
                0x000000000000744c                _malloc_r
 .text._printf_float
                0x0000000000007500      0x468 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
                0x0000000000007500                _printf_float
 .text._printf_i
                0x0000000000007968      0x25c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
                0x0000000000007968                _printf_i
 .text._sbrk_r  0x0000000000007bc4       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
                0x0000000000007bc4                _sbrk_r
 .text._localeconv_r
                0x0000000000007be4        0x8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
                0x0000000000007be4                _localeconv_r
 .text.__malloc_lock
                0x0000000000007bec        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
                0x0000000000007bec                __malloc_lock
 .text.__malloc_unlock
                0x0000000000007bf8        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
                0x0000000000007bf8                __malloc_unlock
 .text._Balloc  0x0000000000007c04       0x80 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007c04                _Balloc
 .text._Bfree   0x0000000000007c84       0x44 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007c84                _Bfree
 .text.__multadd
                0x0000000000007cc8       0x90 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007cc8                __multadd
 .text.__i2b    0x0000000000007d58       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007d58                __i2b
 .text.__multiply
                0x0000000000007d84      0x154 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007d84                __multiply
 .text.__pow5mult
                0x0000000000007ed8       0xb4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007ed8                __pow5mult
 .text.__lshift
                0x0000000000007f8c       0xd8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000007f8c                __lshift
 .text.__mdiff  0x0000000000008064      0x130 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000008064                __mdiff
 .text.__d2b    0x0000000000008194       0xb0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x0000000000008194                __d2b
 .text.__assert_func
                0x0000000000008244       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
                0x0000000000008244                __assert_func
 .text.fprintf  0x0000000000008280       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
                0x0000000000008280                fiprintf
                0x0000000000008280                fprintf
 .text._vfprintf_r
                0x00000000000082a4      0x258 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
                0x00000000000082a4                _vfprintf_r
                0x00000000000082a4                _vfiprintf_r
 .text.__swbuf_r
                0x00000000000084fc       0xa4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
                0x00000000000084fc                __swbuf_r
 .text.__swsetup_r
                0x00000000000085a0       0xd8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
                0x00000000000085a0                __swsetup_r
 .text._fflush_r
                0x0000000000008678       0x78 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
                0x0000000000008678                _fflush_r
 .text.std      0x00000000000086f0       0x48 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .text._cleanup_r
                0x0000000000008738        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x0000000000008738                _cleanup_r
 .text.__sfp_lock_acquire
                0x0000000000008744        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x0000000000008744                __sfp_lock_acquire
 .text.__sfp_lock_release
                0x0000000000008750        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x0000000000008750                __sfp_lock_release
 .text.__sinit_lock_acquire
                0x000000000000875c        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x000000000000875c                __sinit_lock_acquire
 .text.__sinit_lock_release
                0x0000000000008768        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x0000000000008768                __sinit_lock_release
 .text.__sinit  0x0000000000008774       0x70 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x0000000000008774                __sinit
 .text.__sfp    0x00000000000087e4       0x8c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x00000000000087e4                __sfp
 .text.__smakebuf_r
                0x0000000000008870       0x80 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
                0x0000000000008870                __smakebuf_r
 .text._write_r
                0x00000000000088f0       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
                0x00000000000088f0                _write_r
 .text._close_r
                0x0000000000008914       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
                0x0000000000008914                _close_r
 .text._fstat_r
                0x0000000000008934       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
                0x0000000000008934                _fstat_r
 .text._isatty_r
                0x0000000000008958       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
                0x0000000000008958                _isatty_r
 .text._lseek_r
                0x0000000000008978       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
                0x0000000000008978                _lseek_r
 .text._read_r  0x000000000000899c       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
                0x000000000000899c                _read_r
 .text._OffsetAbsSyms
                0x00000000000089c0        0x2 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                0x00000000000089c0                _OffsetAbsSyms
 .text.i2c_transfer
                0x00000000000089c2       0x1e app/libapp.a(zephyr_i2c_driver.c.obj)
 .text.calculate_crc8
                0x00000000000089e0       0x30 app/libapp.a(zephyr_i2c_driver.c.obj)
                0x00000000000089e0                calculate_crc8
 .text.m117_init
                0x0000000000008a10       0xac app/libapp.a(m117_sensor.c.obj)
                0x0000000000008a10                m117_init
 .text.m117_start_conversion
                0x0000000000008abc       0x1e app/libapp.a(m117_sensor.c.obj)
                0x0000000000008abc                m117_start_conversion
 .text.m117_measure_temperature
                0x0000000000008ada       0x18 app/libapp.a(m117_sensor.c.obj)
                0x0000000000008ada                m117_measure_temperature
 .text.arch_printk_char_out
                0x0000000000008af2        0x4 zephyr/libzephyr.a(printk.c.obj)
                0x0000000000008af2                arch_printk_char_out
 .text.printk   0x0000000000008af6       0x1a zephyr/libzephyr.a(printk.c.obj)
                0x0000000000008af6                printk
 .text.z_thread_entry
                0x0000000000008b10       0x14 zephyr/libzephyr.a(thread_entry.c.obj)
                0x0000000000008b10                z_thread_entry
 .text._ldiv5   0x0000000000008b24       0x30 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text._get_digit
                0x0000000000008b54       0x2e zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.outs     0x0000000000008b82       0x2e zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.sys_poweroff
                0x0000000000008bb0       0x14 zephyr/libzephyr.a(poweroff.c.obj)
                0x0000000000008bb0                sys_poweroff
 .text.process_recheck
                0x0000000000008bc4       0x38 zephyr/libzephyr.a(onoff.c.obj)
 .text.validate_args
                0x0000000000008bfc       0x20 zephyr/libzephyr.a(onoff.c.obj)
 .text.notify_one
                0x0000000000008c1c       0x2c zephyr/libzephyr.a(onoff.c.obj)
 .text.transition_complete
                0x0000000000008c48       0x1c zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_manager_init
                0x0000000000008c64       0x26 zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000008c64                onoff_manager_init
 .text.onoff_request
                0x0000000000008c8a       0xae zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000008c8a                onoff_request
 .text.sys_notify_validate
                0x0000000000008d38       0x22 zephyr/libzephyr.a(notify.c.obj)
                0x0000000000008d38                sys_notify_validate
 .text.sys_notify_finalize
                0x0000000000008d5a       0x1a zephyr/libzephyr.a(notify.c.obj)
                0x0000000000008d5a                sys_notify_finalize
 .text._ConfigAbsSyms
                0x0000000000008d74        0x2 zephyr/libzephyr.a(configs.c.obj)
                0x0000000000008d74                _ConfigAbsSyms
 .text.pm_device_state_get
                0x0000000000008d76       0x12 zephyr/libzephyr.a(device.c.obj)
                0x0000000000008d76                pm_device_state_get
 .text.pm_device_is_busy
                0x0000000000008d88        0xe zephyr/libzephyr.a(device.c.obj)
                0x0000000000008d88                pm_device_is_busy
 .text.pm_device_is_powered
                0x0000000000008d96       0x18 zephyr/libzephyr.a(device.c.obj)
                0x0000000000008d96                pm_device_is_powered
 .text.pm_device_driver_init
                0x0000000000008dae       0x44 zephyr/libzephyr.a(device.c.obj)
                0x0000000000008dae                pm_device_driver_init
 .text.put_sync_locked
                0x0000000000008df2       0x3e zephyr/libzephyr.a(device_runtime.c.obj)
 .text.atomic_test_bit
                0x0000000000008e30        0xc zephyr/libzephyr.a(device_runtime.c.obj)
 .text.k_sem_take
                0x0000000000008e3c        0x4 zephyr/libzephyr.a(device_runtime.c.obj)
 .text.k_sem_give
                0x0000000000008e40        0x4 zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_put
                0x0000000000008e44       0x5a zephyr/libzephyr.a(device_runtime.c.obj)
                0x0000000000008e44                pm_device_runtime_put
 .text.runtime_suspend_work
                0x0000000000008e9e       0x6c zephyr/libzephyr.a(device_runtime.c.obj)
 .text.pm_device_runtime_auto_enable
                0x0000000000008f0a       0x1e zephyr/libzephyr.a(device_runtime.c.obj)
                0x0000000000008f0a                pm_device_runtime_auto_enable
 .text.abort_function
                0x0000000000008f28        0x2 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .text.z_arm_fatal_error
                0x0000000000008f2a        0xc zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                0x0000000000008f2a                z_arm_fatal_error
 .text.z_do_kernel_oops
                0x0000000000008f36        0x8 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                0x0000000000008f36                z_do_kernel_oops
 .text.z_arm_nmi
                0x0000000000008f3e        0xe zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                0x0000000000008f3e                z_arm_nmi
 .text.z_irq_spurious
                0x0000000000008f4c        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x0000000000008f4c                z_irq_spurious
 .text.configure_builtin_stack_guard
                0x0000000000008f54        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000008f54                configure_builtin_stack_guard
 .text.arch_irq_unlock_outlined
                0x0000000000008f5c        0xe zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000008f5c                arch_irq_unlock_outlined
 .text.arch_cpu_idle
                0x0000000000008f6a       0x2a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000008f6a                arch_cpu_idle
 .text.arch_cpu_atomic_idle
                0x0000000000008f94       0x2e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000008f94                arch_cpu_atomic_idle
 .text.arm_cmse_mpu_region_get
                0x0000000000008fc2       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                0x0000000000008fc2                arm_cmse_mpu_region_get
 .text.mpu_configure_region
                0x0000000000008fd4       0x32 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text._stdin_hook_default
                0x0000000000009006        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._stdout_hook_default
                0x000000000000900a        0x6 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text.malloc_prepare
                0x0000000000009010        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .text._read    0x0000000000009014        0x8 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000009014                _read
                0x0000000000009014                read
 .text._write   0x000000000000901c        0x8 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x000000000000901c                _write
                0x000000000000901c                write
 .text._close   0x0000000000009024        0x6 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000009024                _close
                0x0000000000009024                close
 .text._lseek   0x000000000000902a        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x000000000000902a                _lseek
                0x000000000000902a                lseek
 .text._isatty  0x000000000000902e        0xa zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x000000000000902e                _isatty
                0x000000000000902e                isatty
 .text._fstat   0x0000000000009038        0xa zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000009038                fstat
                0x0000000000009038                _fstat
 .text.__retarget_lock_init_recursive
                0x0000000000009042       0x14 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000009042                __retarget_lock_init_recursive
 .text.__retarget_lock_acquire_recursive
                0x0000000000009056        0xc zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000009056                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x0000000000009062        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000000009062                __retarget_lock_release_recursive
 .text.onoff_stop
                0x0000000000009066       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.get_status
                0x0000000000009076       0x12 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.stop     0x0000000000009088       0x52 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_stop
                0x00000000000090da        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.async_start
                0x00000000000090e0       0x5c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_start
                0x000000000000913c        0xe zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.onoff_started_callback
                0x000000000000914a       0x14 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclkaudio_start
                0x000000000000915e        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclk192m_start
                0x0000000000009164        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.lfclk_start
                0x000000000000916a        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclkaudio_stop
                0x0000000000009170        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclk192m_stop
                0x0000000000009176        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.lfclk_stop
                0x000000000000917c        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.blocking_start_callback
                0x0000000000009182        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.gpio_nrfx_port_get_raw
                0x0000000000009188        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_set_masked_raw
                0x0000000000009194       0x14 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_set_bits_raw
                0x00000000000091a8        0xa zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_clear_bits_raw
                0x00000000000091b2        0xa zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_toggle_bits
                0x00000000000091bc       0x14 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_manage_callback
                0x00000000000091d0       0x52 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.irq_connect1
                0x0000000000009222        0xa zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.i2c_nrfx_twim_init
                0x000000000000922c       0x26 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .text.pinctrl_apply_state.isra.0
                0x0000000000009252       0x1e zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .text.twim_nrfx_pm_action
                0x0000000000009270       0x32 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x0000000000009270                twim_nrfx_pm_action
 .text.i2c_nrfx_twim_configure
                0x00000000000092a2       0x3a zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                0x00000000000092a2                i2c_nrfx_twim_configure
 .text.pinctrl_lookup_state
                0x00000000000092dc       0x26 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                0x00000000000092dc                pinctrl_lookup_state
 .text.event_clear
                0x0000000000009302       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.hw_cc3xx_init_internal
                0x000000000000931a        0x4 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text.hw_cc3xx_init
                0x000000000000931e       0x12 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text.nrfx_isr
                0x0000000000009330        0x2 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                0x0000000000009330                nrfx_isr
 .text.nrfx_busy_wait
                0x0000000000009332        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                0x0000000000009332                nrfx_busy_wait
 .text.nrfx_gppi_event_endpoint_setup
                0x0000000000009336        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000009336                nrfx_gppi_event_endpoint_setup
 .text.nrfx_gppi_task_endpoint_setup
                0x0000000000009340        0xa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000009340                nrfx_gppi_task_endpoint_setup
 .text.clock_stop
                0x000000000000934a      0x162 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_stop
                0x00000000000094ac        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x00000000000094ac                nrfx_clock_stop
 .text.nrf_gpio_reconfigure
                0x00000000000094b0       0x92 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrf_gpio_cfg_sense_set
                0x0000000000009542       0x1e modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_trigger_disable
                0x0000000000009560       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_pin_uninit
                0x00000000000095a8        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000095a8                nrfx_gpiote_pin_uninit
 .text.nrfx_gpiote_trigger_disable
                0x00000000000095ac        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000095ac                nrfx_gpiote_trigger_disable
 .text.xfer_completeness_check
                0x00000000000095b0       0x62 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.pins_configure
                0x0000000000009612       0x54 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.__nrfy_internal_twim_event_handle.isra.0
                0x0000000000009666       0x22 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.__nrfy_internal_twim_events_process.constprop.0
                0x0000000000009688       0x54 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .text.nrf_gpio_pin_set
                0x00000000000096dc       0x18 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .text.z_impl_k_busy_wait
                0x00000000000096f4        0x8 zephyr/kernel/libkernel.a(busy_wait.c.obj)
                0x00000000000096f4                z_impl_k_busy_wait
 .text.z_device_state_init
                0x00000000000096fc        0x2 zephyr/kernel/libkernel.a(device.c.obj)
                0x00000000000096fc                z_device_state_init
 .text.z_impl_device_is_ready
                0x00000000000096fe       0x16 zephyr/kernel/libkernel.a(device.c.obj)
                0x00000000000096fe                z_impl_device_is_ready
 .text.arch_system_halt
                0x0000000000009714       0x10 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x0000000000009714                arch_system_halt
 .text.k_sys_fatal_error_handler
                0x0000000000009724        0x6 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x0000000000009724                k_sys_fatal_error_handler
 .text.do_device_init
                0x000000000000972a       0x48 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_early_memset
                0x0000000000009772        0x4 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000009772                z_early_memset
 .text.z_early_memcpy
                0x0000000000009776        0x4 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000009776                z_early_memcpy
 .text.z_init_static
                0x000000000000977a        0x2 zephyr/kernel/libkernel.a(init_static.c.obj)
                0x000000000000977a                z_init_static
 .text.create_free_list
                0x000000000000977c       0x34 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_init
                0x00000000000097b0       0x1c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x00000000000097b0                k_mem_slab_init
 .text.k_mem_slab_free
                0x00000000000097cc       0x4c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x00000000000097cc                k_mem_slab_free
 .text.idle     0x0000000000009818       0x16 zephyr/kernel/libkernel.a(idle.c.obj)
                0x0000000000009818                idle
 .text.adjust_owner_prio.isra.0
                0x000000000000982e       0x10 zephyr/kernel/libkernel.a(mutex.c.obj)
 .text.z_impl_k_mutex_init
                0x000000000000983e        0xe zephyr/kernel/libkernel.a(mutex.c.obj)
                0x000000000000983e                z_impl_k_mutex_init
 .text.z_impl_k_sem_init
                0x000000000000984c       0x18 zephyr/kernel/libkernel.a(sem.c.obj)
                0x000000000000984c                z_impl_k_sem_init
 .text.flag_test_and_clear
                0x0000000000009864       0x16 zephyr/kernel/libkernel.a(work.c.obj)
 .text.unschedule_locked
                0x000000000000987a       0x1e zephyr/kernel/libkernel.a(work.c.obj)
 .text.notify_queue_locked.isra.0
                0x0000000000009898        0xe zephyr/kernel/libkernel.a(work.c.obj)
 .text.cancel_async_locked
                0x00000000000098a6       0x66 zephyr/kernel/libkernel.a(work.c.obj)
 .text.work_timeout
                0x000000000000990c       0x3c zephyr/kernel/libkernel.a(work.c.obj)
 .text.k_work_init_delayable
                0x0000000000009948       0x18 zephyr/kernel/libkernel.a(work.c.obj)
                0x0000000000009948                k_work_init_delayable
 .text.k_work_cancel_delayable
                0x0000000000009960       0x26 zephyr/kernel/libkernel.a(work.c.obj)
                0x0000000000009960                k_work_cancel_delayable
 .text.k_is_in_isr
                0x0000000000009986        0xc zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000009986                k_is_in_isr
 .text.z_impl_k_thread_name_set
                0x0000000000009992        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000009992                z_impl_k_thread_name_set
 .text.z_impl_k_thread_start
                0x0000000000009998        0x4 zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000009998                z_impl_k_thread_start
 .text.sys_dlist_remove
                0x000000000000999c       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.unpend_thread_no_timeout
                0x00000000000099ac       0x14 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.add_to_waitq_locked
                0x00000000000099c0       0x50 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_ready_thread
                0x0000000000009a10       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009a10                z_ready_thread
 .text.z_impl_k_thread_suspend
                0x0000000000009a2e       0x34 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009a2e                z_impl_k_thread_suspend
 .text.z_sched_wake_thread
                0x0000000000009a62       0x4e zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009a62                z_sched_wake_thread
 .text.z_thread_timeout
                0x0000000000009ab0        0x8 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009ab0                z_thread_timeout
 .text.z_unpend_first_thread
                0x0000000000009ab8       0x36 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009ab8                z_unpend_first_thread
 .text.z_thread_abort
                0x0000000000009aee       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009aee                z_thread_abort
 .text.z_sched_wake
                0x0000000000009b32       0x46 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009b32                z_sched_wake
 .text.z_sched_waitq_walk
                0x0000000000009b78       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000009b78                z_sched_waitq_walk
 .text.z_abort_timeout
                0x0000000000009bbc       0x2a zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000009bbc                z_abort_timeout
 .text.sys_clock_tick_get_32
                0x0000000000009be6        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000009be6                sys_clock_tick_get_32
 .text.k_event_wait_internal
                0x0000000000009bee       0x82 zephyr/kernel/libkernel.a(events.c.obj)
 .text.event_walk_op
                0x0000000000009c70       0x30 zephyr/kernel/libkernel.a(events.c.obj)
 .text.z_impl_k_event_set
                0x0000000000009ca0        0x8 zephyr/kernel/libkernel.a(events.c.obj)
                0x0000000000009ca0                z_impl_k_event_set
 .text.z_impl_k_event_clear
                0x0000000000009ca8        0x8 zephyr/kernel/libkernel.a(events.c.obj)
                0x0000000000009ca8                z_impl_k_event_clear
 .text.z_impl_k_event_wait
                0x0000000000009cb0        0x6 zephyr/kernel/libkernel.a(events.c.obj)
                0x0000000000009cb0                z_impl_k_event_wait
 .text.memcpy   0x0000000000009cb6       0x1a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
                0x0000000000009cb6                memcpy
 .text.memset   0x0000000000009cd0       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
                0x0000000000009cd0                memset
 .text.__cvt    0x0000000000009ce0       0xd0 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
                0x0000000000009ce0                __cvt
 .text.__exponent
                0x0000000000009db0       0x72 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
                0x0000000000009db0                __exponent
 .text._printf_common
                0x0000000000009e22       0xe4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
                0x0000000000009e22                _printf_common
 .text.strnlen  0x0000000000009f06       0x18 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
                0x0000000000009f06                strnlen
 .text.quorem   0x0000000000009f1e      0x11a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
 .text.memchr   0x000000000000a038       0x1c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
                0x000000000000a038                memchr
 .text.__hi0bits
                0x000000000000a054       0x44 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x000000000000a054                __hi0bits
 .text.__lo0bits
                0x000000000000a098       0x5a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x000000000000a098                __lo0bits
 .text.__mcmp   0x000000000000a0f2       0x36 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x000000000000a0f2                __mcmp
 .text._calloc_r
                0x000000000000a128       0x1c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
                0x000000000000a128                _calloc_r
 .text.__ascii_mbtowc
                0x000000000000a144       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
                0x000000000000a144                __ascii_mbtowc
 .text.__sfputc_r
                0x000000000000a168       0x2e C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .text.__sfputs_r
                0x000000000000a196       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
                0x000000000000a196                __sfputs_r
 .text.__ascii_wctomb
                0x000000000000a1ba       0x1a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
                0x000000000000a1ba                __ascii_wctomb
 .text.__sflush_r
                0x000000000000a1d4      0x106 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
                0x000000000000a1d4                __sflush_r
 .text.__sfmoreglue
                0x000000000000a2da       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x000000000000a2da                __sfmoreglue
 .text._fwalk_reent
                0x000000000000a306       0x3e C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
                0x000000000000a306                _fwalk_reent
 .text.__swhatbuf_r
                0x000000000000a344       0x4a C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
                0x000000000000a344                __swhatbuf_r
 .text.__sread  0x000000000000a38e       0x22 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
                0x000000000000a38e                __sread
 .text.__swrite
                0x000000000000a3b0       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
                0x000000000000a3b0                __swrite
 .text.__sseek  0x000000000000a3e8       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
                0x000000000000a3e8                __sseek
 .text.__sclose
                0x000000000000a40c        0x8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
                0x000000000000a40c                __sclose
 *(SORT_BY_ALIGNMENT(.TEXT.*))
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.t.*))
 *(SORT_BY_ALIGNMENT(.glue_7t))
 .glue_7t       0x000000000000a414        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.glue_7))
 .glue_7        0x000000000000a414        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.vfp11_veneer))
 .vfp11_veneer  0x000000000000a414        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.v4_bx))
 .v4_bx         0x000000000000a414        0x0 linker stubs
                0x000000000000a414                . = ALIGN (0x4)
                0x000000000000a414                __text_region_end = .

.ARM.exidx      0x000000000000a414        0x8
                0x000000000000a414                __exidx_start = .
 *(SORT_BY_ALIGNMENT(.ARM.exidx*) SORT_BY_ALIGNMENT(gnu.linkonce.armexidx.*))
 .ARM.exidx     0x000000000000a414        0x8 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
                0x000000000000a41c                __exidx_end = .
                0x000000000000a41c                __rodata_region_start = .

initlevel       0x000000000000a41c       0x68
                0x000000000000a41c                __init_start = .
                0x000000000000a41c                __init_EARLY_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_EARLY?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_EARLY??_*)))
                0x000000000000a41c                __init_PRE_KERNEL_1_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_1?_*)))
 .z_init_PRE_KERNEL_10_0_
                0x000000000000a41c        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .z_init_PRE_KERNEL_10_0_
                0x000000000000a424        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_1??_*)))
 .z_init_PRE_KERNEL_130_00090_
                0x000000000000a42c        0x8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .z_init_PRE_KERNEL_130_0_
                0x000000000000a434        0x8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .z_init_PRE_KERNEL_140_00010_
                0x000000000000a43c        0x8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_init_PRE_KERNEL_140_00014_
                0x000000000000a444        0x8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_init_PRE_KERNEL_140_0_
                0x000000000000a44c        0x8 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                0x000000000000a454                __init_PRE_KERNEL_2_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_2?_*)))
 .z_init_PRE_KERNEL_20_0_
                0x000000000000a454        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_2??_*)))
                0x000000000000a45c                __init_POST_KERNEL_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_POST_KERNEL?_*)))
 .z_init_POST_KERNEL0_0_
                0x000000000000a45c        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_POST_KERNEL??_*)))
 .z_init_POST_KERNEL35_0_
                0x000000000000a464        0x8 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .z_init_POST_KERNEL40_0_
                0x000000000000a46c        0x8 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .z_init_POST_KERNEL40_0_
                0x000000000000a474        0x8 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .z_init_POST_KERNEL50_00102_
                0x000000000000a47c        0x8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                0x000000000000a484                __init_APPLICATION_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_APPLICATION?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_APPLICATION??_*)))
                0x000000000000a484                __init_SMP_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_SMP?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_SMP??_*)))
                0x000000000000a484                __init_end = .
                0x000000000000a484                __deferred_init_list_start = .
 *(SORT_BY_ALIGNMENT(.z_deferred_init*))
                0x000000000000a484                __deferred_init_list_end = .

device_area     0x000000000000a484       0x60
                0x000000000000a484                _device_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._device.static.*_?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._device.static.*_??_*)))
 ._device.static.1_30_
                0x000000000000a484       0x18 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x000000000000a484                __device_dts_ord_90
 ._device.static.1_40_
                0x000000000000a49c       0x30 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                0x000000000000a49c                __device_dts_ord_10
                0x000000000000a4b4                __device_dts_ord_14
 ._device.static.3_50_
                0x000000000000a4cc       0x18 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                0x000000000000a4cc                __device_dts_ord_102
                0x000000000000a4e4                _device_list_end = .

sw_isr_table    0x000000000000a4e4      0x228
                0x000000000000a4e4                . = ALIGN (0x4)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.sw_isr_table*))
 .gnu.linkonce.sw_isr_table
                0x000000000000a4e4      0x228 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                0x000000000000a4e4                _sw_isr_table

initlevel_error
                0x000000000000a41c        0x0
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_[_A-Z0-9]*)))
                0x0000000000000001                ASSERT ((SIZEOF (initlevel_error) == 0x0), Undefined initialization levels used.)

app_shmem_regions
                0x000000000000a70c        0x0
                0x000000000000a70c                __app_shmem_regions_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.app_regions.*)))
                0x000000000000a70c                __app_shmem_regions_end = .

k_p4wq_initparam_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _k_p4wq_initparam_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_p4wq_initparam.static.*)))
                0x000000000000a70c                _k_p4wq_initparam_list_end = .

_static_thread_data_area
                0x000000000000a70c        0x0
                0x000000000000a70c                __static_thread_data_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.__static_thread_data.static.*)))
                0x000000000000a70c                __static_thread_data_list_end = .

device_deps     0x000000000000a70c        0x0
                0x000000000000a70c                __device_deps_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.__device_deps_pass2*)))
                0x000000000000a70c                __device_deps_end = .

ztest           0x000000000000a70c        0x0
                0x000000000000a70c                _ztest_expected_result_entry_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_expected_result_entry.static.*)))
                0x000000000000a70c                _ztest_expected_result_entry_list_end = .
                0x000000000000a70c                _ztest_suite_node_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_suite_node.static.*)))
                0x000000000000a70c                _ztest_suite_node_list_end = .
                0x000000000000a70c                _ztest_unit_test_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_unit_test.static.*)))
                0x000000000000a70c                _ztest_unit_test_list_end = .
                0x000000000000a70c                _ztest_test_rule_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_test_rule.static.*)))
                0x000000000000a70c                _ztest_test_rule_list_end = .

init_array      0x000000000000a70c        0x0
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.ctors*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.init_array*)))
                0x0000000000000001                ASSERT ((SIZEOF (init_array) == 0x0), GNU-style constructors required but STATIC_INIT_GNU not enabled)

bt_l2cap_fixed_chan_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _bt_l2cap_fixed_chan_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_l2cap_fixed_chan.static.*)))
                0x000000000000a70c                _bt_l2cap_fixed_chan_list_end = .

bt_gatt_service_static_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _bt_gatt_service_static_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_gatt_service_static.static.*)))
                0x000000000000a70c                _bt_gatt_service_static_list_end = .

log_strings_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _log_strings_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_strings.static.*)))
                0x000000000000a70c                _log_strings_list_end = .

log_const_area  0x000000000000a70c        0x0
                0x000000000000a70c                _log_const_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_const.static.*)))
                0x000000000000a70c                _log_const_list_end = .

log_backend_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _log_backend_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_backend.static.*)))
                0x000000000000a70c                _log_backend_list_end = .

log_link_area   0x000000000000a70c        0x0
                0x000000000000a70c                _log_link_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_link.static.*)))
                0x000000000000a70c                _log_link_list_end = .

tracing_backend_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _tracing_backend_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._tracing_backend.static.*)))
                0x000000000000a70c                _tracing_backend_list_end = .

zephyr_dbg_info
 *(SORT_BY_ALIGNMENT(.dbg_thread_info))

intc_table_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _intc_table_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._intc_table.static.*)))
                0x000000000000a70c                _intc_table_list_end = .

symbol_to_keep  0x000000000000a70c        0x0
                0x000000000000a70c                __symbol_to_keep_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.symbol_to_keep*)))
                0x000000000000a70c                __symbol_to_keep_end = .

shell_area      0x000000000000a70c        0x0
                0x000000000000a70c                _shell_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell.static.*)))
                0x000000000000a70c                _shell_list_end = .

shell_root_cmds_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _shell_root_cmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_root_cmds.static.*)))
                0x000000000000a70c                _shell_root_cmds_list_end = .

shell_subcmds_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _shell_subcmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_subcmds.static.*)))
                0x000000000000a70c                _shell_subcmds_list_end = .

shell_dynamic_subcmds_area
                0x000000000000a70c        0x0
                0x000000000000a70c                _shell_dynamic_subcmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_dynamic_subcmds.static.*)))
                0x000000000000a70c                _shell_dynamic_subcmds_list_end = .

cfb_font_area   0x000000000000a70c        0x0
                0x000000000000a70c                _cfb_font_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._cfb_font.static.*)))
                0x000000000000a70c                _cfb_font_list_end = .

rodata          0x000000000000a710      0x790
 *(SORT_BY_ALIGNMENT(.rodata))
 .rodata        0x000000000000a710        0x8 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 *(SORT_BY_ALIGNMENT(.rodata.*))
 *fill*         0x000000000000a718        0x8 
 .rodata.delay_machine_code.1
                0x000000000000a720        0x6 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 *fill*         0x000000000000a726        0x2 
 .rodata.__mprec_bigtens
                0x000000000000a728       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x000000000000a728                __mprec_bigtens
 .rodata.__mprec_tens
                0x000000000000a750       0xc8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                0x000000000000a750                __mprec_tens
 .rodata.mem_attr_region
                0x000000000000a818        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .rodata.apis   0x000000000000a818        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .rodata.mutexes
                0x000000000000a820       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.mutex_apis
                0x000000000000a834       0x10 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.static_regions
                0x000000000000a844        0xc zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .rodata.mpu_config
                0x000000000000a850        0x8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                0x000000000000a850                mpu_config
 .rodata.mpu_regions
                0x000000000000a858       0x20 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .rodata.transitions.0
                0x000000000000a878        0xc zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .rodata.transitions.0
                0x000000000000a884        0xc zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.config
                0x000000000000a890       0x20 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.clock_control_api
                0x000000000000a8b0       0x1c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.gpio_nrfx_p1_cfg
                0x000000000000a8cc       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.gpio_nrfx_p0_cfg
                0x000000000000a8e4       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.gpio_nrfx_drv_api_funcs
                0x000000000000a8fc       0x24 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.twim_1z_config
                0x000000000000a920       0x30 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_dev_config__device_dts_ord_102
                0x000000000000a950        0xc zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_states__device_dts_ord_102
                0x000000000000a95c       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_state_pins_1__device_dts_ord_102
                0x000000000000a96c        0x8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.__pinctrl_state_pins_0__device_dts_ord_102
                0x000000000000a974        0x8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.i2c_nrfx_twim_driver_api
                0x000000000000a97c       0x18 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.levels.0
                0x000000000000a994       0x18 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.CSWTCH.10
                0x000000000000a9ac       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .rodata.mutex_free.str1.4
                0x000000000000a9cc       0x26 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x000000000000a9f2        0x2 
 .rodata.mutex_init.str1.4
                0x000000000000a9f4       0x23 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x000000000000aa17        0x1 
 .rodata.CC_PalPowerSaveModeInit.str1.4
                0x000000000000aa18       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .rodata._global_impure_ptr
                0x000000000000aa38        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
                0x000000000000aa38                _global_impure_ptr
 .rodata.p05.0  0x000000000000aa3c        0xc C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .rodata.__sf_fake_stderr
                0x000000000000aa48       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x000000000000aa48                __sf_fake_stderr
 .rodata.__sf_fake_stdout
                0x000000000000aa68       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x000000000000aa68                __sf_fake_stdout
 .rodata.__sf_fake_stdin
                0x000000000000aa88       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
                0x000000000000aa88                __sf_fake_stdin
 .rodata.z_cbvprintf_impl.str1.1
                0x000000000000aaa8        0x6 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .rodata.action_expected_state
                0x000000000000aaae        0x4 zephyr/libzephyr.a(device.c.obj)
 .rodata.action_target_state
                0x000000000000aab2        0x4 zephyr/libzephyr.a(device.c.obj)
 .rodata.boot_banner.str1.1
                0x000000000000aab6       0x63 zephyr/libzephyr.a(banner.c.obj)
 .rodata.mutex_free_platform.str1.1
                0x000000000000ab19       0x26 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.mutex_init_platform.str1.1
                0x000000000000ab3f       0x2d zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.str1.1
                0x000000000000ab6c        0xf zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .rodata.abort.str1.1
                0x000000000000ab7b        0x9 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .rodata.forwarded_psels.0
                0x000000000000ab84        0xc zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .rodata.CSWTCH.4
                0x000000000000ab90        0x4 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .rodata.str1.1
                0x000000000000ab94        0xb zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.str1.1
                0x000000000000ab9f       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.str1.1
                0x000000000000abb7        0x9 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .rodata.drive_modes
                0x000000000000abc0        0x9 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .rodata.CSWTCH.19
                0x000000000000abc9        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.7
                0x000000000000abcd        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.5
                0x000000000000abd1        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.port_offset.1
                0x000000000000abd5       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.ports  0x000000000000abe5        0x2 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.z_cstart.str1.1
                0x000000000000abe7        0x5 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.str1.1
                0x000000000000abec        0x9 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .rodata._ctype_
                0x000000000000abf5      0x101 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-ctype_.o)
                0x000000000000abf5                _ctype_
 .rodata._printf_float.str1.1
                0x000000000000acf6       0x10 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
                                         0x12 (size before relaxing)
 .rodata._printf_i.str1.1
                0x000000000000ad06       0x22 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
 .rodata._dtoa_r.str1.1
                0x000000000000ad28       0xa9 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
                                         0xab (size before relaxing)
 .rodata._Balloc.str1.1
                0x000000000000add1       0x75 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
                                         0x8c (size before relaxing)
 .rodata.__multadd.str1.1
                0x000000000000ae46       0x11 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .rodata.__assert_func.str1.1
                0x000000000000ae46       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
                                         0x3d (size before relaxing)
 .rodata._setlocale_r.str1.1
                0x000000000000ae82        0x8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
                                          0x9 (size before relaxing)
 .rodata.str1.1
                0x000000000000ae8a        0x2 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .rodata._vfprintf_r.str1.1
                0x000000000000ae8c       0x11 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.r.*))
                0x000000000000aea0                . = ALIGN (0x4)
 *fill*         0x000000000000ae9d        0x3 
                0x000000000000aea0                __rodata_region_end = .
                0x000000000000aea0                . = ALIGN (_region_min_align)
                0x000000000000aea0                __rom_region_end = ((__rom_region_start + .) - ADDR (rom_start))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.got.plt))
 *(SORT_BY_ALIGNMENT(.igot.plt))
 *(SORT_BY_ALIGNMENT(.got))
 *(SORT_BY_ALIGNMENT(.igot))
                0x0000000020000000                . = 0x20000000
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                _image_ram_start = .

.ramfunc        0x0000000020000000        0x0 load address 0x000000000000aea0
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                __ramfunc_start = .
 *(SORT_BY_ALIGNMENT(.ramfunc))
 *(SORT_BY_ALIGNMENT(.ramfunc.*))
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                __ramfunc_end = .
                0x0000000000000000                __ramfunc_size = (__ramfunc_end - __ramfunc_start)
                0x000000000000aea0                __ramfunc_load_start = LOADADDR (.ramfunc)

datas           0x0000000020000000      0x36c load address 0x000000000000aea0
                0x0000000020000000                __data_region_start = .
                0x0000000020000000                __data_start = .
 *(SORT_BY_ALIGNMENT(.data))
 *(SORT_BY_ALIGNMENT(.data.*))
 .data.__pm_device_dts_ord_102
                0x0000000020000000       0x68 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .data._char_out
                0x0000000020000068        0x4 zephyr/libzephyr.a(printk.c.obj)
 .data.power_mutex
                0x000000002000006c        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.rng_mutex
                0x0000000020000074        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.asym_mutex
                0x000000002000007c        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.sym_mutex
                0x0000000020000084        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data._stdin_hook
                0x000000002000008c        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .data._stdout_hook
                0x0000000020000090        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .data.SystemCoreClock
                0x0000000020000094        0x4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                0x0000000020000094                SystemCoreClock
 .data.dppi     0x0000000020000098        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .data.m_cb     0x00000000200000a0       0x10 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .data.m_cb     0x00000000200000b0       0x84 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .data.timeout_list
                0x0000000020000134        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
 .data.platform_abort_apis
                0x000000002000013c        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x000000002000013c                platform_abort_apis
 .data.platform_mutexes
                0x0000000020000144       0x14 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000020000144                platform_mutexes
 .data.platform_mutex_apis
                0x0000000020000158       0x10 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000020000158                platform_mutex_apis
 .data.power_mutex
                0x0000000020000168        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.rng_mutex
                0x0000000020000170        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.asym_mutex
                0x0000000020000178        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.sym_mutex
                0x0000000020000180        0x8 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.pCCRndCryptoMutex
                0x0000000020000188        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000188                pCCRndCryptoMutex
 .data.CCPowerMutex
                0x000000002000018c        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x000000002000018c                CCPowerMutex
 .data.CCRndCryptoMutex
                0x0000000020000190        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000190                CCRndCryptoMutex
 .data.CCAsymCryptoMutex
                0x0000000020000194        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000194                CCAsymCryptoMutex
 .data.CCSymCryptoMutex
                0x0000000020000198        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000198                CCSymCryptoMutex
 .data._impure_ptr
                0x000000002000019c        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
                0x000000002000019c                _impure_ptr
 .data.impure_data
                0x00000000200001a0       0x60 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
 .data.__global_locale
                0x0000000020000200      0x16c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
                0x0000000020000200                __global_locale
 *(SORT_BY_ALIGNMENT(.kernel.*))
                0x000000002000036c                __data_end = .
                0x000000000000036c                __data_size = (__data_end - __data_start)
                0x000000000000aea0                __data_load_start = LOADADDR (datas)
                0x000000000000aea0                __data_region_load_start = LOADADDR (datas)

device_states   0x000000002000036c        0x8 load address 0x000000000000b20c
                0x000000002000036c                __device_states_start = .
 *(SORT_BY_ALIGNMENT(.z_devstate))
 .z_devstate    0x000000002000036c        0x2 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .z_devstate    0x000000002000036e        0x4 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_devstate    0x0000000020000372        0x2 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 *(SORT_BY_ALIGNMENT(.z_devstate.*))
                0x0000000020000374                __device_states_end = .

pm_device_slots_area
                0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _pm_device_slots_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._pm_device_slots.static.*)))
                0x0000000020000374                _pm_device_slots_list_end = .

log_mpsc_pbuf_area
                0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _log_mpsc_pbuf_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_mpsc_pbuf.static.*)))
                0x0000000020000374                _log_mpsc_pbuf_list_end = .

log_msg_ptr_area
                0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _log_msg_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_msg_ptr.static.*)))
                0x0000000020000374                _log_msg_ptr_list_end = .

log_dynamic_area
                0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _log_dynamic_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_dynamic.static.*)))
                0x0000000020000374                _log_dynamic_list_end = .

k_timer_area    0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _k_timer_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_timer.static.*)))
                0x0000000020000374                _k_timer_list_end = .

k_mem_slab_area
                0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _k_mem_slab_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mem_slab.static.*)))
                0x0000000020000374                _k_mem_slab_list_end = .

k_heap_area     0x0000000020000374        0x0 load address 0x000000000000b214
                0x0000000020000374                _k_heap_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_heap.static.*)))
                0x0000000020000374                _k_heap_list_end = .

k_mutex_area    0x0000000020000374       0x8c load address 0x000000000000b214
                0x0000000020000374                _k_mutex_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mutex.static.*)))
 ._k_mutex.static.__lock___malloc_recursive_mutex_
                0x0000000020000374       0x14 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000020000374                __lock___malloc_recursive_mutex
 ._k_mutex.static.__lock___sfp_recursive_mutex_
                0x0000000020000388       0x14 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x0000000020000388                __lock___sfp_recursive_mutex
 ._k_mutex.static.__lock___sinit_recursive_mutex_
                0x000000002000039c       0x14 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                0x000000002000039c                __lock___sinit_recursive_mutex
 ._k_mutex.static.asym_mutex_int_
                0x00000000200003b0       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200003b0                asym_mutex_int
 ._k_mutex.static.power_mutex_int_
                0x00000000200003c4       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200003c4                power_mutex_int
 ._k_mutex.static.rng_mutex_int_
                0x00000000200003d8       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200003d8                rng_mutex_int
 ._k_mutex.static.sym_mutex_int_
                0x00000000200003ec       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200003ec                sym_mutex_int
                0x0000000020000400                _k_mutex_list_end = .

k_stack_area    0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_stack_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_stack.static.*)))
                0x0000000020000400                _k_stack_list_end = .

k_msgq_area     0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_msgq_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_msgq.static.*)))
                0x0000000020000400                _k_msgq_list_end = .

k_mbox_area     0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_mbox_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mbox.static.*)))
                0x0000000020000400                _k_mbox_list_end = .

k_pipe_area     0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_pipe_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_pipe.static.*)))
                0x0000000020000400                _k_pipe_list_end = .

k_sem_area      0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_sem_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_sem.static.*)))
                0x0000000020000400                _k_sem_list_end = .

k_event_area    0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_event_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_event.static.*)))
                0x0000000020000400                _k_event_list_end = .

k_queue_area    0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_queue_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_queue.static.*)))
                0x0000000020000400                _k_queue_list_end = .

k_fifo_area     0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_fifo_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_fifo.static.*)))
                0x0000000020000400                _k_fifo_list_end = .

k_lifo_area     0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_lifo_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_lifo.static.*)))
                0x0000000020000400                _k_lifo_list_end = .

k_condvar_area  0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _k_condvar_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_condvar.static.*)))
                0x0000000020000400                _k_condvar_list_end = .

sys_mem_blocks_ptr_area
                0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _sys_mem_blocks_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._sys_mem_blocks_ptr.static.*)))
                0x0000000020000400                _sys_mem_blocks_ptr_list_end = .

net_buf_pool_area
                0x0000000020000400        0x0 load address 0x000000000000b2a0
                0x0000000020000400                _net_buf_pool_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._net_buf_pool.static.*)))
                0x0000000020000400                _net_buf_pool_list_end = .
                0x0000000020000400                __data_region_end = .

bss             0x0000000020000400      0xc3b
                0x0000000020000400                . = ALIGN (0x4)
                0x0000000020000400                __bss_start = .
                0x0000000020000400                __kernel_ram_start = .
 *(SORT_BY_ALIGNMENT(.bss))
 *(SORT_BY_ALIGNMENT(.bss.*))
 .bss.cc_data   0x0000000020000400       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.last_count
                0x0000000020000410        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.anchor    0x0000000020000418        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.z_idle_threads
                0x0000000020000420       0xd0 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000420                z_idle_threads
 .bss.z_main_thread
                0x00000000200004f0       0xd0 zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000200004f0                z_main_thread
 .bss._thread_dummy
                0x00000000200005c0       0xd0 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000200005c0                _thread_dummy
 .bss.curr_tick
                0x0000000020000690        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss.k_sys_work_q
                0x0000000020000698       0xf0 zephyr/kernel/libkernel.a(system_work_q.c.obj)
                0x0000000020000698                k_sys_work_q
 .bss.i2c_dev   0x0000000020000788        0x4 app/libapp.a(main_temp_low_power.c.obj)
 .bss.i2c_dev   0x000000002000078c        0x4 app/libapp.a(zephyr_i2c_driver.c.obj)
 .bss.mutex_slab_buffer
                0x0000000020000790      0x500 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000790                mutex_slab_buffer
 .bss.mutex_slab
                0x0000000020000c90       0x1c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000c90                mutex_slab
 .bss.dyn_reg_info
                0x0000000020000cac       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss.heap_sz   0x0000000020000cc0        0x4 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .bss.cpunet_mgr
                0x0000000020000cc4       0x1c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .bss.cli.1     0x0000000020000ce0       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.on.2      0x0000000020000cf0        0x4 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.data      0x0000000020000cf4       0xa0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.hfclk_users
                0x0000000020000d94        0x4 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.gpio_nrfx_p1_data
                0x0000000020000d98        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss.gpio_nrfx_p0_data
                0x0000000020000da4        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss.twim_1_data
                0x0000000020000db0       0x24 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .bss.force_isr_mask
                0x0000000020000dd4        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.int_mask  0x0000000020000dd8        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.overflow_cnt
                0x0000000020000ddc        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.m_clock_cb
                0x0000000020000de0        0x8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .bss.m_cb      0x0000000020000de8       0x28 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .bss._kernel   0x0000000020000e10       0x28 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000e10                _kernel
 .bss.pending_cancels
                0x0000000020000e38        0x8 zephyr/kernel/libkernel.a(work.c.obj)
 .bss.announce_remaining
                0x0000000020000e40        0x4 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss.nrf_cc3xx_platform_initialized
                0x0000000020000e44        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss.power_mutex_int
                0x0000000020000e48        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.rng_mutex_int
                0x0000000020000e4c        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.asym_mutex_int
                0x0000000020000e50        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.sym_mutex_int
                0x0000000020000e54        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.use_count
                0x0000000020000e58        0x4 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .bss.nrf_cc3xx_platform_ctr_drbg_global_ctx
                0x0000000020000e5c      0x1c0 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                0x0000000020000e5c                nrf_cc3xx_platform_ctr_drbg_global_ctx
 .bss.__malloc_sbrk_start
                0x000000002000101c        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
                0x000000002000101c                __malloc_sbrk_start
 .bss.__malloc_free_list
                0x0000000020001020        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
                0x0000000020001020                __malloc_free_list
 .bss.errno     0x0000000020001024        0x4 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
                0x0000000020001024                errno
 .bss.m117_initialized
                0x0000000020001028        0x1 app/libapp.a(main_temp_low_power.c.obj)
 .bss.static_regions_num
                0x0000000020001029        0x1 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss.twim_1_msg_buf
                0x000000002000102a       0x10 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .bss.z_sys_post_kernel
                0x000000002000103a        0x1 zephyr/kernel/libkernel.a(init.c.obj)
                0x000000002000103a                z_sys_post_kernel
 .bss.lock      0x000000002000103b        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .bss.lock      0x000000002000103b        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .bss.lock      0x000000002000103b        0x0 zephyr/kernel/libkernel.a(work.c.obj)
 .bss._sched_spinlock
                0x000000002000103b        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000002000103b                _sched_spinlock
 *(SORT_BY_ALIGNMENT(COMMON))
 *(SORT_BY_ALIGNMENT(.kernel_bss.*))
                0x000000002000103c                __bss_end = ALIGN (0x4)

noinit          0x0000000020001040     0x1140
 *(SORT_BY_ALIGNMENT(.noinit))
 *(SORT_BY_ALIGNMENT(.noinit.*))
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".2
                0x0000000020001040      0x800 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020001040                z_interrupt_stacks
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".1
                0x0000000020001840      0x140 zephyr/kernel/libkernel.a(init.c.obj)
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".0
                0x0000000020001980      0x400 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020001980                z_main_stack
 .noinit."WEST_TOPDIR/zephyr/kernel/system_work_q.c".0
                0x0000000020001d80      0x400 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 *(SORT_BY_ALIGNMENT(.kernel_noinit.*))
                0x0000000020070000                __kernel_ram_end = 0x20070000
                0x000000000006fc00                __kernel_ram_size = (__kernel_ram_end - __kernel_ram_start)
                0x0000000000004c7c                PROVIDE (soc_reset_hook = SystemInit)

/DISCARD/
 *(SORT_BY_ALIGNMENT(.irq_info*))
 *(SORT_BY_ALIGNMENT(.intList*))

.last_ram_section
                0x0000000020002180        0x0
                0x0000000020002180                _image_ram_end = .
                0x0000000000002180                _image_ram_size = (_image_ram_end - _image_ram_start)
                0x0000000020002180                _end = .
                0x0000000020002180                z_mapped_end = .

.stab
 *(SORT_BY_ALIGNMENT(.stab))

.stabstr
 *(SORT_BY_ALIGNMENT(.stabstr))

.stab.excl
 *(SORT_BY_ALIGNMENT(.stab.excl))

.stab.exclstr
 *(SORT_BY_ALIGNMENT(.stab.exclstr))

.stab.index
 *(SORT_BY_ALIGNMENT(.stab.index))

.stab.indexstr
 *(SORT_BY_ALIGNMENT(.stab.indexstr))

.gnu.build.attributes
 *(SORT_BY_ALIGNMENT(.gnu.build.attributes) SORT_BY_ALIGNMENT(.gnu.build.attributes.*))

.comment        0x0000000000000000       0x40
 *(SORT_BY_ALIGNMENT(.comment))
 .comment       0x0000000000000000       0x20 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                                         0x21 (size before relaxing)
 .comment       0x0000000000000020       0x21 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .comment       0x0000000000000020       0x21 app/libapp.a(main_temp_low_power.c.obj)
 .comment       0x0000000000000020       0x21 app/libapp.a(zephyr_i2c_driver.c.obj)
 .comment       0x0000000000000020       0x21 app/libapp.a(m117_sensor.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(printk.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(thread_entry.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(poweroff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(onoff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(notify.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(configs.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(mem_attr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(device.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(device_runtime.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(banner.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .comment       0x0000000000000020       0x21 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(device.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(fatal.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(init.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(init_static.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(idle.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(mutex.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(sem.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(work.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(thread.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(sched.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(xip.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(timeout.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(events.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .comment       0x0000000000000020       0x20 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                                         0x21 (size before relaxing)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)

.debug
 *(SORT_BY_ALIGNMENT(.debug))

.line
 *(SORT_BY_ALIGNMENT(.line))

.debug_srcinfo
 *(SORT_BY_ALIGNMENT(.debug_srcinfo))

.debug_sfnames
 *(SORT_BY_ALIGNMENT(.debug_sfnames))

.debug_aranges  0x0000000000000000     0x1818
 *(SORT_BY_ALIGNMENT(.debug_aranges))
 .debug_aranges
                0x0000000000000000       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_aranges
                0x0000000000000020       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_aranges
                0x0000000000000040       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_aranges
                0x0000000000000060       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_aranges
                0x0000000000000088       0x18 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_aranges
                0x00000000000000a0       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_aranges
                0x00000000000000c0       0x20 app/libapp.a(main_temp_low_power.c.obj)
 .debug_aranges
                0x00000000000000e0       0x48 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_aranges
                0x0000000000000128       0x38 app/libapp.a(m117_sensor.c.obj)
 .debug_aranges
                0x0000000000000160       0x68 zephyr/libzephyr.a(printk.c.obj)
 .debug_aranges
                0x00000000000001c8       0x20 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_aranges
                0x00000000000001e8       0x48 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_aranges
                0x0000000000000230       0x20 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_aranges
                0x0000000000000250       0x90 zephyr/libzephyr.a(onoff.c.obj)
 .debug_aranges
                0x00000000000002e0       0x28 zephyr/libzephyr.a(notify.c.obj)
 .debug_aranges
                0x0000000000000308       0x20 zephyr/libzephyr.a(configs.c.obj)
 .debug_aranges
                0x0000000000000328       0x28 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_aranges
                0x0000000000000350       0x90 zephyr/libzephyr.a(device.c.obj)
 .debug_aranges
                0x00000000000003e0       0x88 zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_aranges
                0x0000000000000468       0x20 zephyr/libzephyr.a(banner.c.obj)
 .debug_aranges
                0x0000000000000488       0x28 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_aranges
                0x00000000000004b0       0x40 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_aranges
                0x00000000000004f0       0x30 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_aranges
                0x0000000000000520       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_aranges
                0x0000000000000540       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_aranges
                0x0000000000000560       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_aranges
                0x00000000000005a0       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_aranges
                0x00000000000005d0       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_aranges
                0x00000000000005f0       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_aranges
                0x0000000000000618       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_aranges
                0x0000000000000658       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_aranges
                0x0000000000000678       0x50 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_aranges
                0x00000000000006c8       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_aranges
                0x00000000000006f8       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_aranges
                0x0000000000000718       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_aranges
                0x0000000000000738       0x40 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_aranges
                0x0000000000000778       0x28 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_aranges
                0x00000000000007a0       0x68 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_aranges
                0x0000000000000808       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_aranges
                0x0000000000000820      0x110 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_aranges
                0x0000000000000930       0x20 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_aranges
                0x0000000000000950       0x38 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_aranges
                0x0000000000000988       0x38 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_aranges
                0x00000000000009c0       0x20 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_aranges
                0x00000000000009e0       0xe0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_aranges
                0x0000000000000ac0       0x68 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_aranges
                0x0000000000000b28       0x38 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_aranges
                0x0000000000000b60       0x48 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_aranges
                0x0000000000000ba8       0x20 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_aranges
                0x0000000000000bc8       0x28 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_aranges
                0x0000000000000bf0       0xd8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_aranges
                0x0000000000000cc8       0x28 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_aranges
                0x0000000000000cf0       0x40 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_aranges
                0x0000000000000d30       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_aranges
                0x0000000000000d60       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_aranges
                0x0000000000000d90       0xf0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_aranges
                0x0000000000000e80       0x70 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_aranges
                0x0000000000000ef0       0x78 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_aranges
                0x0000000000000f68      0x188 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_aranges
                0x00000000000010f0       0xa0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_aranges
                0x0000000000001190       0x30 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_aranges
                0x00000000000011c0       0x20 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_aranges
                0x00000000000011e0       0x38 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_aranges
                0x0000000000001218       0x38 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_aranges
                0x0000000000001250       0x68 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_aranges
                0x00000000000012b8       0x20 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_aranges
                0x00000000000012d8       0x48 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_aranges
                0x0000000000001320       0x28 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_aranges
                0x0000000000001348       0x38 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_aranges
                0x0000000000001380       0x38 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_aranges
                0x00000000000013b8      0x110 zephyr/kernel/libkernel.a(work.c.obj)
 .debug_aranges
                0x00000000000014c8       0x90 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_aranges
                0x0000000000001558      0x188 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_aranges
                0x00000000000016e0       0x20 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_aranges
                0x0000000000001700       0x90 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_aranges
                0x0000000000001790       0x68 zephyr/kernel/libkernel.a(events.c.obj)
 .debug_aranges
                0x00000000000017f8       0x20 zephyr/kernel/libkernel.a(system_work_q.c.obj)

.debug_pubnames
 *(SORT_BY_ALIGNMENT(.debug_pubnames))

.debug_info     0x0000000000000000    0x571e7
 *(SORT_BY_ALIGNMENT(.debug_info) SORT_BY_ALIGNMENT(.gnu.linkonce.wi.*))
 .debug_info    0x0000000000000000      0x190 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_info    0x0000000000000190       0xe3 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_info    0x0000000000000273      0xfb3 app/libapp.a(main_temp_low_power.c.obj)
 .debug_info    0x0000000000001226      0xc28 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_info    0x0000000000001e4e      0x9ee app/libapp.a(m117_sensor.c.obj)
 .debug_info    0x000000000000283c      0x715 zephyr/libzephyr.a(printk.c.obj)
 .debug_info    0x0000000000002f51      0x7f1 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_info    0x0000000000003742     0x15b1 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_info    0x0000000000004cf3      0x1aa zephyr/libzephyr.a(poweroff.c.obj)
 .debug_info    0x0000000000004e9d     0x28ea zephyr/libzephyr.a(onoff.c.obj)
 .debug_info    0x0000000000007787      0x324 zephyr/libzephyr.a(notify.c.obj)
 .debug_info    0x0000000000007aab       0x38 zephyr/libzephyr.a(configs.c.obj)
 .debug_info    0x0000000000007ae3      0x2c1 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_info    0x0000000000007da4     0x1668 zephyr/libzephyr.a(device.c.obj)
 .debug_info    0x000000000000940c     0x2863 zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_info    0x000000000000bc6f       0xe3 zephyr/libzephyr.a(banner.c.obj)
 .debug_info    0x000000000000bd52      0x24d zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_info    0x000000000000bf9f     0x11ff zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_info    0x000000000000d19e      0x78d zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_info    0x000000000000d92b       0xbd zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_info    0x000000000000d9e8       0x23 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_info    0x000000000000da0b      0xae0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_info    0x000000000000e4eb     0x1787 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_info    0x000000000000fc72       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_info    0x000000000000fc95       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_info    0x000000000000fcb8      0x95f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_info    0x0000000000010617      0xb87 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_info    0x000000000001119e      0xd26 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_info    0x0000000000011ec4       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_info    0x0000000000011ee6      0xacc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_info    0x00000000000129b2      0x6e5 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_info    0x0000000000013097     0x103c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_info    0x00000000000140d3      0x632 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_info    0x0000000000014705      0x741 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_info    0x0000000000014e46      0x1aa zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_info    0x0000000000014ff0      0x637 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_info    0x0000000000015627      0x90a zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_info    0x0000000000015f31     0x19ab zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_info    0x00000000000178dc      0x1f8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_info    0x0000000000017ad4     0x19e6 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_info    0x00000000000194ba      0x1c2 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_info    0x000000000001967c     0x13ae zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_info    0x000000000001aa2a      0xdec zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_info    0x000000000001b816      0x305 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_info    0x000000000001bb1b     0x3997 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_info    0x000000000001f4b2     0x28e4 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_info    0x0000000000021d96     0x218f zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_info    0x0000000000023f25     0x1ce9 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_info    0x0000000000025c0e      0x1af zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_info    0x0000000000025dbd     0x1cb6 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_info    0x0000000000027a73     0x364b zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_info    0x000000000002b0be      0x3d4 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_info    0x000000000002b492     0x1a92 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_info    0x000000000002cf24      0x264 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_info    0x000000000002d188      0x35b modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_info    0x000000000002d4e3     0x1637 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_info    0x000000000002eb1a     0x286a modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_info    0x0000000000031384     0x1bc5 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_info    0x0000000000032f49     0x63ac modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_info    0x00000000000392f5     0x4ef5 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_info    0x000000000003e1ea      0xa16 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_info    0x000000000003ec00       0xea zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_info    0x000000000003ecea      0x39b zephyr/kernel/libkernel.a(device.c.obj)
 .debug_info    0x000000000003f085      0xebb zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_info    0x000000000003ff40     0x21e1 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_info    0x0000000000042121       0x38 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_info    0x0000000000042159     0x143d zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_info    0x0000000000043596      0x2ef zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_info    0x0000000000043885     0x166b zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_info    0x0000000000044ef0     0x1078 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_info    0x0000000000045f68     0x5962 zephyr/kernel/libkernel.a(work.c.obj)
 .debug_info    0x000000000004b8ca     0x16f3 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_info    0x000000000004cfbd     0x671f zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_info    0x00000000000536dc      0x149 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_info    0x0000000000053825     0x1bd6 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_info    0x00000000000553fb     0x1333 zephyr/kernel/libkernel.a(events.c.obj)
 .debug_info    0x000000000005672e      0xab9 zephyr/kernel/libkernel.a(system_work_q.c.obj)

.debug_abbrev   0x0000000000000000     0xe908
 *(SORT_BY_ALIGNMENT(.debug_abbrev))
 .debug_abbrev  0x0000000000000000      0x118 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_abbrev  0x0000000000000118       0x62 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_abbrev  0x000000000000017a      0x38c app/libapp.a(main_temp_low_power.c.obj)
 .debug_abbrev  0x0000000000000506      0x348 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_abbrev  0x000000000000084e      0x2d0 app/libapp.a(m117_sensor.c.obj)
 .debug_abbrev  0x0000000000000b1e      0x320 zephyr/libzephyr.a(printk.c.obj)
 .debug_abbrev  0x0000000000000e3e      0x25a zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_abbrev  0x0000000000001098      0x41f zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_abbrev  0x00000000000014b7      0x152 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_abbrev  0x0000000000001609      0x54f zephyr/libzephyr.a(onoff.c.obj)
 .debug_abbrev  0x0000000000001b58      0x1bf zephyr/libzephyr.a(notify.c.obj)
 .debug_abbrev  0x0000000000001d17       0x2e zephyr/libzephyr.a(configs.c.obj)
 .debug_abbrev  0x0000000000001d45      0x142 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_abbrev  0x0000000000001e87      0x504 zephyr/libzephyr.a(device.c.obj)
 .debug_abbrev  0x000000000000238b      0x6a3 zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_abbrev  0x0000000000002a2e       0x9c zephyr/libzephyr.a(banner.c.obj)
 .debug_abbrev  0x0000000000002aca      0x13d zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_abbrev  0x0000000000002c07      0x406 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_abbrev  0x000000000000300d      0x2db zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_abbrev  0x00000000000032e8       0x70 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_abbrev  0x0000000000003358       0x14 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_abbrev  0x000000000000336c      0x19f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_abbrev  0x000000000000350b      0x421 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_abbrev  0x000000000000392c       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_abbrev  0x0000000000003940       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_abbrev  0x0000000000003954      0x286 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_abbrev  0x0000000000003bda      0x247 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_abbrev  0x0000000000003e21      0x2d3 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_abbrev  0x00000000000040f4       0x12 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_abbrev  0x0000000000004106      0x28e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_abbrev  0x0000000000004394      0x1cb zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_abbrev  0x000000000000455f      0x3f3 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_abbrev  0x0000000000004952      0x180 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_abbrev  0x0000000000004ad2      0x159 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_abbrev  0x0000000000004c2b      0x136 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_abbrev  0x0000000000004d61      0x1b2 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_abbrev  0x0000000000004f13      0x21f zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_abbrev  0x0000000000005132      0x507 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_abbrev  0x0000000000005639      0x112 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_abbrev  0x000000000000574b      0x68f zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_abbrev  0x0000000000005dda      0x14d zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_abbrev  0x0000000000005f27      0x45b zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_abbrev  0x0000000000006382      0x486 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_abbrev  0x0000000000006808      0x141 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_abbrev  0x0000000000006949      0x7b7 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_abbrev  0x0000000000007100      0x5ec zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_abbrev  0x00000000000076ec      0x4fd zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_abbrev  0x0000000000007be9      0x482 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_abbrev  0x000000000000806b       0xd0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_abbrev  0x000000000000813b      0x2ca zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_abbrev  0x0000000000008405      0x71a zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_abbrev  0x0000000000008b1f      0x246 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_abbrev  0x0000000000008d65      0x2b6 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_abbrev  0x000000000000901b      0x155 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_abbrev  0x0000000000009170      0x10b modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_abbrev  0x000000000000927b      0x35b modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_abbrev  0x00000000000095d6      0x524 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_abbrev  0x0000000000009afa      0x3f4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_abbrev  0x0000000000009eee      0x5b4 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_abbrev  0x000000000000a4a2      0x51c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_abbrev  0x000000000000a9be      0x2d0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_abbrev  0x000000000000ac8e       0x9d zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_abbrev  0x000000000000ad2b      0x1fb zephyr/kernel/libkernel.a(device.c.obj)
 .debug_abbrev  0x000000000000af26      0x3c3 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_abbrev  0x000000000000b2e9      0x70d zephyr/kernel/libkernel.a(init.c.obj)
 .debug_abbrev  0x000000000000b9f6       0x2e zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_abbrev  0x000000000000ba24      0x576 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_abbrev  0x000000000000bf9a      0x1df zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_abbrev  0x000000000000c179      0x437 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_abbrev  0x000000000000c5b0      0x405 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_abbrev  0x000000000000c9b5      0x6b0 zephyr/kernel/libkernel.a(work.c.obj)
 .debug_abbrev  0x000000000000d065      0x4c9 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_abbrev  0x000000000000d52e      0x68c zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_abbrev  0x000000000000dbba       0xbe zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_abbrev  0x000000000000dc78      0x511 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_abbrev  0x000000000000e189      0x4ee zephyr/kernel/libkernel.a(events.c.obj)
 .debug_abbrev  0x000000000000e677      0x291 zephyr/kernel/libkernel.a(system_work_q.c.obj)

.debug_line     0x0000000000000000    0x26c08
 *(SORT_BY_ALIGNMENT(.debug_line) SORT_BY_ALIGNMENT(.debug_line.*) SORT_BY_ALIGNMENT(.debug_line_end))
 .debug_line    0x0000000000000000      0x176 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .debug_line    0x0000000000000176      0x12b zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_line    0x00000000000002a1      0x508 app/libapp.a(main_temp_low_power.c.obj)
 .debug_line    0x00000000000007a9      0x52d app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_line    0x0000000000000cd6      0x459 app/libapp.a(m117_sensor.c.obj)
 .debug_line    0x000000000000112f      0x4b2 zephyr/libzephyr.a(printk.c.obj)
 .debug_line    0x00000000000015e1      0x3ae zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_line    0x000000000000198f     0x1459 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_line    0x0000000000002de8      0x272 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_line    0x000000000000305a     0x16ca zephyr/libzephyr.a(onoff.c.obj)
 .debug_line    0x0000000000004724      0x276 zephyr/libzephyr.a(notify.c.obj)
 .debug_line    0x000000000000499a      0x258 zephyr/libzephyr.a(configs.c.obj)
 .debug_line    0x0000000000004bf2      0x26e zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_line    0x0000000000004e60      0x88f zephyr/libzephyr.a(device.c.obj)
 .debug_line    0x00000000000056ef     0x1263 zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_line    0x0000000000006952       0xb7 zephyr/libzephyr.a(banner.c.obj)
 .debug_line    0x0000000000006a09      0x232 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_line    0x0000000000006c3b      0x7b9 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_line    0x00000000000073f4      0x3d8 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_line    0x00000000000077cc       0xb1 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_line    0x000000000000787d       0x6a zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_line    0x00000000000078e7      0x358 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_line    0x0000000000007c3f      0x96d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_line    0x00000000000085ac       0x77 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_line    0x0000000000008623       0x91 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_line    0x00000000000086b4      0x4ad zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_line    0x0000000000008b61      0x417 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_line    0x0000000000008f78      0x4aa zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_line    0x0000000000009422       0xc4 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_line    0x00000000000094e6      0x3b8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_line    0x000000000000989e      0x3ad zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_line    0x0000000000009c4b      0x80f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_line    0x000000000000a45a      0x34c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_line    0x000000000000a7a6      0x25e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_line    0x000000000000aa04      0x26f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_line    0x000000000000ac73      0x330 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_line    0x000000000000afa3      0x45a zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_line    0x000000000000b3fd      0xe93 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_line    0x000000000000c290      0x23d zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_line    0x000000000000c4cd      0xaaf zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_line    0x000000000000cf7c      0x304 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_line    0x000000000000d280      0x684 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_line    0x000000000000d904      0x4c6 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_line    0x000000000000ddca      0x250 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_line    0x000000000000e01a     0x15d6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_line    0x000000000000f5f0      0xddf zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_line    0x00000000000103cf      0x9a7 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_line    0x0000000000010d76      0x81e zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_line    0x0000000000011594      0x1fc zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_line    0x0000000000011790      0x608 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_line    0x0000000000011d98     0x16f6 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_line    0x000000000001348e      0x273 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_line    0x0000000000013701      0x5f3 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_line    0x0000000000013cf4      0x294 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_line    0x0000000000013f88      0x30f modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_line    0x0000000000014297      0x8a1 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_line    0x0000000000014b38     0x10e6 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_line    0x0000000000015c1e      0xc63 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_line    0x0000000000016881     0x232f modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_line    0x0000000000018bb0     0x1dec modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_line    0x000000000001a99c      0x526 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_line    0x000000000001aec2      0x197 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_line    0x000000000001b059      0x3b6 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_line    0x000000000001b40f      0x661 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_line    0x000000000001ba70      0xce5 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_line    0x000000000001c755       0x63 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_line    0x000000000001c7b8      0x972 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_line    0x000000000001d12a      0x346 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_line    0x000000000001d470      0x9f8 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_line    0x000000000001de68      0x80f zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_line    0x000000000001e677     0x24f9 zephyr/kernel/libkernel.a(work.c.obj)
 .debug_line    0x0000000000020b70      0xac2 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_line    0x0000000000021632     0x3504 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_line    0x0000000000024b36      0x242 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_line    0x0000000000024d78     0x1201 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_line    0x0000000000025f79      0x915 zephyr/kernel/libkernel.a(events.c.obj)
 .debug_line    0x000000000002688e      0x37a zephyr/kernel/libkernel.a(system_work_q.c.obj)

.debug_frame    0x0000000000000000     0x4588
 *(SORT_BY_ALIGNMENT(.debug_frame))
 .debug_frame   0x0000000000000000       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_frame   0x0000000000000020       0x2c app/libapp.a(main_temp_low_power.c.obj)
 .debug_frame   0x000000000000004c       0xbc app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_frame   0x0000000000000108       0x8c app/libapp.a(m117_sensor.c.obj)
 .debug_frame   0x0000000000000194      0x118 zephyr/libzephyr.a(printk.c.obj)
 .debug_frame   0x00000000000002ac       0x28 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_frame   0x00000000000002d4       0xe0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_frame   0x00000000000003b4       0x28 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_frame   0x00000000000003dc      0x190 zephyr/libzephyr.a(onoff.c.obj)
 .debug_frame   0x000000000000056c       0x30 zephyr/libzephyr.a(notify.c.obj)
 .debug_frame   0x000000000000059c       0x20 zephyr/libzephyr.a(configs.c.obj)
 .debug_frame   0x00000000000005bc       0x30 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_frame   0x00000000000005ec      0x12c zephyr/libzephyr.a(device.c.obj)
 .debug_frame   0x0000000000000718      0x1b8 zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_frame   0x00000000000008d0       0x2c zephyr/libzephyr.a(banner.c.obj)
 .debug_frame   0x00000000000008fc       0x30 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_frame   0x000000000000092c       0x90 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_frame   0x00000000000009bc       0x48 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_frame   0x0000000000000a04       0x2c zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_frame   0x0000000000000a30       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_frame   0x0000000000000a50       0x88 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_frame   0x0000000000000ad8       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_frame   0x0000000000000b20       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_frame   0x0000000000000b40       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_frame   0x0000000000000b70       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_frame   0x0000000000000bd0       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_frame   0x0000000000000bf8       0x88 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_frame   0x0000000000000c80       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_frame   0x0000000000000cc0       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_frame   0x0000000000000ce0       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_frame   0x0000000000000d0c       0x70 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_frame   0x0000000000000d7c       0x3c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_frame   0x0000000000000db8      0x140 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_frame   0x0000000000000ef8      0x260 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_frame   0x0000000000001158       0x28 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_frame   0x0000000000001180       0x68 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_frame   0x00000000000011e8       0x80 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_frame   0x0000000000001268       0x20 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_frame   0x0000000000001288      0x264 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_frame   0x00000000000014ec      0x104 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_frame   0x00000000000015f0       0x84 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_frame   0x0000000000001674       0xc4 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_frame   0x0000000000001738       0x2c zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_frame   0x0000000000001764       0x50 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_frame   0x00000000000017b4      0x268 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_frame   0x0000000000001a1c       0x3c modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_frame   0x0000000000001a58       0x68 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_frame   0x0000000000001ac0       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_frame   0x0000000000001b08       0x54 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_frame   0x0000000000001b5c      0x1c0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_frame   0x0000000000001d1c      0x10c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_frame   0x0000000000001e28      0x140 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_frame   0x0000000000001f68      0x4c0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_frame   0x0000000000002428      0x1b8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_frame   0x00000000000025e0       0x5c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_frame   0x000000000000263c       0x20 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_frame   0x000000000000265c       0x5c zephyr/kernel/libkernel.a(device.c.obj)
 .debug_frame   0x00000000000026b8       0x74 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_frame   0x000000000000272c      0x114 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_frame   0x0000000000002840       0x20 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_frame   0x0000000000002860       0xb8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_frame   0x0000000000002918       0x38 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_frame   0x0000000000002950       0x74 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_frame   0x00000000000029c4       0x8c zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_frame   0x0000000000002a50      0x34c zephyr/kernel/libkernel.a(work.c.obj)
 .debug_frame   0x0000000000002d9c      0x158 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_frame   0x0000000000002ef4      0x4dc zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_frame   0x00000000000033d0       0x2c zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_frame   0x00000000000033fc      0x19c zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_frame   0x0000000000003598       0xe8 zephyr/kernel/libkernel.a(events.c.obj)
 .debug_frame   0x0000000000003680       0x28 zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .debug_frame   0x00000000000036a8       0x30 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
 .debug_frame   0x00000000000036d8       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
 .debug_frame   0x0000000000003700       0x20 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
 .debug_frame   0x0000000000003720       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
 .debug_frame   0x0000000000003758       0x30 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
 .debug_frame   0x0000000000003788       0x94 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
 .debug_frame   0x000000000000381c       0x60 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
 .debug_frame   0x000000000000387c       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
 .debug_frame   0x00000000000038a8       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
 .debug_frame   0x00000000000038d0       0x6c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
 .debug_frame   0x000000000000393c       0x40 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .debug_frame   0x000000000000397c       0x28 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
 .debug_frame   0x00000000000039a4       0x30 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
 .debug_frame   0x00000000000039d4      0x260 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .debug_frame   0x0000000000003c34       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
 .debug_frame   0x0000000000003c60       0x5c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .debug_frame   0x0000000000003cbc       0x40 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
 .debug_frame   0x0000000000003cfc       0x64 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
 .debug_frame   0x0000000000003d60       0x48 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .debug_frame   0x0000000000003da8       0x48 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
 .debug_frame   0x0000000000003df0       0xa8 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .debug_frame   0x0000000000003e98       0x40 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
 .debug_frame   0x0000000000003ed8       0x3c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
 .debug_frame   0x0000000000003f14       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
 .debug_frame   0x0000000000003f40       0x5c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
 .debug_frame   0x0000000000003f9c      0x14c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .debug_frame   0x00000000000040e8       0x54 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
 .debug_frame   0x000000000000413c       0x58 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
 .debug_frame   0x0000000000004194       0x88 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
 .debug_frame   0x000000000000421c       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
 .debug_frame   0x0000000000004248       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
 .debug_frame   0x0000000000004274       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
 .debug_frame   0x00000000000042a0       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
 .debug_frame   0x00000000000042cc       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
 .debug_frame   0x00000000000042f8       0x2c C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
 .debug_frame   0x0000000000004324       0xac c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x00000000000043d0       0x50 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x0000000000004420       0xc4 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x00000000000044e4       0x20 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .debug_frame   0x0000000000004504       0x24 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x0000000000004528       0x2c c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x0000000000004554       0x34 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)

.debug_str      0x0000000000000000     0xdbc8
 *(SORT_BY_ALIGNMENT(.debug_str))
 .debug_str     0x0000000000000000      0x2d2 zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
                                        0x31b (size before relaxing)
 .debug_str     0x00000000000002d2      0x296 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                                        0x34e (size before relaxing)
 .debug_str     0x0000000000000568      0x752 app/libapp.a(main_temp_low_power.c.obj)
                                        0xb9d (size before relaxing)
 .debug_str     0x0000000000000cba      0x3dd app/libapp.a(zephyr_i2c_driver.c.obj)
                                        0x77c (size before relaxing)
 .debug_str     0x0000000000001097       0xd3 app/libapp.a(m117_sensor.c.obj)
                                        0x5bb (size before relaxing)
 .debug_str     0x000000000000116a      0x13e zephyr/libzephyr.a(printk.c.obj)
                                        0x5d3 (size before relaxing)
 .debug_str     0x00000000000012a8       0xa2 zephyr/libzephyr.a(thread_entry.c.obj)
                                        0x5c9 (size before relaxing)
 .debug_str     0x000000000000134a      0x418 zephyr/libzephyr.a(cbprintf_complete.c.obj)
                                        0x71b (size before relaxing)
 .debug_str     0x0000000000001762       0x79 zephyr/libzephyr.a(poweroff.c.obj)
                                        0x2a7 (size before relaxing)
 .debug_str     0x00000000000017db      0x48a zephyr/libzephyr.a(onoff.c.obj)
                                        0x801 (size before relaxing)
 .debug_str     0x0000000000001c65       0x45 zephyr/libzephyr.a(notify.c.obj)
                                        0x323 (size before relaxing)
 .debug_str     0x0000000000001caa       0x46 zephyr/libzephyr.a(configs.c.obj)
                                        0x1ab (size before relaxing)
 .debug_str     0x0000000000001cf0       0xab zephyr/libzephyr.a(mem_attr.c.obj)
                                        0x520 (size before relaxing)
 .debug_str     0x0000000000001d9b      0x295 zephyr/libzephyr.a(device.c.obj)
                                        0xb61 (size before relaxing)
 .debug_str     0x0000000000002030      0x426 zephyr/libzephyr.a(device_runtime.c.obj)
                                        0xf21 (size before relaxing)
 .debug_str     0x0000000000002456       0x3e zephyr/libzephyr.a(banner.c.obj)
                                        0x24b (size before relaxing)
 .debug_str     0x0000000000002494      0x11d zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                                        0x55b (size before relaxing)
 .debug_str     0x00000000000025b1      0x492 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                                        0xd15 (size before relaxing)
 .debug_str     0x0000000000002a43      0x100 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                                        0x503 (size before relaxing)
 .debug_str     0x0000000000002b43       0x59 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                                        0x25f (size before relaxing)
 .debug_str     0x0000000000002b9c       0x45 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                                         0x5c (size before relaxing)
 .debug_str     0x0000000000002be1      0x1b4 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                                        0x6df (size before relaxing)
 .debug_str     0x0000000000002d95      0x67b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                                        0xc97 (size before relaxing)
 .debug_str     0x0000000000003410       0x3d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                                         0x60 (size before relaxing)
 .debug_str     0x000000000000344d       0x3b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                                         0x5e (size before relaxing)
 .debug_str     0x0000000000003488      0x186 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                                        0x574 (size before relaxing)
 .debug_str     0x000000000000360e       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                                        0x729 (size before relaxing)
 .debug_str     0x000000000000366e       0x5b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
                                        0x74e (size before relaxing)
 .debug_str     0x00000000000036c9       0x41 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                                         0x64 (size before relaxing)
 .debug_str     0x000000000000370a      0x34c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                                        0x800 (size before relaxing)
 .debug_str     0x0000000000003a56       0xe0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                                        0x4d2 (size before relaxing)
 .debug_str     0x0000000000003b36      0x18a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                                        0x828 (size before relaxing)
 .debug_str     0x0000000000003cc0       0x75 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                                        0x41a (size before relaxing)
 .debug_str     0x0000000000003d35       0x3e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                                        0x6a6 (size before relaxing)
 .debug_str     0x0000000000003d73       0x41 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                                        0x2cb (size before relaxing)
 .debug_str     0x0000000000003db4      0x18c zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                                        0x3e3 (size before relaxing)
 .debug_str     0x0000000000003f40      0x1b8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                                        0x748 (size before relaxing)
 .debug_str     0x00000000000040f8      0x3c9 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                                        0xacd (size before relaxing)
 .debug_str     0x00000000000044c1       0x40 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                                        0x38a (size before relaxing)
 .debug_str     0x0000000000004501      0x4fa zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
                                        0xe97 (size before relaxing)
 .debug_str     0x00000000000049fb      0x1a4 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
                                        0x330 (size before relaxing)
 .debug_str     0x0000000000004b9f      0xb4c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                                        0xf47 (size before relaxing)
 .debug_str     0x00000000000056eb      0x270 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
                                        0x972 (size before relaxing)
 .debug_str     0x000000000000595b       0x53 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
                                        0x35f (size before relaxing)
 .debug_str     0x00000000000059ae     0x109a zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                                       0x1ccc (size before relaxing)
 .debug_str     0x0000000000006a48      0xc04 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                                       0x14cf (size before relaxing)
 .debug_str     0x000000000000764c      0x801 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
                                       0x1765 (size before relaxing)
 .debug_str     0x0000000000007e4d      0x15a zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
                                       0x1230 (size before relaxing)
 .debug_str     0x0000000000007fa7       0x35 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                                        0x2e4 (size before relaxing)
 .debug_str     0x0000000000007fdc      0x418 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                                        0xd2c (size before relaxing)
 .debug_str     0x00000000000083f4      0xafc zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                                       0x170c (size before relaxing)
 .debug_str     0x0000000000008ef0       0x87 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                                        0x5c3 (size before relaxing)
 .debug_str     0x0000000000008f77      0x4d1 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                                        0xdb7 (size before relaxing)
 .debug_str     0x0000000000009448       0x89 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                                        0x466 (size before relaxing)
 .debug_str     0x00000000000094d1       0xb1 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                                        0x4b1 (size before relaxing)
 .debug_str     0x0000000000009582      0xb17 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                                       0x1052 (size before relaxing)
 .debug_str     0x000000000000a099      0x267 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                                       0x15f9 (size before relaxing)
 .debug_str     0x000000000000a300      0x190 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                                        0xbb2 (size before relaxing)
 .debug_str     0x000000000000a490     0x11ed modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                                       0x246e (size before relaxing)
 .debug_str     0x000000000000b67d      0xa1d modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
                                       0x1d24 (size before relaxing)
 .debug_str     0x000000000000c09a       0x49 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
                                        0x7a5 (size before relaxing)
 .debug_str     0x000000000000c0e3       0x2f zephyr/kernel/libkernel.a(busy_wait.c.obj)
                                        0x278 (size before relaxing)
 .debug_str     0x000000000000c112       0x93 zephyr/kernel/libkernel.a(device.c.obj)
                                        0x36f (size before relaxing)
 .debug_str     0x000000000000c1a5       0x88 zephyr/kernel/libkernel.a(fatal.c.obj)
                                        0x86c (size before relaxing)
 .debug_str     0x000000000000c22d      0x538 zephyr/kernel/libkernel.a(init.c.obj)
                                       0x11db (size before relaxing)
 .debug_str     0x000000000000c765       0x31 zephyr/kernel/libkernel.a(init_static.c.obj)
                                        0x1a4 (size before relaxing)
 .debug_str     0x000000000000c796      0x176 zephyr/kernel/libkernel.a(mem_slab.c.obj)
                                        0xb58 (size before relaxing)
 .debug_str     0x000000000000c90c       0x4e zephyr/kernel/libkernel.a(idle.c.obj)
                                        0x369 (size before relaxing)
 .debug_str     0x000000000000c95a      0x150 zephyr/kernel/libkernel.a(mutex.c.obj)
                                        0x9dc (size before relaxing)
 .debug_str     0x000000000000caaa       0x4f zephyr/kernel/libkernel.a(sem.c.obj)
                                        0x930 (size before relaxing)
 .debug_str     0x000000000000caf9      0x51c zephyr/kernel/libkernel.a(work.c.obj)
                                       0x1313 (size before relaxing)
 .debug_str     0x000000000000d015      0x229 zephyr/kernel/libkernel.a(thread.c.obj)
                                        0xb7a (size before relaxing)
 .debug_str     0x000000000000d23e      0x657 zephyr/kernel/libkernel.a(sched.c.obj)
                                       0x125d (size before relaxing)
 .debug_str     0x000000000000d895       0x7d zephyr/kernel/libkernel.a(xip.c.obj)
                                        0x2da (size before relaxing)
 .debug_str     0x000000000000d912      0x12d zephyr/kernel/libkernel.a(timeout.c.obj)
                                        0x881 (size before relaxing)
 .debug_str     0x000000000000da3f      0x12c zephyr/kernel/libkernel.a(events.c.obj)
                                        0x859 (size before relaxing)
 .debug_str     0x000000000000db6b       0x5d zephyr/kernel/libkernel.a(system_work_q.c.obj)
                                        0x8cc (size before relaxing)

.debug_loc      0x0000000000000000    0x25232
 *(SORT_BY_ALIGNMENT(.debug_loc))
 .debug_loc     0x0000000000000000       0x95 app/libapp.a(main_temp_low_power.c.obj)
 .debug_loc     0x0000000000000095      0x65d app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_loc     0x00000000000006f2      0x308 app/libapp.a(m117_sensor.c.obj)
 .debug_loc     0x00000000000009fa      0x2ed zephyr/libzephyr.a(printk.c.obj)
 .debug_loc     0x0000000000000ce7       0xc0 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_loc     0x0000000000000da7     0x1bc0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_loc     0x0000000000002967       0x17 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_loc     0x000000000000297e     0x2057 zephyr/libzephyr.a(onoff.c.obj)
 .debug_loc     0x00000000000049d5      0x135 zephyr/libzephyr.a(notify.c.obj)
 .debug_loc     0x0000000000004b0a       0x6f zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_loc     0x0000000000004b79      0x866 zephyr/libzephyr.a(device.c.obj)
 .debug_loc     0x00000000000053df     0x106f zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_loc     0x000000000000644e       0x15 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_loc     0x0000000000006463      0x340 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_loc     0x00000000000067a3      0x11d zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_loc     0x00000000000068c0      0x720 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_loc     0x0000000000006fe0       0xbc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_loc     0x000000000000709c       0x3a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_loc     0x00000000000070d6       0xee zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_loc     0x00000000000071c4      0x1a1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_loc     0x0000000000007365       0x16 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_loc     0x000000000000737b      0x309 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_loc     0x0000000000007684       0x56 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_loc     0x00000000000076da       0x6a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_loc     0x0000000000007744       0x3f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_loc     0x0000000000007783      0x5f9 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_loc     0x0000000000007d7c       0x25 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_loc     0x0000000000007da1      0xcda zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_loc     0x0000000000008a7b      0x792 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_loc     0x000000000000920d       0x2c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_loc     0x0000000000009239      0x3fa zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_loc     0x0000000000009633      0x2b3 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_loc     0x00000000000098e6     0x18ca zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_loc     0x000000000000b1b0     0x189e zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_loc     0x000000000000ca4e      0x53c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_loc     0x000000000000cf8a      0x507 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_loc     0x000000000000d491       0x42 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_loc     0x000000000000d4d3      0x7dc zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_loc     0x000000000000dcaf     0x1c44 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_loc     0x000000000000f8f3      0x182 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_loc     0x000000000000fa75      0x258 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_loc     0x000000000000fccd      0x221 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_loc     0x000000000000feee      0x996 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_loc     0x0000000000010884     0x166c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_loc     0x0000000000011ef0      0xf8f modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_loc     0x0000000000012e7f     0x3eaa modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_loc     0x0000000000016d29     0x2874 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_loc     0x000000000001959d      0x3e5 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_loc     0x0000000000019982       0x32 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_loc     0x00000000000199b4       0xd2 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_loc     0x0000000000019a86      0x194 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_loc     0x0000000000019c1a      0x841 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_loc     0x000000000001a45b      0x51d zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_loc     0x000000000001a978       0x86 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_loc     0x000000000001a9fe      0x769 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_loc     0x000000000001b167      0x526 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_loc     0x000000000001b68d     0x3ab3 zephyr/kernel/libkernel.a(work.c.obj)
 .debug_loc     0x000000000001f140      0xa06 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_loc     0x000000000001fb46     0x3e47 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_loc     0x000000000002398d      0xf97 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_loc     0x0000000000024924      0x90e zephyr/kernel/libkernel.a(events.c.obj)

.debug_macinfo
 *(SORT_BY_ALIGNMENT(.debug_macinfo))

.debug_weaknames
 *(SORT_BY_ALIGNMENT(.debug_weaknames))

.debug_funcnames
 *(SORT_BY_ALIGNMENT(.debug_funcnames))

.debug_typenames
 *(SORT_BY_ALIGNMENT(.debug_typenames))

.debug_varnames
 *(SORT_BY_ALIGNMENT(.debug_varnames))

.debug_pubtypes
 *(SORT_BY_ALIGNMENT(.debug_pubtypes))

.debug_ranges   0x0000000000000000     0x62c0
 *(SORT_BY_ALIGNMENT(.debug_ranges))
 .debug_ranges  0x0000000000000000       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_ranges  0x0000000000000020       0x10 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_ranges  0x0000000000000030       0x60 app/libapp.a(main_temp_low_power.c.obj)
 .debug_ranges  0x0000000000000090       0xc0 app/libapp.a(zephyr_i2c_driver.c.obj)
 .debug_ranges  0x0000000000000150       0x90 app/libapp.a(m117_sensor.c.obj)
 .debug_ranges  0x00000000000001e0       0x88 zephyr/libzephyr.a(printk.c.obj)
 .debug_ranges  0x0000000000000268       0x10 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_ranges  0x0000000000000278      0x248 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_ranges  0x00000000000004c0       0x40 zephyr/libzephyr.a(poweroff.c.obj)
 .debug_ranges  0x0000000000000500      0x498 zephyr/libzephyr.a(onoff.c.obj)
 .debug_ranges  0x0000000000000998       0x30 zephyr/libzephyr.a(notify.c.obj)
 .debug_ranges  0x00000000000009c8       0x10 zephyr/libzephyr.a(configs.c.obj)
 .debug_ranges  0x00000000000009d8       0x18 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_ranges  0x00000000000009f0       0xc8 zephyr/libzephyr.a(device.c.obj)
 .debug_ranges  0x0000000000000ab8       0xf0 zephyr/libzephyr.a(device_runtime.c.obj)
 .debug_ranges  0x0000000000000ba8       0x10 zephyr/libzephyr.a(banner.c.obj)
 .debug_ranges  0x0000000000000bb8       0x18 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_ranges  0x0000000000000bd0       0x78 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_ranges  0x0000000000000c48       0x38 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_ranges  0x0000000000000c80       0x10 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_ranges  0x0000000000000c90       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_ranges  0x0000000000000ca0      0x110 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_ranges  0x0000000000000db0       0x68 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_ranges  0x0000000000000e18       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_ranges  0x0000000000000e28       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .debug_ranges  0x0000000000000e40       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_ranges  0x0000000000000e88       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_ranges  0x0000000000000eb0       0xb0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_ranges  0x0000000000000f60       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_ranges  0x0000000000000f80       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_ranges  0x0000000000000fb0       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_ranges  0x0000000000000fd8       0xd8 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_ranges  0x00000000000010b0       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_ranges  0x00000000000010c8      0x3d0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_ranges  0x0000000000001498      0x190 zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .debug_ranges  0x0000000000001628       0x10 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_ranges  0x0000000000001638       0xd0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_ranges  0x0000000000001708       0xb0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_ranges  0x00000000000017b8       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .debug_ranges  0x00000000000017c8      0x488 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_ranges  0x0000000000001c50      0x2e8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_ranges  0x0000000000001f38       0xa8 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .debug_ranges  0x0000000000001fe0       0xa0 zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .debug_ranges  0x0000000000002080       0x10 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_ranges  0x0000000000002090      0x110 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_ranges  0x00000000000021a0      0x5e8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_ranges  0x0000000000002788       0x30 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_ranges  0x00000000000027b8       0x48 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_ranges  0x0000000000002800       0x20 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_ranges  0x0000000000002820       0x50 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_ranges  0x0000000000002870       0xf8 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_ranges  0x0000000000002968      0x1f0 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_ranges  0x0000000000002b58      0x328 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_ranges  0x0000000000002e80      0xb68 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_ranges  0x00000000000039e8      0x588 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .debug_ranges  0x0000000000003f70      0x188 modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .debug_ranges  0x00000000000040f8       0x10 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_ranges  0x0000000000004108       0xa0 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_ranges  0x00000000000041a8       0x70 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_ranges  0x0000000000004218      0x280 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_ranges  0x0000000000004498       0x10 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_ranges  0x00000000000044a8       0xd8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_ranges  0x0000000000004580       0x48 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_ranges  0x00000000000045c8       0xe8 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_ranges  0x00000000000046b0       0xd0 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_ranges  0x0000000000004780      0x798 zephyr/kernel/libkernel.a(work.c.obj)
 .debug_ranges  0x0000000000004f18      0x1c0 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_ranges  0x00000000000050d8      0xca0 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_ranges  0x0000000000005d78       0x10 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_ranges  0x0000000000005d88      0x470 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_ranges  0x00000000000061f8       0xb8 zephyr/kernel/libkernel.a(events.c.obj)
 .debug_ranges  0x00000000000062b0       0x10 zephyr/kernel/libkernel.a(system_work_q.c.obj)

.debug_addr
 *(SORT_BY_ALIGNMENT(.debug_addr))

.debug_line_str
 *(SORT_BY_ALIGNMENT(.debug_line_str))

.debug_loclists
 *(SORT_BY_ALIGNMENT(.debug_loclists))

.debug_macro
 *(SORT_BY_ALIGNMENT(.debug_macro))

.debug_names
 *(SORT_BY_ALIGNMENT(.debug_names))

.debug_rnglists
 *(SORT_BY_ALIGNMENT(.debug_rnglists))

.debug_str_offsets
 *(SORT_BY_ALIGNMENT(.debug_str_offsets))

.debug_sup
 *(SORT_BY_ALIGNMENT(.debug_sup))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.note.GNU-stack))

.ARM.attributes
                0x0000000000000000       0x3a
 *(SORT_BY_ALIGNMENT(.ARM.attributes))
 .ARM.attributes
                0x0000000000000000       0x3c zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj
 .ARM.attributes
                0x000000000000003c       0x3c zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj
 .ARM.attributes
                0x0000000000000078       0x3c zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .ARM.attributes
                0x00000000000000b4       0x3c app/libapp.a(main_temp_low_power.c.obj)
 .ARM.attributes
                0x00000000000000f0       0x3c app/libapp.a(zephyr_i2c_driver.c.obj)
 .ARM.attributes
                0x000000000000012c       0x3c app/libapp.a(m117_sensor.c.obj)
 .ARM.attributes
                0x0000000000000168       0x3c zephyr/libzephyr.a(crc32c_sw.c.obj)
 .ARM.attributes
                0x00000000000001a4       0x3c zephyr/libzephyr.a(crc32_sw.c.obj)
 .ARM.attributes
                0x00000000000001e0       0x3c zephyr/libzephyr.a(crc24_sw.c.obj)
 .ARM.attributes
                0x000000000000021c       0x3c zephyr/libzephyr.a(crc16_sw.c.obj)
 .ARM.attributes
                0x0000000000000258       0x3c zephyr/libzephyr.a(crc8_sw.c.obj)
 .ARM.attributes
                0x0000000000000294       0x3c zephyr/libzephyr.a(crc7_sw.c.obj)
 .ARM.attributes
                0x00000000000002d0       0x3c zephyr/libzephyr.a(crc4_sw.c.obj)
 .ARM.attributes
                0x000000000000030c       0x3c zephyr/libzephyr.a(heap.c.obj)
 .ARM.attributes
                0x0000000000000348       0x3c zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .ARM.attributes
                0x0000000000000384       0x3c zephyr/libzephyr.a(printk.c.obj)
 .ARM.attributes
                0x00000000000003c0       0x3c zephyr/libzephyr.a(sem.c.obj)
 .ARM.attributes
                0x00000000000003fc       0x3c zephyr/libzephyr.a(thread_entry.c.obj)
 .ARM.attributes
                0x0000000000000438       0x3c zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .ARM.attributes
                0x0000000000000474       0x3c zephyr/libzephyr.a(cbprintf.c.obj)
 .ARM.attributes
                0x00000000000004b0       0x3c zephyr/libzephyr.a(assert.c.obj)
 .ARM.attributes
                0x00000000000004ec       0x3c zephyr/libzephyr.a(poweroff.c.obj)
 .ARM.attributes
                0x0000000000000528       0x3c zephyr/libzephyr.a(dec.c.obj)
 .ARM.attributes
                0x0000000000000564       0x3c zephyr/libzephyr.a(hex.c.obj)
 .ARM.attributes
                0x00000000000005a0       0x3c zephyr/libzephyr.a(rb.c.obj)
 .ARM.attributes
                0x00000000000005dc       0x3c zephyr/libzephyr.a(timeutil.c.obj)
 .ARM.attributes
                0x0000000000000618       0x3c zephyr/libzephyr.a(bitarray.c.obj)
 .ARM.attributes
                0x0000000000000654       0x3c zephyr/libzephyr.a(onoff.c.obj)
 .ARM.attributes
                0x0000000000000690       0x3c zephyr/libzephyr.a(notify.c.obj)
 .ARM.attributes
                0x00000000000006cc       0x3c zephyr/libzephyr.a(configs.c.obj)
 .ARM.attributes
                0x0000000000000708       0x3c zephyr/libzephyr.a(mem_attr.c.obj)
 .ARM.attributes
                0x0000000000000744       0x3c zephyr/libzephyr.a(device.c.obj)
 .ARM.attributes
                0x0000000000000780       0x3c zephyr/libzephyr.a(device_runtime.c.obj)
 .ARM.attributes
                0x00000000000007bc       0x3c zephyr/libzephyr.a(tracing_none.c.obj)
 .ARM.attributes
                0x00000000000007f8       0x3c zephyr/libzephyr.a(banner.c.obj)
 .ARM.attributes
                0x0000000000000834       0x3c zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .ARM.attributes
                0x0000000000000870       0x3c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .ARM.attributes
                0x00000000000008ac       0x3c zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .ARM.attributes
                0x00000000000008e8       0x3c zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .ARM.attributes
                0x0000000000000924       0x3c zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .ARM.attributes
                0x0000000000000960       0x26 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .ARM.attributes
                0x0000000000000986       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .ARM.attributes
                0x00000000000009c2       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .ARM.attributes
                0x00000000000009fe       0x26 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .ARM.attributes
                0x0000000000000a24       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .ARM.attributes
                0x0000000000000a60       0x26 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .ARM.attributes
                0x0000000000000a86       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .ARM.attributes
                0x0000000000000ac2       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .ARM.attributes
                0x0000000000000afe       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .ARM.attributes
                0x0000000000000b26       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap.c.obj)
 .ARM.attributes
                0x0000000000000b62       0x26 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .ARM.attributes
                0x0000000000000b88       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .ARM.attributes
                0x0000000000000bc4       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .ARM.attributes
                0x0000000000000c00       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .ARM.attributes
                0x0000000000000c3c       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .ARM.attributes
                0x0000000000000c78       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .ARM.attributes
                0x0000000000000cb4       0x3c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .ARM.attributes
                0x0000000000000cf0       0x3c zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .ARM.attributes
                0x0000000000000d2c       0x3c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .ARM.attributes
                0x0000000000000d68       0x3c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .ARM.attributes
                0x0000000000000da4       0x3c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .ARM.attributes
                0x0000000000000de0       0x3c zephyr/lib/libc/newlib/liblib__libc__newlib.a(libc-hooks.c.obj)
 .ARM.attributes
                0x0000000000000e1c       0x3c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .ARM.attributes
                0x0000000000000e58       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .ARM.attributes
                0x0000000000000e94       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .ARM.attributes
                0x0000000000000ed0       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .ARM.attributes
                0x0000000000000f0c       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .ARM.attributes
                0x0000000000000f48       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .ARM.attributes
                0x0000000000000f84       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(poweroff.c.obj)
 .ARM.attributes
                0x0000000000000fc0       0x3c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .ARM.attributes
                0x0000000000000ffc       0x3c zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .ARM.attributes
                0x0000000000001038       0x3c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_common.c.obj)
 .ARM.attributes
                0x0000000000001074       0x3c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim.c.obj)
 .ARM.attributes
                0x00000000000010b0       0x3c zephyr/drivers/i2c/libdrivers__i2c.a(i2c_nrfx_twim_common.c.obj)
 .ARM.attributes
                0x00000000000010ec       0x3c zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .ARM.attributes
                0x0000000000001128       0x3c zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .ARM.attributes
                0x0000000000001164       0x3c zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .ARM.attributes
                0x00000000000011a0       0x3c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .ARM.attributes
                0x00000000000011dc       0x3c modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .ARM.attributes
                0x0000000000001218       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .ARM.attributes
                0x0000000000001254       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .ARM.attributes
                0x0000000000001290       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .ARM.attributes
                0x00000000000012cc       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .ARM.attributes
                0x0000000000001308       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .ARM.attributes
                0x0000000000001344       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .ARM.attributes
                0x0000000000001380       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .ARM.attributes
                0x00000000000013bc       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twim.c.obj)
 .ARM.attributes
                0x00000000000013f8       0x3c modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_twi_twim.c.obj)
 .ARM.attributes
                0x0000000000001434       0x3c zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .ARM.attributes
                0x0000000000001470       0x3c zephyr/kernel/libkernel.a(device.c.obj)
 .ARM.attributes
                0x00000000000014ac       0x3c zephyr/kernel/libkernel.a(errno.c.obj)
 .ARM.attributes
                0x00000000000014e8       0x3c zephyr/kernel/libkernel.a(fatal.c.obj)
 .ARM.attributes
                0x0000000000001524       0x3c zephyr/kernel/libkernel.a(init.c.obj)
 .ARM.attributes
                0x0000000000001560       0x3c zephyr/kernel/libkernel.a(init_static.c.obj)
 .ARM.attributes
                0x000000000000159c       0x3c zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .ARM.attributes
                0x00000000000015d8       0x3c zephyr/kernel/libkernel.a(idle.c.obj)
 .ARM.attributes
                0x0000000000001614       0x3c zephyr/kernel/libkernel.a(mutex.c.obj)
 .ARM.attributes
                0x0000000000001650       0x3c zephyr/kernel/libkernel.a(sem.c.obj)
 .ARM.attributes
                0x000000000000168c       0x3c zephyr/kernel/libkernel.a(work.c.obj)
 .ARM.attributes
                0x00000000000016c8       0x3c zephyr/kernel/libkernel.a(thread.c.obj)
 .ARM.attributes
                0x0000000000001704       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .ARM.attributes
                0x0000000000001740       0x3c zephyr/kernel/libkernel.a(xip.c.obj)
 .ARM.attributes
                0x000000000000177c       0x3c zephyr/kernel/libkernel.a(timeout.c.obj)
 .ARM.attributes
                0x00000000000017b8       0x3c zephyr/kernel/libkernel.a(events.c.obj)
 .ARM.attributes
                0x00000000000017f4       0x3c zephyr/kernel/libkernel.a(system_work_q.c.obj)
 .ARM.attributes
                0x0000000000001830       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .ARM.attributes
                0x000000000000186a       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .ARM.attributes
                0x00000000000018a4       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .ARM.attributes
                0x00000000000018de       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .ARM.attributes
                0x0000000000001918       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .ARM.attributes
                0x0000000000001952       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .ARM.attributes
                0x000000000000198c       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .ARM.attributes
                0x00000000000019c6       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .ARM.attributes
                0x0000000000001a00       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .ARM.attributes
                0x0000000000001a3a       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .ARM.attributes
                0x0000000000001a74       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .ARM.attributes
                0x0000000000001aae       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000001ae8       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .ARM.attributes
                0x0000000000001b22       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .ARM.attributes
                0x0000000000001b5c       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .ARM.attributes
                0x0000000000001b96       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000001bd0       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .ARM.attributes
                0x0000000000001c0a       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .ARM.attributes
                0x0000000000001c44       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .ARM.attributes
                0x0000000000001c7e       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .ARM.attributes
                0x0000000000001cb8       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .ARM.attributes
                0x0000000000001cf2       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .ARM.attributes
                0x0000000000001d2c       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .ARM.attributes
                0x0000000000001d66       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .ARM.attributes
                0x0000000000001da0       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .ARM.attributes
                0x0000000000001dda       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .ARM.attributes
                0x0000000000001e14       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .ARM.attributes
                0x0000000000001e4e       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .ARM.attributes
                0x0000000000001e88       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .ARM.attributes
                0x0000000000001ec2       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .ARM.attributes
                0x0000000000001efc       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .ARM.attributes
                0x0000000000001f36       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .ARM.attributes
                0x0000000000001f70       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .ARM.attributes
                0x0000000000001faa       0x3a C:/ncs/v2.9.0-zigbee/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/hard-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .ARM.attributes
                0x0000000000001fe4       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-ctype_.o)
 .ARM.attributes
                0x000000000000201c       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-malloc.o)
 .ARM.attributes
                0x0000000000002054       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcmp.o)
 .ARM.attributes
                0x000000000000208c       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memcpy-stub.o)
 .ARM.attributes
                0x00000000000020c4       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memmove.o)
 .ARM.attributes
                0x00000000000020fc       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memset.o)
 .ARM.attributes
                0x0000000000002134       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-freer.o)
 .ARM.attributes
                0x000000000000216c       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-mallocr.o)
 .ARM.attributes
                0x00000000000021a4       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_float.o)
 .ARM.attributes
                0x00000000000021dc       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf_i.o)
 .ARM.attributes
                0x0000000000002214       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-sbrkr.o)
 .ARM.attributes
                0x000000000000224c       0x24 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strcmp.o)
 .ARM.attributes
                0x0000000000002270       0x17 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strlen.o)
 .ARM.attributes
                0x0000000000002287       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-strnlen.o)
 .ARM.attributes
                0x00000000000022bf       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-dtoa.o)
 .ARM.attributes
                0x00000000000022f7       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-impure.o)
 .ARM.attributes
                0x000000000000232f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-localeconv.o)
 .ARM.attributes
                0x0000000000002367       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-memchr-stub.o)
 .ARM.attributes
                0x000000000000239f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mlock.o)
 .ARM.attributes
                0x00000000000023d7       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mprec.o)
 .ARM.attributes
                0x000000000000240f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-callocr.o)
 .ARM.attributes
                0x0000000000002447       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-reent.o)
 .ARM.attributes
                0x000000000000247f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-assert.o)
 .ARM.attributes
                0x00000000000024b7       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fprintf.o)
 .ARM.attributes
                0x00000000000024ef       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-locale.o)
 .ARM.attributes
                0x0000000000002527       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-mbtowc_r.o)
 .ARM.attributes
                0x000000000000255f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-vfprintf.o)
 .ARM.attributes
                0x0000000000002597       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wbuf.o)
 .ARM.attributes
                0x00000000000025cf       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wctomb_r.o)
 .ARM.attributes
                0x0000000000002607       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-wsetup.o)
 .ARM.attributes
                0x000000000000263f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fflush.o)
 .ARM.attributes
                0x0000000000002677       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-findfp.o)
 .ARM.attributes
                0x00000000000026af       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fvwrite.o)
 .ARM.attributes
                0x00000000000026e7       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fwalk.o)
 .ARM.attributes
                0x000000000000271f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-makebuf.o)
 .ARM.attributes
                0x0000000000002757       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-reallocr.o)
 .ARM.attributes
                0x000000000000278f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-stdio.o)
 .ARM.attributes
                0x00000000000027c7       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-writer.o)
 .ARM.attributes
                0x00000000000027ff       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-closer.o)
 .ARM.attributes
                0x0000000000002837       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-fstatr.o)
 .ARM.attributes
                0x000000000000286f       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-isattyr.o)
 .ARM.attributes
                0x00000000000028a7       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-lseekr.o)
 .ARM.attributes
                0x00000000000028df       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-nano-msizer.o)
 .ARM.attributes
                0x0000000000002917       0x38 C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a(lib_a-readr.o)
 .ARM.attributes
                0x000000000000294f       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(cmse.o)
 .ARM.attributes
                0x0000000000002987       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000000029ad       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x00000000000029d3       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x00000000000029f9       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x0000000000002a1f       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .ARM.attributes
                0x0000000000002a45       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x0000000000002a6b       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .ARM.attributes
                0x0000000000002a91       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .ARM.attributes
                0x0000000000002ab7       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x0000000000002add       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_popcountsi2.o)
 .ARM.attributes
                0x0000000000002b15       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .ARM.attributes
                0x0000000000002b4d       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x0000000000002b85       0x38 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x0000000000002bbd       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x0000000000002be3       0x26 c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 *(SORT_BY_ALIGNMENT(.gnu.attributes))

.last_section   0x000000000000b2a0        0x4
                0x000000000000b2a0        0x4 LONG 0xe015e015
                0x000000000000b2a4                _flash_used = ((LOADADDR (.last_section) + SIZEOF (.last_section)) - __rom_region_start)
OUTPUT(zephyr\zephyr.elf elf32-littlearm)
LOAD linker stubs
