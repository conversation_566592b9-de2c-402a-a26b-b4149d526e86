/*
 * SW2600 Temperature Sensor + Low Power Mode
 * Based on successful 3.07µA power consumption test
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>

#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* Collect temperature data */
static void collect_temperature_data(void)
{
    LOG_INF("=== Temperature Data Collection ===");

    /* Initialize M117 sensor with default settings */
    int ret = m117_init(M117_MPS_1, M117_REPEAT_MEDIUM);
    if (ret < 0) {
        LOG_ERR("M117 init failed: %d", ret);
        return;
    }

    /* Read temperature using one-shot measurement */
    float temperature = 0.0f;
    ret = m117_measure_temperature(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature measurement failed: %d", ret);
    } else {
        LOG_INF("Temperature: %.2f°C", (double)temperature);
    }

    LOG_INF("=== Data Collection Complete ===");
}

/* Suspend all devices for low power */
static void suspend_devices_for_sleep(void)
{
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
    
    /* Note: I2C device will be automatically suspended when not in use */
}

int main(void)
{
    LOG_INF("\n=== SW2600 TEMP + LOW POWER TEST ===");
    LOG_INF("Target: <5µA sleep current");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
        /* Continue anyway for testing */
    }
    
    /* Collect temperature data */
    collect_temperature_data();
    
    /* Wait a bit for output */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Entering system off mode...");
    LOG_INF("Press button to wake up");
    
    /* Small delay for UART output */
    k_msleep(200);
    
    /* Suspend devices for low power */
    suspend_devices_for_sleep();
    
    /* Final delay */
    k_msleep(100);
    
    /* Enter system off mode directly */
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running - sys_poweroff failed!");
    }
    return 0;
}
