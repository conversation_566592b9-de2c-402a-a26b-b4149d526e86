/*
 * SW2600 Temperature Sensor - Working Version
 *
 * Features:
 * - M117 temperature sensor data collection
 * - Low power mode with 3.5µA sleep current (verified)
 * - 1-minute wake cycle for temperature measurement
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/pm/device.h>
#include <zephyr/logging/log.h>
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(sw2600_temp, CONFIG_LOG_DEFAULT_LEVEL);

/* Configuration */
#define DATA_COLLECTION_INTERVAL_S  60      // 1 minute data collection cycle
#define TEMPERATURE_READ_COUNT      3       // Number of temperature readings per cycle
#define COUNTDOWN_DELAY_S           5       // Countdown before sleep

/* Device references */
static const struct device *uart_dev;
static const struct device *i2c_dev;

/* Global state */
static bool m117_initialized = false;

/**
 * @brief Initialize M117 temperature sensor
 */
static int init_temperature_sensor(void)
{
    int ret;

    LOG_INF("Initializing M117 temperature sensor...");

    ret = m117_init(M117_MPS_1, M117_REPEAT_HIGH);
    if (ret < 0) {
        LOG_ERR("Failed to initialize M117 sensor: %d", ret);
        return ret;
    }

    m117_initialized = true;
    LOG_INF("M117 temperature sensor initialized successfully");

    return 0;
}

/**
 * @brief Collect temperature data
 */
static int collect_temperature_data(void)
{
    int ret;
    float temperature;
    float temp_sum = 0.0f;
    int valid_readings = 0;

    if (!m117_initialized) {
        LOG_ERR("M117 sensor not initialized");
        return -ENODEV;
    }

    LOG_INF("=== Temperature Data Collection ===");

    /* Collect multiple temperature readings */
    for (int i = 0; i < TEMPERATURE_READ_COUNT; i++) {
        ret = m117_measure_temperature(&temperature);
        if (ret == 0) {
            LOG_INF("Temperature reading %d: %.2f°C", i + 1, (double)temperature);
            temp_sum += temperature;
            valid_readings++;
        } else {
            LOG_WRN("Temperature reading %d failed: %d", i + 1, ret);
        }

        /* Small delay between readings */
        k_sleep(K_MSEC(100));
    }

    /* Calculate and display average */
    if (valid_readings > 0) {
        float avg_temperature = temp_sum / valid_readings;
        LOG_INF("Average temperature: %.2f°C (%d valid readings)",
                (double)avg_temperature, valid_readings);
        LOG_INF("Temperature data collection completed");
        return 0;
    } else {
        LOG_ERR("No valid temperature readings obtained");
        return -EIO;
    }
}

/**
 * @brief Resume I2C device after wake up
 */
static int resume_i2c_device(void)
{
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
        if (ret < 0) {
            printk("WARNING: Failed to resume I2C device: %d\n", ret);
            return ret;
        }
        printk("I2C device resumed\n");
    }
    
    return 0;
}

/**
 * @brief Suspend devices for power saving
 */
static void suspend_devices(void)
{
    printk("Suspending devices for power saving...\n");
    
    /* Suspend I2C */
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            printk("Failed to suspend I2C: %d\n", ret);
        } else {
            printk("I2C suspended\n");
        }
    }
    
    /* Suspend UART */
    uart_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (uart_dev && device_is_ready(uart_dev)) {
        int ret = pm_device_action_run(uart_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            printk("Failed to suspend UART: %d\n", ret);
        } else {
            printk("UART suspended\n");
        }
    }
}

/**
 * @brief Enter deep sleep mode
 */
static void enter_deep_sleep(void)
{
    printk("=== ENTERING DEEP SLEEP MODE ===\n");
    printk("Expected: Current should drop to ~3.5µA\n");
    printk("System will reset after %d seconds\n", DATA_COLLECTION_INTERVAL_S);
    
    /* Small delay to ensure UART output */
    k_msleep(100);
    
    /* Suspend devices */
    suspend_devices();
    
    /* Enter system off mode */
    sys_poweroff();
}

/**
 * @brief Main data collection and power management cycle
 */
static void data_collection_cycle(void)
{
    int ret;
    
    printk("=== SW2600 Data Collection Cycle ===\n");
    printk("Wake up for temperature data collection\n");
    
    /* Resume I2C device */
    resume_i2c_device();
    
    /* Collect temperature data */
    ret = collect_temperature_data();
    if (ret < 0) {
        printk("Temperature data collection failed: %d\n", ret);
    }
    
    printk("Data collection completed, preparing for sleep\n");
    printk("Next wake up in %d seconds\n", DATA_COLLECTION_INTERVAL_S);
}

/**
 * @brief Main function
 */
int main(void)
{
    int ret;
    
    printk("\n=== SW2600 Temperature Sensor Application ===\n");
    printk("Features:\n");
    printk("  - M117 temperature sensor\n");
    printk("  - %d second data collection cycle\n", DATA_COLLECTION_INTERVAL_S);
    printk("  - Low power mode (verified: 3.5µA)\n");
    printk("\n");
    
    /* Initialize I2C device reference */
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    if (!i2c_dev || !device_is_ready(i2c_dev)) {
        printk("ERROR: I2C device not ready\n");
    } else {
        printk("I2C device ready\n");
    }
    
    /* Initialize temperature sensor */
    ret = init_temperature_sensor();
    if (ret < 0) {
        printk("Temperature sensor initialization failed: %d\n", ret);
        printk("Continuing with power test only...\n");
    }
    
    printk("=== SW2600 Application Started Successfully ===\n");
    
    /* Main application loop */
    while (1) {
        /* Perform data collection cycle */
        data_collection_cycle();
        
        /* Countdown before sleep */
        for (int i = COUNTDOWN_DELAY_S; i > 0; i--) {
            printk("Deep sleep countdown: %d seconds remaining\n", i);
            k_sleep(K_SECONDS(1));
        }
        
        /* Enter deep sleep mode */
        enter_deep_sleep();
        
        /* Should not reach here - system will reset after sleep */
        printk("ERROR: Should not reach here!\n");
        k_sleep(K_SECONDS(10));
    }
    
    return 0;
}
