/**
 * @file adxl382_vibration.h
 * @brief ADXL382 Vibration Sensor Driver Header
 * 
 * SPI implementation for ADXL382 vibration sensor
 * Optimized for ultra-low power consumption
 */

#ifndef ADXL382_VIBRATION_H
#define ADXL382_VIBRATION_H

#include <zephyr/kernel.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/spi.h>

/* ADXL382 Configuration */
#define ADXL382_SAMPLE_COUNT    2048    // Number of samples to collect
#define ADXL382_SAMPLE_RATE     16000   // 16kHz sampling rate

/**
 * @brief Raw acceleration data structure
 */
typedef struct {
    int16_t x;  // X-axis acceleration (raw ADC value)
    int16_t y;  // Y-axis acceleration (raw ADC value)
    int16_t z;  // Z-axis acceleration (raw ADC value)
} raw_accel_data_t;

/**
 * @brief Vibration analysis result structure
 */
typedef struct {
    float accel_rms_x;          // X-axis acceleration RMS (m/s²)
    float accel_rms_y;          // Y-axis acceleration RMS (m/s²)
    float accel_rms_z;          // Z-axis acceleration RMS (m/s²)
    
    float velocity_rms_x;       // X-axis velocity RMS (mm/s)
    float velocity_rms_y;       // Y-axis velocity RMS (mm/s)
    float velocity_rms_z;       // Z-axis velocity RMS (mm/s)
    
    float displacement_pp_x;    // X-axis displacement peak-to-peak (μm)
    float displacement_pp_y;    // Y-axis displacement peak-to-peak (μm)
    float displacement_pp_z;    // Z-axis displacement peak-to-peak (μm)
    
    uint16_t main_freq_x;       // X-axis main frequency (Hz)
    uint16_t main_freq_y;       // Y-axis main frequency (Hz)
    uint16_t main_freq_z;       // Z-axis main frequency (Hz)
    
    float main_amp_x;           // X-axis main amplitude (m/s²)
    float main_amp_y;           // Y-axis main amplitude (m/s²)
    float main_amp_z;           // Z-axis main amplitude (m/s²)
} vibration_result_t;

/**
 * @brief Initialize ADXL382 vibration sensor
 * @return 0 on success, negative error code on failure
 */
int adxl382_init(void);

/**
 * @brief Collect vibration data and perform analysis
 * @param result Pointer to store vibration analysis results
 * @return 0 on success, negative error code on failure
 */
int adxl382_collect_vibration_data(vibration_result_t *result);

/**
 * @brief Power off ADXL382 sensor
 * @return 0 on success, negative error code on failure
 */
int adxl382_power_off(void);

/**
 * @brief Deinitialize ADXL382 vibration sensor
 * @return 0 on success, negative error code on failure
 */
int adxl382_deinit(void);

#endif /* ADXL382_VIBRATION_H */
