/dts-v1/;

/ {
	#address-cells = < 0x1 >;
	#size-cells = < 0x1 >;
	model = "Nordic NRF5340 DK NRF5340 Application";
	compatible = "nordic,nrf5340-dk-nrf5340-cpuapp";
	chosen {
		zephyr,entropy = &cryptocell;
		zephyr,flash-controller = &flash_controller;
		zephyr,console = &uart0;
		zephyr,shell-uart = &uart0;
		zephyr,uart-mcumgr = &uart0;
		zephyr,bt-mon-uart = &uart0;
		zephyr,bt-c2h-uart = &uart0;
		zephyr,bt-hci = &bt_hci_ipc0;
		nordic,802154-spinel-ipc = &ipc0;
		zephyr,ieee802154 = &ieee802154;
		zephyr,ipc_shm = &sram0_shared;
		zephyr,sram = &sram0_image;
		zephyr,flash = &flash0;
		zephyr,code-partition = &slot0_partition;
		zephyr,sram-secure-partition = &sram0_s;
		zephyr,sram-non-secure-partition = &sram0_ns;
		nordic,pm-ext-flash = &mx25r64;
		ncs,zigbee-timer = &timer2;
	};
	aliases {
		led0 = &led0;
		led1 = &led1;
		led2 = &led2;
		led3 = &led3;
		sw0 = &button0;
		sw1 = &button1;
		sw2 = &button2;
		sw3 = &button3;
		bootloader-led0 = &led0;
		mcuboot-button0 = &button0;
		mcuboot-led0 = &led0;
		pwm-led0 = &pwm_led0;
		watchdog0 = &wdt0;
		mic-power = &mic_power;
		adxl382-pwr-ctrl = &adxl382_pwr_ctrl;
	};
	soc {
		#address-cells = < 0x1 >;
		#size-cells = < 0x1 >;
		compatible = "nordic,nrf5340-cpuapp-qkaa", "nordic,nrf5340-cpuapp", "nordic,nrf53", "simple-bus";
		interrupt-parent = < &nvic >;
		ranges;
		nvic: interrupt-controller@e000e100 {
			#address-cells = < 0x1 >;
			compatible = "arm,v8m-nvic";
			reg = < 0xe000e100 0xc00 >;
			interrupt-controller;
			#interrupt-cells = < 0x2 >;
			arm,num-irq-priority-bits = < 0x3 >;
			phandle = < 0x1 >;
		};
		systick: timer@e000e010 {
			compatible = "arm,armv8m-systick";
			reg = < 0xe000e010 0x10 >;
			status = "disabled";
		};
		ficr: ficr@ff0000 {
			compatible = "nordic,nrf-ficr";
			reg = < 0xff0000 0x1000 >;
			#nordic,ficr-cells = < 0x1 >;
			status = "okay";
		};
		uicr: uicr@ff8000 {
			compatible = "nordic,nrf-uicr";
			reg = < 0xff8000 0x1000 >;
			status = "okay";
		};
		sram0: memory@20000000 {
			compatible = "mmio-sram";
			reg = < 0x20000000 0x80000 >;
		};
		peripheral@50000000 {
			#address-cells = < 0x1 >;
			#size-cells = < 0x1 >;
			ranges = < 0x0 0x50000000 0x10000000 >;
			dcnf: dcnf@0 {
				compatible = "nordic,nrf-dcnf";
				reg = < 0x0 0x1000 >;
				status = "okay";
			};
			oscillators: oscillator@4000 {
				compatible = "nordic,nrf-oscillators";
				reg = < 0x4000 0x1000 >;
				status = "okay";
			};
			regulators: regulator@4000 {
				compatible = "nordic,nrf53x-regulators";
				reg = < 0x4000 0x1000 >;
				status = "okay";
				#address-cells = < 0x1 >;
				#size-cells = < 0x1 >;
				vregmain: regulator@4704 {
					compatible = "nordic,nrf5x-regulator";
					reg = < 0x4704 0x1 >;
					status = "okay";
					regulator-name = "VREGMAIN";
					regulator-initial-mode = < 0x1 >;
				};
				vregradio: regulator@4904 {
					compatible = "nordic,nrf5x-regulator";
					reg = < 0x4904 0x1 >;
					status = "okay";
					regulator-name = "VREGRADIO";
					regulator-initial-mode = < 0x1 >;
				};
				vregh: regulator@4b00 {
					compatible = "nordic,nrf53x-regulator-hv";
					reg = < 0x4b00 0x44 >;
					status = "okay";
					regulator-name = "VREGH";
				};
			};
			clock: clock@5000 {
				compatible = "nordic,nrf-clock";
				reg = < 0x5000 0x1000 >;
				interrupts = < 0x5 0x1 >;
				status = "okay";
			};
			power: power@5000 {
				compatible = "nordic,nrf-power";
				reg = < 0x5000 0x1000 >;
				ranges = < 0x0 0x5000 0x1000 >;
				interrupts = < 0x5 0x1 >;
				status = "okay";
				#address-cells = < 0x1 >;
				#size-cells = < 0x1 >;
				gpregret1: gpregret1@51c {
					#address-cells = < 0x1 >;
					#size-cells = < 0x1 >;
					compatible = "nordic,nrf-gpregret";
					reg = < 0x51c 0x1 >;
					status = "okay";
				};
				gpregret2: gpregret2@520 {
					#address-cells = < 0x1 >;
					#size-cells = < 0x1 >;
					compatible = "nordic,nrf-gpregret";
					reg = < 0x520 0x1 >;
					status = "okay";
				};
			};
			reset: reset-controller@5000 {
				compatible = "nordic,nrf-reset";
				reg = < 0x5000 0x1000 >;
				status = "okay";
			};
			ctrlap: ctrlap@6000 {
				compatible = "nordic,nrf-ctrlapperi";
				reg = < 0x6000 0x1000 >;
				status = "okay";
			};
			i2c0: i2c@8000 {
				compatible = "nordic,nrf-twim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0x8000 0x1000 >;
				interrupts = < 0x8 0x1 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
				zephyr,pm-device-runtime-auto;
			};
			spi0: spi@8000 {
				compatible = "nordic,nrf-spim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0x8000 0x1000 >;
				interrupts = < 0x8 0x1 >;
				max-frequency = < 0x7a1200 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
			};
			uart0: uart@8000 {
				compatible = "nordic,nrf-uarte";
				reg = < 0x8000 0x1000 >;
				interrupts = < 0x8 0x1 >;
				status = "okay";
				current-speed = < 0x1c200 >;
				pinctrl-0 = < &uart0_default >;
				pinctrl-1 = < &uart0_sleep >;
				pinctrl-names = "default", "sleep";
			};
			i2c1: arduino_i2c: i2c@9000 {
				compatible = "nordic,nrf-twim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0x9000 0x1000 >;
				interrupts = < 0x9 0x1 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "okay";
				zephyr,pm-device-runtime-auto;
				pinctrl-0 = < &i2c1_default >;
				pinctrl-1 = < &i2c1_sleep >;
				pinctrl-names = "default", "sleep";
				clock-frequency = < 0x186a0 >;
				m117: m117@48 {
					compatible = "i2c-device";
					reg = < 0x48 >;
					label = "M117";
				};
			};
			spi1: spi@9000 {
				compatible = "nordic,nrf-spim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0x9000 0x1000 >;
				interrupts = < 0x9 0x1 >;
				max-frequency = < 0x7a1200 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
			};
			uart1: arduino_serial: uart@9000 {
				compatible = "nordic,nrf-uarte";
				reg = < 0x9000 0x1000 >;
				interrupts = < 0x9 0x1 >;
				status = "disabled";
				current-speed = < 0x1c200 >;
				pinctrl-0 = < &uart1_default >;
				pinctrl-1 = < &uart1_sleep >;
				pinctrl-names = "default", "sleep";
			};
			spi4: arduino_spi: spi@a000 {
				compatible = "nordic,nrf-spim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0xa000 0x1000 >;
				interrupts = < 0xa 0x1 >;
				max-frequency = < 0x1e84800 >;
				easydma-maxcnt-bits = < 0x10 >;
				rx-delay-supported;
				rx-delay = < 0x2 >;
				status = "disabled";
				cs-gpios = < &arduino_header 0x10 0x1 >;
				pinctrl-0 = < &spi4_default >;
				pinctrl-1 = < &spi4_sleep >;
				pinctrl-names = "default", "sleep";
			};
			i2c2: i2c@b000 {
				compatible = "nordic,nrf-twim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0xb000 0x1000 >;
				interrupts = < 0xb 0x1 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
				zephyr,pm-device-runtime-auto;
			};
			spi2: spi@b000 {
				compatible = "nordic,nrf-spim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0xb000 0x1000 >;
				interrupts = < 0xb 0x1 >;
				max-frequency = < 0x7a1200 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
			};
			uart2: uart@b000 {
				compatible = "nordic,nrf-uarte";
				reg = < 0xb000 0x1000 >;
				interrupts = < 0xb 0x1 >;
				status = "disabled";
			};
			i2c3: i2c@c000 {
				compatible = "nordic,nrf-twim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0xc000 0x1000 >;
				interrupts = < 0xc 0x1 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
				zephyr,pm-device-runtime-auto;
			};
			spi3: spi@c000 {
				compatible = "nordic,nrf-spim";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0xc000 0x1000 >;
				interrupts = < 0xc 0x1 >;
				max-frequency = < 0x7a1200 >;
				easydma-maxcnt-bits = < 0x10 >;
				status = "disabled";
			};
			uart3: uart@c000 {
				compatible = "nordic,nrf-uarte";
				reg = < 0xc000 0x1000 >;
				interrupts = < 0xc 0x1 >;
				status = "disabled";
			};
			adc: adc@e000 {
				compatible = "nordic,nrf-saadc";
				reg = < 0xe000 0x1000 >;
				interrupts = < 0xe 0x1 >;
				status = "disabled";
				#io-channel-cells = < 0x1 >;
				phandle = < 0x18 >;
			};
			timer0: timer@f000 {
				compatible = "nordic,nrf-timer";
				status = "disabled";
				reg = < 0xf000 0x1000 >;
				cc-num = < 0x6 >;
				max-bit-width = < 0x20 >;
				interrupts = < 0xf 0x1 >;
				prescaler = < 0x0 >;
			};
			timer1: timer@10000 {
				compatible = "nordic,nrf-timer";
				status = "disabled";
				reg = < 0x10000 0x1000 >;
				cc-num = < 0x6 >;
				max-bit-width = < 0x20 >;
				interrupts = < 0x10 0x1 >;
				prescaler = < 0x0 >;
				phandle = < 0x12 >;
			};
			timer2: timer@11000 {
				compatible = "nordic,nrf-timer";
				status = "disabled";
				reg = < 0x11000 0x1000 >;
				cc-num = < 0x6 >;
				max-bit-width = < 0x20 >;
				interrupts = < 0x11 0x1 >;
				prescaler = < 0x0 >;
			};
			rtc0: rtc@14000 {
				compatible = "nordic,nrf-rtc";
				reg = < 0x14000 0x1000 >;
				cc-num = < 0x4 >;
				interrupts = < 0x14 0x1 >;
				status = "disabled";
				clock-frequency = < 0x8000 >;
				prescaler = < 0x1 >;
			};
			rtc1: rtc@15000 {
				compatible = "nordic,nrf-rtc";
				reg = < 0x15000 0x1000 >;
				cc-num = < 0x4 >;
				interrupts = < 0x15 0x1 >;
				status = "disabled";
				clock-frequency = < 0x8000 >;
				prescaler = < 0x1 >;
			};
			dppic0: dppic: dppic@17000 {
				compatible = "nordic,nrf-dppic";
				reg = < 0x17000 0x1000 >;
				status = "okay";
			};
			wdt: wdt0: watchdog@18000 {
				compatible = "nordic,nrf-wdt";
				reg = < 0x18000 0x1000 >;
				interrupts = < 0x18 0x1 >;
				status = "disabled";
			};
			wdt1: watchdog@19000 {
				compatible = "nordic,nrf-wdt";
				reg = < 0x19000 0x1000 >;
				interrupts = < 0x19 0x1 >;
				status = "disabled";
			};
			comp: comparator@1a000 {
				compatible = "nordic,nrf-comp";
				reg = < 0x1a000 0x1000 >;
				interrupts = < 0x1a 0x1 >;
				status = "disabled";
			};
			egu0: egu@1b000 {
				compatible = "nordic,nrf-egu";
				reg = < 0x1b000 0x1000 >;
				interrupts = < 0x1b 0x1 >;
				status = "okay";
			};
			egu1: egu@1c000 {
				compatible = "nordic,nrf-egu";
				reg = < 0x1c000 0x1000 >;
				interrupts = < 0x1c 0x1 >;
				status = "okay";
			};
			egu2: egu@1d000 {
				compatible = "nordic,nrf-egu";
				reg = < 0x1d000 0x1000 >;
				interrupts = < 0x1d 0x1 >;
				status = "okay";
			};
			egu3: egu@1e000 {
				compatible = "nordic,nrf-egu";
				reg = < 0x1e000 0x1000 >;
				interrupts = < 0x1e 0x1 >;
				status = "okay";
			};
			egu4: egu@1f000 {
				compatible = "nordic,nrf-egu";
				reg = < 0x1f000 0x1000 >;
				interrupts = < 0x1f 0x1 >;
				status = "okay";
			};
			egu5: egu@20000 {
				compatible = "nordic,nrf-egu";
				reg = < 0x20000 0x1000 >;
				interrupts = < 0x20 0x1 >;
				status = "okay";
			};
			pwm0: pwm@21000 {
				compatible = "nordic,nrf-pwm";
				reg = < 0x21000 0x1000 >;
				interrupts = < 0x21 0x1 >;
				status = "disabled";
				#pwm-cells = < 0x3 >;
				pinctrl-0 = < &pwm0_default >;
				pinctrl-1 = < &pwm0_sleep >;
				pinctrl-names = "default", "sleep";
				phandle = < 0x17 >;
			};
			pwm1: pwm@22000 {
				compatible = "nordic,nrf-pwm";
				reg = < 0x22000 0x1000 >;
				interrupts = < 0x22 0x1 >;
				status = "disabled";
				#pwm-cells = < 0x3 >;
			};
			pwm2: pwm@23000 {
				compatible = "nordic,nrf-pwm";
				reg = < 0x23000 0x1000 >;
				interrupts = < 0x23 0x1 >;
				status = "disabled";
				#pwm-cells = < 0x3 >;
			};
			pwm3: pwm@24000 {
				compatible = "nordic,nrf-pwm";
				reg = < 0x24000 0x1000 >;
				interrupts = < 0x24 0x1 >;
				status = "disabled";
				#pwm-cells = < 0x3 >;
			};
			pdm0: pdm@26000 {
				compatible = "nordic,nrf-pdm";
				reg = < 0x26000 0x1000 >;
				interrupts = < 0x26 0x1 >;
				status = "disabled";
			};
			i2s0: i2s@28000 {
				compatible = "nordic,nrf-i2s";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0x28000 0x1000 >;
				interrupts = < 0x28 0x1 >;
				status = "okay";
				pinctrl-0 = < &i2s0_default >;
				pinctrl-1 = < &i2s0_sleep >;
				pinctrl-names = "default", "sleep";
				clock-source = "PCLK32M_HFXO";
			};
			mbox: ipc: mbox@2a000 {
				compatible = "nordic,mbox-nrf-ipc", "nordic,nrf-ipc";
				reg = < 0x2a000 0x1000 >;
				tx-mask = < 0xffff >;
				rx-mask = < 0xffff >;
				interrupts = < 0x2a 0x1 >;
				#mbox-cells = < 0x1 >;
				status = "okay";
				phandle = < 0x14 >;
			};
			qspi: qspi@2b000 {
				compatible = "nordic,nrf-qspi";
				#address-cells = < 0x1 >;
				#size-cells = < 0x0 >;
				reg = < 0x2b000 0x1000 >, < 0x10000000 0x10000000 >;
				reg-names = "qspi", "qspi_mm";
				interrupts = < 0x2b 0x1 >;
				status = "disabled";
				pinctrl-0 = < &qspi_default >;
				pinctrl-1 = < &qspi_sleep >;
				pinctrl-names = "default", "sleep";
				mx25r64: mx25r6435f@0 {
					compatible = "nordic,qspi-nor";
					reg = < 0x0 >;
					writeoc = "pp4io";
					readoc = "read4io";
					sck-frequency = < 0x7a1200 >;
					jedec-id = [ C2 28 17 ];
					sfdp-bfp = [ E5 20 F1 FF FF FF FF 03 44 EB 08 6B 08 3B 04 BB EE FF FF FF FF FF 00 FF FF FF 00 FF 0C 20 0F 52 10 D8 00 FF 23 72 F5 00 82 ED 04 CC 44 83 68 44 30 B0 30 B0 F7 C4 D5 5C 00 BE 29 FF F0 D0 FF FF ];
					size = < 0x4000000 >;
					has-dpd;
					t-enter-dpd = < 0x2710 >;
					t-exit-dpd = < 0x88b8 >;
				};
			};
			nfct: nfct@2d000 {
				compatible = "nordic,nrf-nfct";
				reg = < 0x2d000 0x1000 >;
				interrupts = < 0x2d 0x1 >;
				status = "disabled";
			};
			mutex: mutex@30000 {
				compatible = "nordic,nrf-mutex";
				reg = < 0x30000 0x1000 >;
				status = "okay";
			};
			qdec0: qdec@33000 {
				compatible = "nordic,nrf-qdec";
				reg = < 0x33000 0x1000 >;
				interrupts = < 0x33 0x1 >;
				status = "disabled";
			};
			qdec1: qdec@34000 {
				compatible = "nordic,nrf-qdec";
				reg = < 0x34000 0x1000 >;
				interrupts = < 0x34 0x1 >;
				status = "disabled";
			};
			usbd: zephyr_udc0: usbd@36000 {
				compatible = "nordic,nrf-usbd";
				reg = < 0x36000 0x1000 >;
				interrupts = < 0x36 0x1 >;
				num-bidir-endpoints = < 0x1 >;
				num-in-endpoints = < 0x7 >;
				num-out-endpoints = < 0x7 >;
				num-isoin-endpoints = < 0x1 >;
				num-isoout-endpoints = < 0x1 >;
				status = "disabled";
			};
			usbreg: regulator@37000 {
				compatible = "nordic,nrf-usbreg";
				reg = < 0x37000 0x1000 >;
				interrupts = < 0x37 0x1 >;
				status = "okay";
			};
			flash_controller: flash-controller@39000 {
				compatible = "nordic,nrf53-flash-controller";
				reg = < 0x39000 0x1000 >;
				partial-erase;
				#address-cells = < 0x1 >;
				#size-cells = < 0x1 >;
				flash0: flash@0 {
					compatible = "soc-nv-flash";
					erase-block-size = < 0x1000 >;
					write-block-size = < 0x4 >;
					reg = < 0x0 0x100000 >;
					partitions {
						compatible = "fixed-partitions";
						#address-cells = < 0x1 >;
						#size-cells = < 0x1 >;
						boot_partition: partition@0 {
							label = "mcuboot";
							reg = < 0x0 0x10000 >;
						};
						slot0_partition: partition@10000 {
							label = "image-0";
							reg = < 0x10000 0x40000 >;
						};
						slot0_ns_partition: partition@50000 {
							label = "image-0-nonsecure";
							reg = < 0x50000 0x30000 >;
						};
						slot1_partition: partition@80000 {
							label = "image-1";
							reg = < 0x80000 0x40000 >;
						};
						slot1_ns_partition: partition@c0000 {
							label = "image-1-nonsecure";
							reg = < 0xc0000 0x30000 >;
						};
						tfm_ps_partition: partition@f0000 {
							label = "tfm-ps";
							reg = < 0xf0000 0x4000 >;
						};
						tfm_its_partition: partition@f4000 {
							label = "tfm-its";
							reg = < 0xf4000 0x2000 >;
						};
						tfm_otp_partition: partition@f6000 {
							label = "tfm-otp";
							reg = < 0xf6000 0x2000 >;
						};
						storage_partition: partition@f8000 {
							label = "storage";
							reg = < 0xf8000 0x8000 >;
						};
					};
				};
			};
			kmu: kmu@39000 {
				compatible = "nordic,nrf-kmu";
				reg = < 0x39000 0x1000 >;
				interrupts = < 0x39 0x1 >;
				status = "okay";
			};
			vmc: vmc@81000 {
				compatible = "nordic,nrf-vmc";
				reg = < 0x81000 0x1000 >;
				status = "okay";
			};
			gpio0: gpio@842500 {
				compatible = "nordic,nrf-gpio";
				gpio-controller;
				reg = < 0x842500 0x300 >;
				#gpio-cells = < 0x2 >;
				status = "okay";
				port = < 0x0 >;
				gpiote-instance = < &gpiote >;
				phandle = < 0x15 >;
			};
			gpio1: gpio@842800 {
				compatible = "nordic,nrf-gpio";
				gpio-controller;
				reg = < 0x842800 0x300 >;
				#gpio-cells = < 0x2 >;
				ngpios = < 0x10 >;
				status = "okay";
				port = < 0x1 >;
				gpiote-instance = < &gpiote >;
				phandle = < 0x16 >;
			};
			ieee802154: ieee802154 {
				compatible = "nordic,nrf-ieee802154";
				status = "okay";
			};
		};
		spu: spu@50003000 {
			compatible = "nordic,nrf-spu";
			reg = < 0x50003000 0x1000 >;
			interrupts = < 0x3 0x1 >;
			status = "okay";
		};
		gpiote: gpiote0: gpiote@5000d000 {
			compatible = "nordic,nrf-gpiote";
			reg = < 0x5000d000 0x1000 >;
			interrupts = < 0xd 0x5 >;
			status = "okay";
			instance = < 0x0 >;
			phandle = < 0x11 >;
		};
		gpiote1: gpiote@4002f000 {
			compatible = "nordic,nrf-gpiote";
			reg = < 0x4002f000 0x1000 >;
			interrupts = < 0x2f 0x5 >;
			status = "disabled";
			instance = < 0x1 >;
		};
		cryptocell: crypto@50844000 {
			compatible = "nordic,cryptocell", "arm,cryptocell-312";
			reg = < 0x50844000 0x1000 >, < 0x50845000 0x1000 >;
			reg-names = "wrapper", "core";
			interrupts = < 0x44 0x1 >;
			status = "okay";
		};
	};
	pinctrl: pin-controller {
		compatible = "nordic,nrf-pinctrl";
		i2c1_default: i2c1_default {
			phandle = < 0x4 >;
			group1 {
				psels = < 0xb000028 >, < 0xc000018 >;
			};
		};
		i2c1_sleep: i2c1_sleep {
			phandle = < 0x5 >;
			group1 {
				psels = < 0xb000028 >, < 0xc000018 >;
				low-power-enable;
			};
		};
		uart0_default: uart0_default {
			phandle = < 0x2 >;
			group1 {
				psels = < 0x2c >;
			};
			group2 {
				psels = < 0x100002e >;
				bias-pull-up;
			};
		};
		uart0_sleep: uart0_sleep {
			phandle = < 0x3 >;
			group1 {
				psels = < 0x2c >, < 0x100002e >;
				low-power-enable;
			};
		};
		pwm0_default: pwm0_default {
			phandle = < 0xb >;
			group1 {
				psels = < 0x1600001c >;
			};
		};
		pwm0_sleep: pwm0_sleep {
			phandle = < 0xc >;
			group1 {
				psels = < 0x1600001c >;
				low-power-enable;
			};
		};
		qspi_default: qspi_default {
			phandle = < 0xf >;
			group1 {
				psels = < 0x1d000011 >, < 0x1f00000d >, < 0x2000000e >, < 0x2100000f >, < 0x22000010 >, < 0x1e000012 >;
				nordic,drive-mode = < 0x3 >;
			};
		};
		qspi_sleep: qspi_sleep {
			phandle = < 0x10 >;
			group1 {
				psels = < 0x1d000011 >, < 0x1f00000d >, < 0x2000000e >, < 0x2100000f >, < 0x22000010 >;
				low-power-enable;
			};
			group2 {
				psels = < 0x1e000012 >;
				low-power-enable;
				bias-pull-up;
			};
		};
		uart1_default: uart1_default {
			phandle = < 0x6 >;
			group1 {
				psels = < 0x21 >, < 0x200000b >;
			};
			group2 {
				psels = < 0x1000020 >, < 0x300000a >;
				bias-pull-up;
			};
		};
		uart1_sleep: uart1_sleep {
			phandle = < 0x7 >;
			group1 {
				psels = < 0x21 >, < 0x1000020 >, < 0x200000b >, < 0x300000a >;
				low-power-enable;
			};
		};
		spi4_default: spi4_default {
			phandle = < 0x9 >;
			group1 {
				psels = < 0x400002f >, < 0x600002e >, < 0x500002d >;
			};
		};
		spi4_sleep: spi4_sleep {
			phandle = < 0xa >;
			group1 {
				psels = < 0x400002f >, < 0x600002e >, < 0x500002d >;
				low-power-enable;
			};
		};
		i2s0_default: i2s0_default {
			phandle = < 0xd >;
			group1 {
				psels = < 0xd000026 >, < 0xf000013 >, < 0x11000015 >;
				bias-pull-down;
				nordic,drive-mode = < 0x3 >;
			};
		};
		i2s0_sleep: i2s0_sleep {
			phandle = < 0xe >;
			group1 {
				psels = < 0xd000026 >, < 0xf000013 >, < 0x11000015 >;
				low-power-enable;
			};
		};
		spi2_default: spi2_default {
			group1 {
				psels = < 0x4000017 >, < 0x5000025 >, < 0x6000027 >;
			};
		};
		spi2_sleep: spi2_sleep {
			group1 {
				psels = < 0x4000017 >, < 0x5000025 >, < 0x6000027 >;
				low-power-enable;
			};
		};
	};
	rng_hci: entropy_bt_hci {
		compatible = "zephyr,bt-hci-entropy";
		status = "okay";
	};
	sw_pwm: sw-pwm {
		compatible = "nordic,nrf-sw-pwm";
		status = "disabled";
		generator = < &timer1 >;
		clock-prescaler = < 0x0 >;
		#pwm-cells = < 0x3 >;
	};
	cpus {
		#address-cells = < 0x1 >;
		#size-cells = < 0x0 >;
		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-m33f";
			reg = < 0x0 >;
			#address-cells = < 0x1 >;
			#size-cells = < 0x1 >;
			itm: itm@e0000000 {
				compatible = "arm,armv8m-itm";
				reg = < 0xe0000000 0x1000 >;
				swo-ref-frequency = < 0x3d09000 >;
			};
			mpu: mpu@e000ed90 {
				compatible = "arm,armv8m-mpu";
				reg = < 0xe000ed90 0x40 >;
			};
		};
	};
	ipc {
		ipc0: ipc0 {
			compatible = "zephyr,ipc-openamp-static-vrings";
			memory-region = < &sram0_shared >;
			mboxes = < &mbox 0x0 >, < &mbox 0x1 >;
			mbox-names = "tx", "rx";
			role = "host";
			status = "okay";
			bt_hci_ipc0: bt_hci_ipc0 {
				compatible = "zephyr,bt-hci-ipc";
				status = "okay";
			};
		};
	};
	leds {
		compatible = "gpio-leds";
		led0: led_0 {
			gpios = < &gpio0 0x5 0x1 >;
			label = "Green LED 0";
		};
		led1: led_1 {
			gpios = < &gpio0 0x6 0x1 >;
			label = "Green LED 1";
		};
		led2: led_2 {
			gpios = < &gpio0 0x1e 0x1 >;
			label = "Green LED 2";
			status = "disabled";
		};
		led3: led_3 {
			gpios = < &gpio0 0x1f 0x1 >;
			label = "Green LED 3";
			status = "disabled";
		};
	};
	buttons {
		compatible = "gpio-keys";
		button0: button_0 {
			gpios = < &gpio0 0x7 0x11 >;
			label = "Push button 1";
			zephyr,code = < 0xb >;
		};
		button1: button_1 {
			gpios = < &gpio0 0x18 0x11 >;
			label = "Push button 2";
			zephyr,code = < 0x2 >;
			status = "disabled";
		};
		button2: button_2 {
			gpios = < &gpio0 0x8 0x11 >;
			label = "Push button 3";
			zephyr,code = < 0x3 >;
			status = "disabled";
		};
		button3: button_3 {
			gpios = < &gpio0 0x9 0x11 >;
			label = "Push button 4";
			zephyr,code = < 0x4 >;
			status = "disabled";
		};
	};
	arduino_header: connector {
		compatible = "arduino-header-r3";
		#gpio-cells = < 0x2 >;
		gpio-map-mask = < 0xffffffff 0xffffffc0 >;
		gpio-map-pass-thru = < 0x0 0x3f >;
		gpio-map = < 0x0 0x0 &gpio0 0x4 0x0 >, < 0x1 0x0 &gpio0 0x5 0x0 >, < 0x2 0x0 &gpio0 0x6 0x0 >, < 0x3 0x0 &gpio0 0x7 0x0 >, < 0x4 0x0 &gpio0 0x19 0x0 >, < 0x5 0x0 &gpio0 0x1a 0x0 >, < 0x6 0x0 &gpio1 0x0 0x0 >, < 0x7 0x0 &gpio1 0x1 0x0 >, < 0x8 0x0 &gpio1 0x4 0x0 >, < 0x9 0x0 &gpio1 0x5 0x0 >, < 0xa 0x0 &gpio1 0x6 0x0 >, < 0xb 0x0 &gpio1 0x7 0x0 >, < 0xc 0x0 &gpio1 0x8 0x0 >, < 0xd 0x0 &gpio1 0x9 0x0 >, < 0xe 0x0 &gpio1 0xa 0x0 >, < 0xf 0x0 &gpio1 0xb 0x0 >, < 0x10 0x0 &gpio1 0xc 0x0 >, < 0x11 0x0 &gpio1 0xd 0x0 >, < 0x12 0x0 &gpio1 0xe 0x0 >, < 0x13 0x0 &gpio1 0xf 0x0 >, < 0x14 0x0 &gpio1 0x2 0x0 >, < 0x15 0x0 &gpio1 0x3 0x0 >;
		phandle = < 0x8 >;
	};
	pwmleds {
		compatible = "pwm-leds";
		pwm_led0: pwm_led_0 {
			pwms = < &pwm0 0x0 0x1312d00 0x1 >;
		};
	};
	arduino_adc: analog-connector {
		compatible = "arduino,uno-adc";
		#io-channel-cells = < 0x1 >;
		io-channel-map = < 0x0 &adc 0x0 >, < 0x1 &adc 0x1 >, < 0x2 &adc 0x2 >, < 0x3 &adc 0x3 >, < 0x4 &adc 0x4 >, < 0x5 &adc 0x5 >;
	};
	gpio_fwd: nrf-gpio-forwarder {
		compatible = "nordic,nrf-gpio-forwarder";
		status = "okay";
		uart {
			gpios = < &gpio1 0x1 0x0 >, < &gpio1 0x0 0x0 >, < &gpio0 0xb 0x0 >, < &gpio0 0xa 0x0 >;
		};
		nrf21540-spi-if {
			gpios = < &gpio1 0x3 0x0 >, < &gpio0 0x1d 0x0 >, < &gpio0 0x14 0x0 >, < &gpio0 0x1c 0x0 >;
		};
		nrf21540-gpio-if {
			gpios = < &gpio0 0x1f 0x0 >, < &gpio0 0x1e 0x0 >, < &gpio1 0xa 0x0 >, < &gpio1 0xb 0x0 >;
		};
	};
	reserved-memory {
		#address-cells = < 0x1 >;
		#size-cells = < 0x1 >;
		ranges;
		sram0_image: image@20000000 {
			reg = < 0x20000000 0x70000 >;
		};
		sram0_s: image_s@20000000 {
			reg = < 0x20000000 0x40000 >;
		};
		sram0_ns: image_ns@20040000 {
			reg = < 0x20040000 0x40000 >;
		};
		sram0_ns_app: image_ns_app@20040000 {
			reg = < 0x20040000 0x30000 >;
		};
		sram0_shared: memory@20070000 {
			reg = < 0x20070000 0x10000 >;
			phandle = < 0x13 >;
		};
	};
	mic_power: mic_power {
		compatible = "regulator-fixed";
		label = "MIC_VDD";
		regulator-name = "mic-vdd";
		enable-gpios = < &gpio1 0x4 0x0 >;
		startup-delay-us = < 0x186a0 >;
		regulator-min-microvolt = < 0x1b7740 >;
		regulator-max-microvolt = < 0x1b7740 >;
		status = "okay";
	};
	adxl382_pwr_ctrl: adxl382-pwr-ctrl {
		compatible = "regulator-fixed";
		regulator-name = "tck106ag";
		enable-gpios = < &gpio0 0x1a 0x0 >;
		regulator-boot-on;
		regulator-always-on;
		startup-delay-us = < 0x2710 >;
		regulator-min-microvolt = < 0x325aa0 >;
		regulator-max-microvolt = < 0x325aa0 >;
		status = "okay";
	};
};
