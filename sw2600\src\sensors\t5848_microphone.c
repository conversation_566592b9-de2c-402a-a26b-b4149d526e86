/**
 * @file t5848_microphone.c
 * @brief T5848 Microphone Sensor Driver Implementation
 * 
 * I2S implementation for T5848 microphone sensor
 * Optimized for ultra-low power consumption
 */

#include "t5848_microphone.h"
#include <zephyr/logging/log.h>
#include <math.h>
#include <stdlib.h>

LOG_MODULE_REGISTER(t5848_mic, LOG_LEVEL_INF);

/* Microphone Configuration */
#define SAMPLE_RATE             16000   // 16kHz sampling rate
#define SAMPLES_PER_BLOCK       400     // Samples per I2S block
#define NUM_BLOCKS_TO_COLLECT   20      // Number of blocks to collect
#define NUM_DBFS_VALUES_TO_TRIM 2       // Number of outliers to trim
#define I2S_BLOCK_SIZE          (SAMPLES_PER_BLOCK * 4) // 4 bytes per sample (stereo 16-bit)
#define AUDIO_BUF_COUNT         4       // Number of audio buffers

/* Microphone power control */
static const struct gpio_dt_spec mic_vdd = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 4,   // P1.04 - MIC-VDD pin
    .dt_flags = GPIO_ACTIVE_HIGH
};

/* I2S device */
static const struct device *i2s_dev;

/* Memory slab for I2S buffers */
K_MEM_SLAB_DEFINE(mic_slab, I2S_BLOCK_SIZE, AUDIO_BUF_COUNT, 4);

/* Static buffers for audio processing */
static int16_t audio_processing_buffer[SAMPLES_PER_BLOCK];
static float dbfs_processing_buffer[NUM_BLOCKS_TO_COLLECT];

/* Static function prototypes */
static int microphone_power_on(void);
static int microphone_power_off(void);
static float calculate_block_dbfs(const int16_t *samples, uint32_t count);
static int float_compare(const void *a, const void *b);

/**
 * @brief Power on microphone
 */
static int microphone_power_on(void)
{
    int ret;

    if (!gpio_is_ready_dt(&mic_vdd)) {
        LOG_ERR("Microphone VDD GPIO not ready");
        return -ENODEV;
    }

    ret = gpio_pin_configure_dt(&mic_vdd, GPIO_OUTPUT_ACTIVE);
    if (ret) {
        LOG_ERR("Failed to configure microphone VDD pin: %d", ret);
        return ret;
    }

    gpio_pin_set_dt(&mic_vdd, 1);
    LOG_DBG("Microphone powered on");
    return 0;
}

/**
 * @brief Power off microphone
 */
static int microphone_power_off(void)
{
    gpio_pin_set_dt(&mic_vdd, 0);
    LOG_DBG("Microphone powered off");
    return 0;
}

/**
 * @brief Calculate dBFS for a block of samples
 */
static float calculate_block_dbfs(const int16_t *samples, uint32_t count)
{
    if (!samples || count == 0) {
        return -96.0f; // Minimum dBFS
    }

    float sum_squares = 0.0f;
    for (uint32_t i = 0; i < count; i++) {
        float sample = (float)samples[i] / 32768.0f; // Normalize to [-1, 1]
        sum_squares += sample * sample;
    }

    float rms = sqrtf(sum_squares / count);
    if (rms < 1e-10f) {
        return -96.0f; // Minimum dBFS
    }

    float dbfs = 20.0f * log10f(rms);
    
    // Convert dBFS to approximate dB SPL (calibration dependent)
    // Typical conversion: dB SPL = dBFS + offset
    float db_spl = dbfs + 94.0f; // Approximate calibration offset
    
    return db_spl;
}

/**
 * @brief Compare function for qsort
 */
static int float_compare(const void *a, const void *b)
{
    float fa = *(const float*)a;
    float fb = *(const float*)b;
    return (fa > fb) - (fa < fb);
}

/**
 * @brief Initialize T5848 microphone sensor
 */
int t5848_init(void)
{
    LOG_INF("Initializing T5848 microphone sensor...");

    /* Get I2S device */
    i2s_dev = DEVICE_DT_GET(DT_NODELABEL(i2s0));
    if (!device_is_ready(i2s_dev)) {
        LOG_ERR("I2S device not ready");
        return -ENODEV;
    }

    LOG_INF("T5848 microphone sensor initialized");
    return 0;
}

/**
 * @brief Read sound level from T5848 microphone
 */
int t5848_read_sound_level(float *sound_db)
{
    int ret;
    struct i2s_config i2s_cfg;

    if (!sound_db) {
        return -EINVAL;
    }

    LOG_DBG("Starting T5848 sound level measurement...");

    /* Power on microphone */
    ret = microphone_power_on();
    if (ret) {
        LOG_ERR("Failed to power on microphone: %d", ret);
        return ret;
    }

    /* Configure I2S */
    i2s_cfg.word_size = 16;
    i2s_cfg.channels = 2;
    i2s_cfg.format = I2S_FMT_DATA_FORMAT_I2S;
    i2s_cfg.options = I2S_OPT_BIT_CLK_MASTER | I2S_OPT_FRAME_CLK_MASTER;
    i2s_cfg.frame_clk_freq = SAMPLE_RATE;
    i2s_cfg.mem_slab = &mic_slab;
    i2s_cfg.block_size = I2S_BLOCK_SIZE;
    i2s_cfg.timeout = 1000;

    ret = i2s_configure(i2s_dev, I2S_DIR_RX, &i2s_cfg);
    if (ret) {
        LOG_ERR("Failed to configure I2S: %d", ret);
        microphone_power_off();
        return ret;
    }

    /* Start I2S reception */
    ret = i2s_trigger(i2s_dev, I2S_DIR_RX, I2S_TRIGGER_START);
    if (ret) {
        LOG_ERR("Failed to start I2S: %d", ret);
        microphone_power_off();
        return ret;
    }

    LOG_DBG("Waiting for microphone to stabilize...");
    k_msleep(100); // Wait for microphone to stabilize

    /* Collect audio blocks */
    uint32_t collected_count = 0;

    LOG_DBG("Collecting %d audio blocks...", NUM_BLOCKS_TO_COLLECT);
    for (int i = 0; i < NUM_BLOCKS_TO_COLLECT; i++) {
        void *rx_block;
        size_t size;
        ret = i2s_read(i2s_dev, &rx_block, &size);
        if (ret < 0) {
            LOG_ERR("Failed to read audio block: %d", ret);
            break; 
        }

        int16_t *interleaved_samples = (int16_t *)rx_block;
        uint32_t sample_pairs = size / 4;
        
        /* Use static buffer instead of stack allocation */
        uint32_t samples_to_copy = (sample_pairs < SAMPLES_PER_BLOCK) ? sample_pairs : SAMPLES_PER_BLOCK;

        for (uint32_t j = 0; j < samples_to_copy; j++) {
            audio_processing_buffer[j] = interleaved_samples[j * 2 + 1]; // Extract right channel
        }
        
        dbfs_processing_buffer[collected_count++] = calculate_block_dbfs(audio_processing_buffer, samples_to_copy);
        k_mem_slab_free(&mic_slab, rx_block);
    }

    /* Stop I2S */
    i2s_trigger(i2s_dev, I2S_DIR_RX, I2S_TRIGGER_STOP);

    /* Power off microphone */
    microphone_power_off();

    if (collected_count == 0) {
        LOG_ERR("No audio blocks collected");
        return -EIO;
    }

    LOG_DBG("Processing %u collected dBFS values...", collected_count);

    /* Sort the dBFS values to trim outliers */
    qsort(dbfs_processing_buffer, collected_count, sizeof(float), float_compare);

    /* Average the trimmed values */
    float total_dbfs = 0.0f;
    uint32_t values_to_average = collected_count - (NUM_DBFS_VALUES_TO_TRIM * 2);
    for (uint32_t i = NUM_DBFS_VALUES_TO_TRIM; i < collected_count - NUM_DBFS_VALUES_TO_TRIM; i++) {
        total_dbfs += dbfs_processing_buffer[i];
    }

    *sound_db = total_dbfs / values_to_average;

    LOG_DBG("Sound level: %.2f dB", (double)*sound_db);
    return 0;
}

/**
 * @brief Deinitialize T5848 microphone sensor
 */
int t5848_deinit(void)
{
    /* Power off microphone */
    microphone_power_off();
    
    LOG_DBG("T5848 microphone sensor deinitialized");
    return 0;
}
