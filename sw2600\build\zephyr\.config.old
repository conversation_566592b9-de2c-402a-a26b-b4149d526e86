
#
# Zephyr Kernel
#
# CONFIG_INPUT is not set
# CONFIG_WIFI is not set
# CONFIG_MIPI_DSI is not set
CONFIG_SERIAL=y
# CONFIG_UART_INTERRUPT_DRIVEN is not set
# CONFIG_UART_ASYNC_API is not set
# CONFIG_I2C is not set
# CONFIG_SPI is not set
# CONFIG_MODEM is not set
CONFIG_FLASH_LOAD_SIZE=0
CONFIG_SRAM_SIZE=448
CONFIG_FLASH_LOAD_OFFSET=0
CONFIG_NUM_IRQS=69
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=32768
CONFIG_FLASH_SIZE=1024
CONFIG_FLASH_BASE_ADDRESS=0x0
CONFIG_MP_MAX_NUM_CPUS=1
CONFIG_SOC_RESET_HOOK=y
CONFIG_SYS_CLOCK_TICKS_PER_SEC=32768
CONFIG_ROM_START_OFFSET=0
CONFIG_PINCTRL=y
CONFIG_BUILD_OUTPUT_BIN=y
# CONFIG_BUILD_NO_GAP_FILL is not set
CONFIG_XIP=y
CONFIG_MAIN_STACK_SIZE=1024
CONFIG_IDLE_STACK_SIZE=320
CONFIG_HAS_FLASH_LOAD_OFFSET=y
# CONFIG_SRAM_VECTOR_TABLE is not set
CONFIG_CPU_HAS_ARM_MPU=y
# CONFIG_COUNTER is not set
# CONFIG_SHARED_INTERRUPTS is not set
CONFIG_PM_DEVICE=y
CONFIG_TICKLESS_KERNEL=y
CONFIG_FPU=y
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=1024
CONFIG_CLOCK_CONTROL=y
CONFIG_CLOCK_CONTROL_INIT_PRIORITY=30
# CONFIG_USE_DT_CODE_PARTITION is not set
# CONFIG_MULTI_LEVEL_INTERRUPTS is not set
CONFIG_GEN_IRQ_VECTOR_TABLE=y
# CONFIG_DYNAMIC_INTERRUPTS is not set
CONFIG_GEN_ISR_TABLES=y
# CONFIG_INIT_STACKS is not set
# CONFIG_REGULATOR is not set
CONFIG_PM_DEVICE_SYSTEM_MANAGED=y
CONFIG_HW_STACK_PROTECTION=y
# CONFIG_MFD is not set
CONFIG_GPIO=y
# CONFIG_CPU_HAS_CUSTOM_FIXED_SOC_MPU_REGIONS is not set
# CONFIG_TINYCRYPT is not set
# CONFIG_CODE_DATA_RELOCATION_SRAM is not set
# CONFIG_MBEDTLS is not set
# CONFIG_MEMC is not set
CONFIG_KERNEL_ENTRY="__start"
# CONFIG_CACHE is not set
CONFIG_LOG=y
# CONFIG_CODE_DATA_RELOCATION is not set
# CONFIG_ROMSTART_RELOCATION_ROM is not set
CONFIG_DCACHE_LINE_SIZE=32
CONFIG_SOC="nrf5340"
# CONFIG_RESET is not set
CONFIG_ARCH_SW_ISR_TABLE_ALIGN=4
CONFIG_NRF_RTC_TIMER=y
CONFIG_LOG_DOMAIN_NAME=""
# CONFIG_ASSERT is not set
CONFIG_BUILD_OUTPUT_HEX=y
CONFIG_PM_DEVICE_POWER_DOMAIN=y
CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT=y
CONFIG_SOC_HAS_TIMING_FUNCTIONS=y
# CONFIG_UART_USE_RUNTIME_CONFIGURE is not set
# CONFIG_SYSCON is not set
CONFIG_SERIAL_INIT_PRIORITY=50
CONFIG_SOC_SERIES="nrf53"
CONFIG_SOC_FAMILY="nordic_nrf"
# CONFIG_INTC_MTK_ADSP is not set
# CONFIG_MTK_ADSP_TIMER is not set
CONFIG_GEN_SW_ISR_TABLE=y
# CONFIG_REBOOT is not set
CONFIG_GEN_IRQ_START_VECTOR=0
CONFIG_SRAM_OFFSET=0
# CONFIG_POWER_DOMAIN is not set
CONFIG_CONSOLE=y
CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN=4
CONFIG_ISR_STACK_SIZE=2048
# CONFIG_SCHED_CPU_MASK is not set
# CONFIG_WATCHDOG is not set
CONFIG_ICACHE_LINE_SIZE=32
CONFIG_PRIVILEGED_STACK_SIZE=1024

#
# Devicetree Info
#
CONFIG_DT_HAS_ARDUINO_UNO_ADC_ENABLED=y
CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED=y
CONFIG_DT_HAS_ARM_ARMV8M_ITM_ENABLED=y
CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED=y
CONFIG_DT_HAS_ARM_CORTEX_M33F_ENABLED=y
CONFIG_DT_HAS_ARM_CRYPTOCELL_312_ENABLED=y
CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED=y
CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED=y
CONFIG_DT_HAS_GPIO_KEYS_ENABLED=y
CONFIG_DT_HAS_GPIO_LEDS_ENABLED=y
CONFIG_DT_HAS_MMIO_SRAM_ENABLED=y
CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DCNF_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_FICR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_KMU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_MUTEX_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_OSCILLATORS_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_RESET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SPU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_TWIM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UICR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_USBREG_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53X_REGULATOR_HV_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53X_REGULATORS_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF5X_REGULATOR_ENABLED=y
CONFIG_DT_HAS_NORDIC_QSPI_NOR_ENABLED=y
CONFIG_DT_HAS_PWM_LEDS_ENABLED=y
CONFIG_DT_HAS_REGULATOR_FIXED_ENABLED=y
CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_BT_HCI_IPC_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED=y
# end of Devicetree Info

#
# Modules
#

#
# Available modules.
#

#
# zigbee (C:/ncs/v2.9.0-zigbee/zigbee)
#
# CONFIG_ZIGBEE_R22 is not set
CONFIG_ZEPHYR_ZIGBEE_MODULE=y
# end of zigbee (C:/ncs/v2.9.0-zigbee/zigbee)

#
# nrf (C:/ncs/v2.9.0-zigbee/nrf)
#
CONFIG_NEWLIB_LIBC_NANO=y
CONFIG_NUM_METAIRQ_PRIORITIES=0
CONFIG_LOG_BUFFER_SIZE=1024
CONFIG_MPSL_WORK_STACK_SIZE=1024

#
# Nordic nRF Connect
#
CONFIG_WARN_EXPERIMENTAL=y
CONFIG_BT_BUF_CMD_TX_COUNT=10
CONFIG_INIT_ARCH_HW_AT_BOOT=y
CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE=4096
# CONFIG_GETOPT is not set
# CONFIG_NCS_SAMPLES_DEFAULTS is not set
# CONFIG_NRF70_RADIO_TEST_COMBO is not set

#
# Image build variants
#
# CONFIG_NCS_MCUBOOT_IN_BUILD is not set
# end of Image build variants

#
# Subsystems
#

#
# Bootloader
#
# CONFIG_BUILD_S1_VARIANT is not set
# CONFIG_SECURE_BOOT is not set
CONFIG_PM_PARTITION_SIZE_PROVISION=0x280
# CONFIG_B0_MIN_PARTITION_SIZE is not set
CONFIG_PM_PARTITION_SIZE_B0_IMAGE=0x8000
# CONFIG_IS_SECURE_BOOTLOADER is not set

#
# Secure Boot firmware validation
#
CONFIG_SB_VALIDATION_INFO_MAGIC=0x86518483
CONFIG_SB_VALIDATION_POINTER_MAGIC=0x6919b47e
CONFIG_SB_VALIDATION_INFO_CRYPTO_ID=1
CONFIG_SB_VALIDATION_INFO_VERSION=2
CONFIG_SB_VALIDATION_METADATA_OFFSET=0
CONFIG_SB_VALIDATE_FW_SIGNATURE=y
# end of Secure Boot firmware validation

# CONFIG_SECURE_BOOT_STORAGE is not set
# CONFIG_MCUBOOT_COMPRESSED_IMAGE_SUPPORT_ENABLED is not set
# CONFIG_MCUBOOT_BOOTLOADER_SIGNATURE_TYPE_ED25519 is not set
# CONFIG_MCUBOOT_BOOTLOADER_SIGNATURE_TYPE_PURE is not set
# end of Bootloader

#
# Bluetooth Low Energy
#
CONFIG_SYSTEM_WORKQUEUE_PRIORITY=-1

#
# BLE over nRF RPC
#
CONFIG_HEAP_MEM_POOL_SIZE=0
# end of BLE over nRF RPC
# end of Bluetooth Low Energy

#
# DFU
#
# CONFIG_DFU_MULTI_IMAGE is not set
# CONFIG_DFU_TARGET is not set
# end of DFU

# CONFIG_ESB is not set

#
# Peripheral CPU DFU (PCD)
#
# CONFIG_PCD is not set
# CONFIG_PCD_APP is not set
CONFIG_PCD_VERSION_PAGE_BUF_SIZE=2046
# CONFIG_PCD_NET is not set
# end of Peripheral CPU DFU (PCD)

#
# Networking
#

#
# Application protocols
#

#
# nRF Cloud
#

#
# Transport
#
# CONFIG_NRF_CLOUD_REST is not set
# end of Transport

#
# Credentials
#
CONFIG_NRF_CLOUD_SEC_TAG=16842753
CONFIG_NRF_CLOUD_AWS_CA_CERT_SIZE_THRESHOLD=1150
CONFIG_NRF_CLOUD_COAP_CA_CERT_SIZE_THRESHOLD=550
# end of Credentials

#
# Client ID (nRF Cloud Device ID)
#
CONFIG_NRF_CLOUD_CLIENT_ID_SRC_COMPILE_TIME=y
# CONFIG_NRF_CLOUD_CLIENT_ID_SRC_RUNTIME is not set
CONFIG_NRF_CLOUD_CLIENT_ID="my-client-id"
# end of Client ID (nRF Cloud Device ID)

#
# Firmware Over-the-Air (FOTA) Updates
#
# CONFIG_NRF_CLOUD_FOTA is not set
CONFIG_FOTA_USE_NRF_CLOUD_SETTINGS_AREA=y
# CONFIG_NRF_CLOUD_FOTA_FULL_MODEM_UPDATE is not set
# CONFIG_NRF_CLOUD_FOTA_SMP is not set
# CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_FOTA_LOG_LEVEL=3
# end of Firmware Over-the-Air (FOTA) Updates

#
# Location
#

#
# Cellular/Wi-Fi
#
# CONFIG_NRF_CLOUD_LOCATION_PARSE_ANCHORS is not set
# CONFIG_NRF_CLOUD_WIFI_LOCATION_ENCODE_OPT_MAC_ONLY is not set
CONFIG_NRF_CLOUD_WIFI_LOCATION_ENCODE_OPT_MAC_RSSI=y
# CONFIG_NRF_CLOUD_WIFI_LOCATION_ENCODE_OPT_ALL is not set
# end of Cellular/Wi-Fi
# end of Location

#
# Alerts
#
# CONFIG_NRF_CLOUD_ALERT is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL=3
# end of Alerts

#
# Logging
#
CONFIG_NRF_CLOUD_LOG_OUTPUT_LEVEL=1
# CONFIG_NRF_CLOUD_LOG_INCLUDE_LEVEL_0 is not set
CONFIG_NRF_CLOUD_LOG_BUF_SIZE=256
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_LOG_LOG_LEVEL=3
# end of Logging

#
# Device Shadow
#

#
# Send shadow info sections on initial connect (MQTT/CoAP)
#
# end of Send shadow info sections on initial connect (MQTT/CoAP)
# end of Device Shadow

CONFIG_NRF_CLOUD_PRINT_DETAILS=y
CONFIG_NRF_CLOUD_VERBOSE_DETAILS=y
# CONFIG_NRF_CLOUD_GATEWAY is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_LOG_LEVEL=3
# end of nRF Cloud

# CONFIG_REST_CLIENT is not set
# CONFIG_DOWNLOAD_CLIENT is not set
# CONFIG_AWS_IOT is not set
# CONFIG_AWS_JOBS is not set
# CONFIG_AZURE_IOT_HUB is not set

#
# Self-Registration (Zi ZHu Ce)
#
# end of Self-Registration (Zi ZHu Ce)

# CONFIG_ICAL_PARSER is not set
# CONFIG_FTP_CLIENT is not set
# CONFIG_LWM2M_CLIENT_UTILS is not set
# CONFIG_WIFI_CREDENTIALS is not set
# CONFIG_WIFI_CREDENTIALS_STATIC is not set
# CONFIG_MQTT_HELPER is not set
# CONFIG_NRF_MCUMGR_SMP_CLIENT is not set
# end of Application protocols

# CONFIG_OPENTHREAD_RPC is not set
# end of Networking

#
# NFC
#
# CONFIG_NFC_NDEF is not set
# CONFIG_NFC_NDEF_PARSER is not set
# CONFIG_NFC_NDEF_PAYLOAD_TYPE_COMMON is not set
# CONFIG_NFC_T2T_PARSER is not set
# CONFIG_NFC_T4T_NDEF_FILE is not set
# CONFIG_NFC_T4T_ISODEP is not set
# CONFIG_NFC_T4T_APDU is not set
# CONFIG_NFC_T4T_CC_FILE is not set
# CONFIG_NFC_T4T_HL_PROCEDURE is not set
# CONFIG_NFC_PLATFORM is not set
# CONFIG_NFC_TNEP_TAG is not set
# CONFIG_NFC_TNEP_POLLER is not set
# CONFIG_NFC_TNEP_CH is not set
# CONFIG_NFC_RPC is not set
# end of NFC

# CONFIG_APP_EVENT_MANAGER is not set
# CONFIG_NRF_PROFILER is not set
# CONFIG_FW_INFO is not set

#
# Debug
#
# CONFIG_CPU_LOAD is not set
# CONFIG_PPI_TRACE is not set
# end of Debug

#
# Multiprotocol service layer (MPSL)
#
# CONFIG_MPSL_FEM_ONLY is not set
# CONFIG_MPSL_FEM_DEVICE_CONFIG_254 is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_OFF is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_ERR is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_WRN is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_INF is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_DBG is not set
CONFIG_MPSL_FEM_LOG_LEVEL_DEFAULT=y
CONFIG_MPSL_FEM_LOG_LEVEL=3
CONFIG_MPSL_THREAD_COOP_PRIO=8
CONFIG_MPSL_TIMESLOT_SESSION_COUNT=0
# CONFIG_MPSL_ASSERT_HANDLER is not set
CONFIG_MPSL_LOW_PRIO_IRQN=26
CONFIG_MPSL_HFCLK_LATENCY=1400
# CONFIG_MPSL_LOG_LEVEL_OFF is not set
# CONFIG_MPSL_LOG_LEVEL_ERR is not set
# CONFIG_MPSL_LOG_LEVEL_WRN is not set
# CONFIG_MPSL_LOG_LEVEL_INF is not set
# CONFIG_MPSL_LOG_LEVEL_DBG is not set
CONFIG_MPSL_LOG_LEVEL_DEFAULT=y
CONFIG_MPSL_LOG_LEVEL=3
# CONFIG_MPSL_PIN_DEBUG is not set
# end of Multiprotocol service layer (MPSL)

#
# Partition Manager
#
# CONFIG_PARTITION_MANAGER_ENABLED is not set
CONFIG_SRAM_BASE_ADDRESS=0x20000000
CONFIG_NRF_TRUSTZONE_FLASH_REGION_SIZE=0x4000
CONFIG_NRF_TRUSTZONE_RAM_REGION_SIZE=0x2000

#
# Zephyr subsystem configurations
#
# end of Zephyr subsystem configurations

#
# NCS subsystem configurations
#
# CONFIG_PM_SINGLE_IMAGE is not set
CONFIG_PM_EXTERNAL_FLASH_BASE=0
CONFIG_PM_EXTERNAL_FLASH_ENABLED=y
CONFIG_PM_EXTERNAL_FLASH_PATH="/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0"
CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS=67108864
# CONFIG_PM_OVERRIDE_EXTERNAL_DRIVER_CHECK is not set
CONFIG_PM_SRAM_BASE=0x20000000
CONFIG_PM_SRAM_SIZE=0x80000
# end of Partition Manager

#
# nRF RPC (Remote Procedure Call) library
#
# end of nRF RPC (Remote Procedure Call) library

# CONFIG_ZIGBEE is not set

#
# Full Modem Firmware Update Management(FMFU)
#
# CONFIG_MGMT_FMFU_LOG_LEVEL_OFF is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_ERR is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_WRN is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_INF is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_DBG is not set
CONFIG_MGMT_FMFU_LOG_LEVEL_DEFAULT=y
CONFIG_MGMT_FMFU_LOG_LEVEL=3
# end of Full Modem Firmware Update Management(FMFU)

# CONFIG_CAF is not set

#
# Nordic IEEE 802.15.4
#
# end of Nordic IEEE 802.15.4

# CONFIG_DM_MODULE is not set

#
# nRF Security
#
# CONFIG_NORDIC_SECURITY_BACKEND is not set
# CONFIG_NRF_SECURITY is not set

#
# Nordic-added meta types
#

#
# Nordic added alg types
#
# CONFIG_PSA_WANT_ALG_ECDSA_ANY is not set
# CONFIG_PSA_WANT_ALG_ED25519PH is not set
# CONFIG_PSA_WANT_ALG_ED448PH is not set
# CONFIG_PSA_WANT_ALG_PURE_EDDSA is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN_RAW is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS_ANY_SALT is not set
# CONFIG_PSA_WANT_ALG_SHA_512_224 is not set
# CONFIG_PSA_WANT_ALG_SHA_512_256 is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P_HMAC is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P_CMAC is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P_MATTER is not set
# CONFIG_PSA_WANT_ALG_SRP_6 is not set
# CONFIG_PSA_WANT_ALG_SRP_PASSWORD_HASH is not set
# CONFIG_PSA_WANT_ALG_XTS is not set

#
# Nordic added ECC curve types
#
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_160 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_192 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_224 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_320 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_255 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_448 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_224 is not set
# CONFIG_PSA_WANT_ECC_SECP_R2_160 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_239 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R2_163 is not set
# CONFIG_PSA_WANT_ECC_FRP_V1_256 is not set
# CONFIG_PSA_WANT_ALG_CHACHA20 is not set
# CONFIG_PSA_WANT_ALG_SHAKE256_512 is not set

#
# Nordic addded RNG configuration
#
# CONFIG_PSA_WANT_GENERATE_RANDOM is not set

#
# Nordic added key types
#
# CONFIG_PSA_WANT_KEY_TYPE_PEPPER is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_GENERATE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_IMPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_EXPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_GENERATE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_PUBLIC_KEY is not set
# end of nRF Security

# CONFIG_NET_CORE_MONITOR is not set

#
# Audio Modules
#
# CONFIG_AUDIO_MODULE_TEST is not set
CONFIG_AUDIO_MODULE_NAME_SIZE=20

#
# Log levels
#
# CONFIG_AUDIO_MODULE_LOG_LEVEL_OFF is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_ERR is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_WRN is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_INF is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_DBG is not set
CONFIG_AUDIO_MODULE_LOG_LEVEL_DEFAULT=y
CONFIG_AUDIO_MODULE_LOG_LEVEL=3
# end of Log levels
# end of Audio Modules

#
# Audio Modules
#
# CONFIG_AUDIO_MODULE_TEMPLATE is not set
# end of Audio Modules

# CONFIG_UART_ASYNC_ADAPTER is not set
# CONFIG_TRUSTED_STORAGE is not set

#
# Logging over RPC
#
# CONFIG_LOG_FORWARDER_RPC is not set
# CONFIG_LOG_BACKEND_RPC is not set
# end of Logging over RPC

#
# SUIT provisioning
#
# CONFIG_SUIT_MPI_GENERATE is not set
# end of SUIT provisioning

# CONFIG_SUIT is not set
# CONFIG_NRF_COMPRESS is not set

#
# MCUboot IDs (informative only, do not change)
#
CONFIG_MCUBOOT_APPLICATION_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_NETWORK_CORE_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_WIFI_PATCHES_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_QSPI_XIP_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_MCUBOOT_IMAGE_NUMBER=-1
# end of MCUboot IDs (informative only, do not change)
# end of Subsystems

# CONFIG_WFA_QT_LOG_LEVEL_OFF is not set
# CONFIG_WFA_QT_LOG_LEVEL_ERR is not set
# CONFIG_WFA_QT_LOG_LEVEL_WRN is not set
# CONFIG_WFA_QT_LOG_LEVEL_INF is not set
# CONFIG_WFA_QT_LOG_LEVEL_DBG is not set
CONFIG_WFA_QT_LOG_LEVEL_DEFAULT=y
CONFIG_WFA_QT_LOG_LEVEL=3
# CONFIG_WFA_QT_CONTROL_APP is not set
CONFIG_WFA_QT_THREAD_STACK_SIZE=5200
CONFIG_WFA_QT_REBOOT_TIMEOUT_MS=1000
CONFIG_WFA_QT_DEFAULT_INTERFACE="wlan0"
CONFIG_WPAS_READY_TIMEOUT_MS=10000

#
# Libraries
#

#
# Binary libraries
#
# end of Binary libraries

# CONFIG_AT_MONITOR is not set
# CONFIG_LTE_LINK_CONTROL is not set
CONFIG_NRF_SPU_FLASH_REGION_SIZE=0x4000
CONFIG_FPROTECT_BLOCK_SIZE=0x4000
# CONFIG_FPROTECT is not set
# CONFIG_AT_CMD_CUSTOM is not set
# CONFIG_DK_LIBRARY is not set
# CONFIG_AT_CMD_PARSER is not set
# CONFIG_AT_PARSER is not set
# CONFIG_MODEM_INFO is not set
# CONFIG_RESET_ON_FATAL_ERROR is not set
# CONFIG_SMS is not set
# CONFIG_SUPL_CLIENT_LIB is not set
# CONFIG_DATE_TIME is not set
# CONFIG_HW_ID_LIBRARY is not set
# CONFIG_RAM_POWER_DOWN_LIBRARY is not set
# CONFIG_WAVE_GEN_LIB is not set
CONFIG_HW_UNIQUE_KEY_SUPPORTED=y
CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE=0
# CONFIG_MODEM_JWT is not set
# CONFIG_LOCATION is not set
# CONFIG_QOS is not set
# CONFIG_SFLOAT is not set
# CONFIG_CONTIN_ARRAY is not set
# CONFIG_PCM_MIX is not set
# CONFIG_TONE is not set
# CONFIG_PSCM is not set
# CONFIG_DATA_FIFO is not set
# CONFIG_FEM_AL_LIB is not set
# CONFIG_SAMPLE_RATE_CONVERTER is not set
CONFIG_NCS_BOOT_BANNER=y
CONFIG_NCS_NCS_BOOT_BANNER_STRING="nRF Connect SDK"
CONFIG_NCS_ZEPHYR_BOOT_BANNER_STRING="Zephyr OS"
# end of Libraries

#
# Device Drivers
#
# CONFIG_BT_DRIVER_QUIRK_NO_AUTO_DLE is not set
# CONFIG_FLASH_RPC is not set
CONFIG_HW_CC3XX=y
# CONFIG_ETH_RTT is not set
# CONFIG_SENSOR is not set
# CONFIG_NRF_SW_LPUART is not set
CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS=1
# end of Device Drivers

#
# External libraries
#
# end of External libraries

#
# Test
#
CONFIG_ZTEST_MULTICORE_DEFAULT_SETTINGS=y
# CONFIG_UNITY is not set

#
# Mocks
#
# CONFIG_MOCK_NRF_MODEM_AT is not set
# CONFIG_MOCK_NRF_RPC is not set
# end of Mocks
# end of Test
# end of Nordic nRF Connect

CONFIG_ZEPHYR_NRF_MODULE=y
# end of nrf (C:/ncs/v2.9.0-zigbee/nrf)

#
# mcuboot (C:/ncs/v2.9.0-zigbee/bootloader/mcuboot)
#

#
# MCUboot
#
CONFIG_BOOT_SIGNATURE_KEY_FILE=""
CONFIG_DT_FLASH_WRITE_BLOCK_SIZE=4
# CONFIG_USE_NRF53_MULTI_IMAGE_WITHOUT_UPGRADE_ONLY is not set
# CONFIG_MCUBOOT_USE_ALL_AVAILABLE_RAM is not set
# end of MCUboot

# CONFIG_XIP_SPLIT_IMAGE is not set
CONFIG_ZEPHYR_MCUBOOT_MODULE=y
# end of mcuboot (C:/ncs/v2.9.0-zigbee/bootloader/mcuboot)

#
# mbedtls (C:/ncs/v2.9.0-zigbee/modules/crypto/mbedtls)
#
CONFIG_ZEPHYR_MBEDTLS_MODULE=y
# end of mbedtls (C:/ncs/v2.9.0-zigbee/modules/crypto/mbedtls)

#
# oberon-psa-crypto (C:/ncs/v2.9.0-zigbee/modules/crypto/oberon-psa-crypto)
#
CONFIG_ZEPHYR_OBERON_PSA_CRYPTO_MODULE=y
# end of oberon-psa-crypto (C:/ncs/v2.9.0-zigbee/modules/crypto/oberon-psa-crypto)

#
# trusted-firmware-m (C:/ncs/v2.9.0-zigbee/modules/tee/tf-m/trusted-firmware-m)
#
CONFIG_TFM_BOARD="C:/ncs/v2.9.0-zigbee/zephyr/modules/trusted-firmware-m/nordic/nrf5340_cpuapp"
# CONFIG_BOOTLOADER_MCUBOOT is not set
CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE=y
# end of trusted-firmware-m (C:/ncs/v2.9.0-zigbee/modules/tee/tf-m/trusted-firmware-m)

CONFIG_ZEPHYR_PSA_ARCH_TESTS_MODULE=y
CONFIG_ZEPHYR_SOC_HWMV1_MODULE=y

#
# cjson (C:/ncs/v2.9.0-zigbee/modules/lib/cjson)
#
# CONFIG_CJSON_LIB is not set
CONFIG_ZEPHYR_CJSON_MODULE=y
# end of cjson (C:/ncs/v2.9.0-zigbee/modules/lib/cjson)

#
# azure-sdk-for-c (C:/ncs/v2.9.0-zigbee/modules/lib/azure-sdk-for-c)
#
# CONFIG_AZURE_SDK is not set
CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE=y
# end of azure-sdk-for-c (C:/ncs/v2.9.0-zigbee/modules/lib/azure-sdk-for-c)

#
# cirrus-logic (C:/ncs/v2.9.0-zigbee/modules/hal/cirrus-logic)
#
# CONFIG_HW_CODEC_CIRRUS_LOGIC is not set
CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE=y
# end of cirrus-logic (C:/ncs/v2.9.0-zigbee/modules/hal/cirrus-logic)

#
# openthread (C:/ncs/v2.9.0-zigbee/modules/lib/openthread)
#
# CONFIG_OPENTHREAD is not set
CONFIG_ZEPHYR_OPENTHREAD_MODULE=y
# end of openthread (C:/ncs/v2.9.0-zigbee/modules/lib/openthread)

#
# suit-generator (C:/ncs/v2.9.0-zigbee/modules/lib/suit-generator)
#
CONFIG_SUIT_ENVELOPE_TEMPLATE_FILENAME=""
CONFIG_SUIT_ENVELOPE_TARGET=""
CONFIG_SUIT_ENVELOPE_OUTPUT_ARTIFACT="merged.hex"
# CONFIG_SUIT_RECOVERY is not set
CONFIG_SUIT_ENVELOPE_OUTPUT_MPI_MERGE=y
# CONFIG_SUIT_LOCAL_ENVELOPE_GENERATE is not set
# CONFIG_SUIT_DFU_CACHE_EXTRACT_IMAGE is not set
CONFIG_ZEPHYR_SUIT_GENERATOR_MODULE=y
# end of suit-generator (C:/ncs/v2.9.0-zigbee/modules/lib/suit-generator)

#
# suit-processor (C:/ncs/v2.9.0-zigbee/modules/lib/suit-processor)
#
# CONFIG_SUIT_PROCESSOR is not set
CONFIG_SUIT_PLATFORM_DRY_RUN_SUPPORT=y
CONFIG_ZEPHYR_SUIT_PROCESSOR_MODULE=y
# end of suit-processor (C:/ncs/v2.9.0-zigbee/modules/lib/suit-processor)

#
# memfault-firmware-sdk (C:/ncs/v2.9.0-zigbee/modules/lib/memfault-firmware-sdk)
#
# CONFIG_MEMFAULT is not set
CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE=y
# end of memfault-firmware-sdk (C:/ncs/v2.9.0-zigbee/modules/lib/memfault-firmware-sdk)

#
# coremark (C:/ncs/v2.9.0-zigbee/modules/benchmark/coremark)
#
# CONFIG_COREMARK is not set
CONFIG_ZEPHYR_COREMARK_MODULE=y
# end of coremark (C:/ncs/v2.9.0-zigbee/modules/benchmark/coremark)

#
# canopennode (C:/ncs/v2.9.0-zigbee/modules/lib/canopennode)
#
CONFIG_ZEPHYR_CANOPENNODE_MODULE=y
# end of canopennode (C:/ncs/v2.9.0-zigbee/modules/lib/canopennode)

#
# chre (C:/ncs/v2.9.0-zigbee/modules/lib/chre)
#
CONFIG_ZEPHYR_CHRE_MODULE=y
# CONFIG_CHRE is not set
# end of chre (C:/ncs/v2.9.0-zigbee/modules/lib/chre)

#
# lz4 (C:/ncs/v2.9.0-zigbee/modules/lib/lz4)
#
CONFIG_ZEPHYR_LZ4_MODULE=y
# CONFIG_LZ4 is not set
# end of lz4 (C:/ncs/v2.9.0-zigbee/modules/lib/lz4)

#
# nanopb (C:/ncs/v2.9.0-zigbee/modules/lib/nanopb)
#
CONFIG_ZEPHYR_NANOPB_MODULE=y
# CONFIG_NANOPB is not set
# end of nanopb (C:/ncs/v2.9.0-zigbee/modules/lib/nanopb)

CONFIG_ZEPHYR_TF_M_TESTS_MODULE=y

#
# zscilib (C:/ncs/v2.9.0-zigbee/modules/lib/zscilib)
#
# CONFIG_ZSL is not set
CONFIG_ZEPHYR_ZSCILIB_MODULE=y
# end of zscilib (C:/ncs/v2.9.0-zigbee/modules/lib/zscilib)

#
# cmsis (C:/ncs/v2.9.0-zigbee/modules/hal/cmsis)
#
CONFIG_ZEPHYR_CMSIS_MODULE=y
CONFIG_HAS_CMSIS_CORE=y
CONFIG_HAS_CMSIS_CORE_M=y
# CONFIG_CMSIS_M_CHECK_DEVICE_DEFINES is not set
# end of cmsis (C:/ncs/v2.9.0-zigbee/modules/hal/cmsis)

#
# cmsis-dsp (C:/ncs/v2.9.0-zigbee/modules/lib/cmsis-dsp)
#
CONFIG_ZEPHYR_CMSIS_DSP_MODULE=y
# CONFIG_CMSIS_DSP is not set
# end of cmsis-dsp (C:/ncs/v2.9.0-zigbee/modules/lib/cmsis-dsp)

#
# cmsis-nn (C:/ncs/v2.9.0-zigbee/modules/lib/cmsis-nn)
#
CONFIG_ZEPHYR_CMSIS_NN_MODULE=y
# CONFIG_CMSIS_NN is not set
# end of cmsis-nn (C:/ncs/v2.9.0-zigbee/modules/lib/cmsis-nn)

#
# fatfs (C:/ncs/v2.9.0-zigbee/modules/fs/fatfs)
#
CONFIG_ZEPHYR_FATFS_MODULE=y
# end of fatfs (C:/ncs/v2.9.0-zigbee/modules/fs/fatfs)

#
# hal_nordic (C:/ncs/v2.9.0-zigbee/modules/hal/nordic)
#
CONFIG_ZEPHYR_HAL_NORDIC_MODULE=y
CONFIG_HAS_NORDIC_DRIVERS=y

#
# Nordic drivers
#
# CONFIG_NRF_802154_SOURCE_HAL_NORDIC is not set
# CONFIG_NRF_802154_SER_HOST is not set
# end of Nordic drivers

CONFIG_HAS_NRFX=y

#
# nrfx drivers
#

#
# nrfx drivers logging
#
# CONFIG_NRFX_CLOCK_LOG is not set
# CONFIG_NRFX_DPPI_LOG is not set
# CONFIG_NRFX_GPIOTE_LOG is not set
# CONFIG_NRFX_IPC_LOG is not set
# end of nrfx drivers logging

CONFIG_NRFX_CLOCK=y
CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED=y
# CONFIG_NRFX_COMP is not set
CONFIG_NRFX_DPPI=y
CONFIG_NRFX_DPPI0=y
# CONFIG_NRFX_EGU0 is not set
# CONFIG_NRFX_EGU1 is not set
# CONFIG_NRFX_EGU2 is not set
# CONFIG_NRFX_EGU3 is not set
# CONFIG_NRFX_EGU4 is not set
# CONFIG_NRFX_EGU5 is not set
CONFIG_NRFX_GPIOTE=y
CONFIG_NRFX_GPIOTE0=y
# CONFIG_NRFX_GPIOTE1 is not set
CONFIG_NRFX_GPPI=y
# CONFIG_NRFX_I2S0 is not set
CONFIG_NRFX_IPC=y
# CONFIG_NRFX_NFCT is not set
# CONFIG_NRFX_NVMC is not set
# CONFIG_NRFX_PDM0 is not set
# CONFIG_NRFX_POWER is not set
# CONFIG_NRFX_PWM0 is not set
# CONFIG_NRFX_PWM1 is not set
# CONFIG_NRFX_PWM2 is not set
# CONFIG_NRFX_PWM3 is not set
# CONFIG_NRFX_QDEC0 is not set
# CONFIG_NRFX_QDEC1 is not set
# CONFIG_NRFX_QSPI is not set
# CONFIG_NRFX_RTC0 is not set
# CONFIG_NRFX_RTC1 is not set
# CONFIG_NRFX_SAADC is not set
# CONFIG_NRFX_SPIM0 is not set
# CONFIG_NRFX_SPIM1 is not set
# CONFIG_NRFX_SPIM2 is not set
# CONFIG_NRFX_SPIM3 is not set
# CONFIG_NRFX_SPIM4 is not set
# CONFIG_NRFX_SYSTICK is not set
# CONFIG_NRFX_TIMER0 is not set
# CONFIG_NRFX_TIMER1 is not set
# CONFIG_NRFX_TIMER2 is not set
# CONFIG_NRFX_TWIM0 is not set
# CONFIG_NRFX_TWIM1 is not set
# CONFIG_NRFX_TWIM2 is not set
# CONFIG_NRFX_TWIM3 is not set
# CONFIG_NRFX_UARTE0 is not set
# CONFIG_NRFX_UARTE1 is not set
# CONFIG_NRFX_UARTE2 is not set
# CONFIG_NRFX_UARTE3 is not set
# CONFIG_NRFX_USBREG is not set
# CONFIG_NRFX_WDT0 is not set
# CONFIG_NRFX_WDT1 is not set

#
# Peripheral Resource Sharing module
#
# CONFIG_NRFX_PRS_BOX_0 is not set
# CONFIG_NRFX_PRS_BOX_1 is not set
# CONFIG_NRFX_PRS_BOX_2 is not set
# CONFIG_NRFX_PRS_BOX_3 is not set
# CONFIG_NRFX_PRS_BOX_4 is not set
# end of Peripheral Resource Sharing module

CONFIG_NRFX_RESERVED_RESOURCES_HEADER="nrfx_config_reserved_resources_ncs.h"
# end of nrfx drivers
# end of hal_nordic (C:/ncs/v2.9.0-zigbee/modules/hal/nordic)

#
# hal_st (C:/ncs/v2.9.0-zigbee/modules/hal/st)
#
CONFIG_ZEPHYR_HAL_ST_MODULE=y
# end of hal_st (C:/ncs/v2.9.0-zigbee/modules/hal/st)

CONFIG_ZEPHYR_HAL_WURTHELEKTRONIK_MODULE=y

#
# hostap (C:/ncs/v2.9.0-zigbee/modules/lib/hostap)
#
# CONFIG_WIFI_NM_WPA_SUPPLICANT is not set
CONFIG_ZEPHYR_HOSTAP_MODULE=y
# end of hostap (C:/ncs/v2.9.0-zigbee/modules/lib/hostap)

CONFIG_ZEPHYR_LIBMETAL_MODULE=y

#
# liblc3 (C:/ncs/v2.9.0-zigbee/modules/lib/liblc3)
#
CONFIG_ZEPHYR_LIBLC3_MODULE=y
# CONFIG_LIBLC3 is not set
# end of liblc3 (C:/ncs/v2.9.0-zigbee/modules/lib/liblc3)

#
# littlefs (C:/ncs/v2.9.0-zigbee/modules/fs/littlefs)
#
CONFIG_ZEPHYR_LITTLEFS_MODULE=y
# end of littlefs (C:/ncs/v2.9.0-zigbee/modules/fs/littlefs)

#
# loramac-node (C:/ncs/v2.9.0-zigbee/modules/lib/loramac-node)
#
CONFIG_ZEPHYR_LORAMAC_NODE_MODULE=y
# CONFIG_HAS_SEMTECH_RADIO_DRIVERS is not set
# end of loramac-node (C:/ncs/v2.9.0-zigbee/modules/lib/loramac-node)

#
# lvgl (C:/ncs/v2.9.0-zigbee/modules/lib/gui/lvgl)
#
CONFIG_ZEPHYR_LVGL_MODULE=y
# end of lvgl (C:/ncs/v2.9.0-zigbee/modules/lib/gui/lvgl)

CONFIG_ZEPHYR_MIPI_SYS_T_MODULE=y

#
# nrf_wifi (C:/ncs/v2.9.0-zigbee/modules/lib/nrf_wifi)
#
CONFIG_ZEPHYR_NRF_WIFI_MODULE=y
# CONFIG_NRF70_BUSLIB is not set
# end of nrf_wifi (C:/ncs/v2.9.0-zigbee/modules/lib/nrf_wifi)

CONFIG_ZEPHYR_OPEN_AMP_MODULE=y

#
# picolibc (C:/ncs/v2.9.0-zigbee/modules/lib/picolibc)
#
# CONFIG_PICOLIBC_MODULE is not set
CONFIG_ZEPHYR_PICOLIBC_MODULE=y
# end of picolibc (C:/ncs/v2.9.0-zigbee/modules/lib/picolibc)

#
# segger (C:/ncs/v2.9.0-zigbee/modules/debug/segger)
#
CONFIG_ZEPHYR_SEGGER_MODULE=y
CONFIG_HAS_SEGGER_RTT=y
# CONFIG_USE_SEGGER_RTT is not set
# end of segger (C:/ncs/v2.9.0-zigbee/modules/debug/segger)

CONFIG_ZEPHYR_TINYCRYPT_MODULE=y

#
# uoscore-uedhoc (C:/ncs/v2.9.0-zigbee/modules/lib/uoscore-uedhoc)
#
CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE=y
# end of uoscore-uedhoc (C:/ncs/v2.9.0-zigbee/modules/lib/uoscore-uedhoc)

#
# zcbor (C:/ncs/v2.9.0-zigbee/modules/lib/zcbor)
#
CONFIG_ZEPHYR_ZCBOR_MODULE=y
# CONFIG_ZCBOR is not set
# end of zcbor (C:/ncs/v2.9.0-zigbee/modules/lib/zcbor)

#
# nrfxlib (C:/ncs/v2.9.0-zigbee/nrfxlib)
#

#
# Nordic nrfxlib
#

#
# nrf_modem (Modem library)
#
CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE=0x4e8
# end of nrf_modem (Modem library)

# CONFIG_NFC_T2T_NRFXLIB is not set
# CONFIG_NFC_T4T_NRFXLIB is not set

#
# Crypto libraries for nRF5x SOCs.
#
CONFIG_NRFXLIB_CRYPTO=y
CONFIG_HAS_HW_NRF_CC3XX=y
# CONFIG_NRF_OBERON is not set
# CONFIG_NRF_CC310_BL is not set
CONFIG_NRF_CC3XX_PLATFORM=y
CONFIG_CC3XX_MUTEX_LOCK=y
# CONFIG_CC3XX_ATOMIC_LOCK is not set
# CONFIG_CC3XX_HW_MUTEX_LOCK is not set
# end of Crypto libraries for nRF5x SOCs.

# CONFIG_NRF_RPC is not set
CONFIG_NRF_802154_SOURCE_NRFXLIB=y
# CONFIG_GZLL is not set
# CONFIG_NRF_DM is not set
# CONFIG_SW_CODEC_LC3_T2_SOFTWARE is not set
# CONFIG_NRF_FUEL_GAUGE is not set
# end of Nordic nrfxlib

CONFIG_ZEPHYR_NRFXLIB_MODULE=y
# end of nrfxlib (C:/ncs/v2.9.0-zigbee/nrfxlib)

CONFIG_ZEPHYR_NRF_HW_MODELS_MODULE=y

#
# connectedhomeip (C:/ncs/v2.9.0-zigbee/modules/lib/matter)
#
# CONFIG_CHIP is not set
CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE=y
# end of connectedhomeip (C:/ncs/v2.9.0-zigbee/modules/lib/matter)

# CONFIG_LIBMETAL is not set
# CONFIG_LVGL is not set
# CONFIG_HAS_MEC_HAL is not set
# CONFIG_HAS_MPFS_HAL is not set
# CONFIG_HAS_MEC5_HAL is not set
# CONFIG_OPENAMP is not set
# CONFIG_MIPI_SYST_LIB is not set
# CONFIG_HAS_TELINK_DRIVERS is not set
# CONFIG_MCUBOOT_BOOTUTIL_LIB is not set

#
# Unavailable modules, please install those via the project manifest.
#

#
# hal_gigadevice module not available.
#

#
# Trusted-firmware-a module not available.
#

#
# THRIFT module not available.
#
# CONFIG_ACPI is not set
# end of Modules

CONFIG_BOARD="nrf5340dk"
CONFIG_BOARD_REVISION=""
CONFIG_BOARD_TARGET="nrf5340dk/nrf5340/cpuapp"
# CONFIG_NET_DRIVERS is not set
CONFIG_BOARD_NRF5340DK=y
CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP=y
CONFIG_BOARD_QUALIFIERS="nrf5340/cpuapp"

#
# Board Options
#
CONFIG_DOMAIN_CPUNET_BOARD="nrf5340dk/nrf5340/cpunet"
# end of Board Options

#
# Hardware Configuration
#
CONFIG_SOC_FAMILY_NORDIC_NRF=y
CONFIG_SOC_SERIES_NRF53X=y
CONFIG_SOC_NRF5340_CPUAPP=y
CONFIG_SOC_NRF5340_CPUAPP_QKAA=y
# CONFIG_BUILD_OUTPUT_INFO_HEADER is not set
CONFIG_HAS_HW_NRF_CC312=y
CONFIG_HAS_HW_NRF_CLOCK=y
CONFIG_HAS_HW_NRF_CTRLAP=y
CONFIG_HAS_HW_NRF_DCNF=y
CONFIG_HAS_HW_NRF_DPPIC=y
CONFIG_HAS_HW_NRF_EGU0=y
CONFIG_HAS_HW_NRF_EGU1=y
CONFIG_HAS_HW_NRF_EGU2=y
CONFIG_HAS_HW_NRF_EGU3=y
CONFIG_HAS_HW_NRF_EGU4=y
CONFIG_HAS_HW_NRF_EGU5=y
CONFIG_HAS_HW_NRF_GPIO0=y
CONFIG_HAS_HW_NRF_GPIO1=y
CONFIG_HAS_HW_NRF_GPIOTE0=y
CONFIG_HAS_HW_NRF_KMU=y
CONFIG_HAS_HW_NRF_MUTEX=y
CONFIG_HAS_HW_NRF_NVMC_PE=y
CONFIG_HAS_HW_NRF_OSCILLATORS=y
CONFIG_HAS_HW_NRF_POWER=y
CONFIG_HAS_HW_NRF_RESET=y
CONFIG_HAS_HW_NRF_SPU=y
CONFIG_HAS_HW_NRF_TWIM1=y
CONFIG_HAS_HW_NRF_UARTE0=y
CONFIG_HAS_HW_NRF_USBREG=y
CONFIG_HAS_HW_NRF_VMC=y
# CONFIG_HAS_NORDIC_RAM_CTRL is not set
# CONFIG_NRF_SYS_EVENT is not set
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED=y
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND=y
CONFIG_SOC_NRF53_ANOMALY_168_WORKAROUND=y
# CONFIG_SOC_NRF53_ANOMALY_168_WORKAROUND_FOR_EXECUTION_FROM_RAM is not set
CONFIG_SOC_NRF53_RTC_PRETICK=y
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET=10
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET=11
CONFIG_NRF_SPU_RAM_REGION_SIZE=0x2000
CONFIG_SOC_NRF_GPIO_FORWARDER_FOR_NRF5340=y
CONFIG_SOC_NRF53_CPUNET_MGMT=y
# CONFIG_SOC_NRF53_CPUNET_ENABLE is not set
# CONFIG_BOARD_ENABLE_CPUNET is not set
CONFIG_SOC_ENABLE_LFXO=y
# CONFIG_SOC_LFXO_CAP_EXTERNAL is not set
# CONFIG_SOC_LFXO_CAP_INT_6PF is not set
CONFIG_SOC_LFXO_CAP_INT_7PF=y
# CONFIG_SOC_LFXO_CAP_INT_9PF is not set
CONFIG_SOC_HFXO_CAP_DEFAULT=y
# CONFIG_SOC_HFXO_CAP_EXTERNAL is not set
# CONFIG_SOC_HFXO_CAP_INTERNAL is not set
CONFIG_NRF_ENABLE_CACHE=y
CONFIG_NRF53_SYNC_RTC=y
# CONFIG_SYNC_RTC_LOG_LEVEL_OFF is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_ERR is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_WRN is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_INF is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_DBG is not set
CONFIG_SYNC_RTC_LOG_LEVEL_DEFAULT=y
CONFIG_SYNC_RTC_LOG_LEVEL=3
CONFIG_NRF53_SYNC_RTC_INIT_PRIORITY=90
CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT=1
CONFIG_NRF53_SYNC_RTC_LOG_TIMESTAMP=y
CONFIG_NRF53_SYNC_RTC_IPM_OUT=7
CONFIG_NRF53_SYNC_RTC_IPM_IN=8
CONFIG_IPM_MSG_CH_8_ENABLE=y
CONFIG_IPM_MSG_CH_8_RX=y
# CONFIG_SOC_NRF54H20_GPD is not set
# CONFIG_SOC_NRF54H20_NO_MRAM_LATENCY is not set
CONFIG_NRF_SOC_SECURE_SUPPORTED=y
# CONFIG_NFCT_PINS_AS_GPIOS is not set
CONFIG_NRF_APPROTECT_USE_UICR=y
# CONFIG_NRF_APPROTECT_LOCK is not set
# CONFIG_NRF_APPROTECT_USER_HANDLING is not set
CONFIG_NRF_SECURE_APPROTECT_USE_UICR=y
# CONFIG_NRF_SECURE_APPROTECT_LOCK is not set
# CONFIG_NRF_SECURE_APPROTECT_USER_HANDLING is not set
# CONFIG_NRF_TRACE_PORT is not set
CONFIG_GPIO_INIT_PRIORITY=40
# CONFIG_SOC_LOG_LEVEL_OFF is not set
# CONFIG_SOC_LOG_LEVEL_ERR is not set
# CONFIG_SOC_LOG_LEVEL_WRN is not set
# CONFIG_SOC_LOG_LEVEL_INF is not set
# CONFIG_SOC_LOG_LEVEL_DBG is not set
CONFIG_SOC_LOG_LEVEL_DEFAULT=y
CONFIG_SOC_LOG_LEVEL=3
# end of Hardware Configuration

CONFIG_SOC_COMPATIBLE_NRF=y
CONFIG_SOC_COMPATIBLE_NRF53X=y
CONFIG_SOC_COMPATIBLE_NRF5340_CPUAPP=y
CONFIG_ARCH="arm"
# CONFIG_EXTRA_EXCEPTION_INFO is not set
CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT=y
CONFIG_CPU_CORTEX=y
CONFIG_KOBJECT_TEXT_AREA=256
CONFIG_ARM_MPU=y
CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE=32
# CONFIG_MPU_ALLOW_FLASH_WRITE is not set

#
# ARM Options
#
CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK=y
CONFIG_ARM_ON_EXIT_CPU_IDLE=y
CONFIG_CPU_CORTEX_M=y
# CONFIG_ARM_ZIMAGE_HEADER is not set
CONFIG_ISA_THUMB2=y
CONFIG_ASSEMBLER_ISA_THUMB2=y
CONFIG_COMPILER_ISA_THUMB2=y
CONFIG_STACK_ALIGN_DOUBLE_WORD=y
# CONFIG_RUNTIME_NMI is not set
# CONFIG_PLATFORM_SPECIFIC_INIT is not set
CONFIG_FAULT_DUMP=2
CONFIG_BUILTIN_STACK_GUARD=y
CONFIG_ARM_STACK_PROTECTION=y
CONFIG_ARM_STORE_EXC_RETURN=y
CONFIG_FP_HARDABI=y
# CONFIG_FP_SOFTABI is not set
CONFIG_FP16=y
CONFIG_FP16_IEEE=y
# CONFIG_FP16_ALT is not set
CONFIG_CPU_CORTEX_M33=y
CONFIG_CPU_CORTEX_M_HAS_SYSTICK=y
CONFIG_CPU_CORTEX_M_HAS_DWT=y
CONFIG_CPU_CORTEX_M_HAS_BASEPRI=y
CONFIG_CPU_CORTEX_M_HAS_VTOR=y
CONFIG_CPU_CORTEX_M_HAS_SPLIM=y
CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS=y
CONFIG_CPU_CORTEX_M_HAS_CMSE=y
CONFIG_ARMV7_M_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_SE=y
CONFIG_ARMV7_M_ARMV8_M_FP=y
CONFIG_ARMV8_M_DSP=y

#
# ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33/M55 options
#
# CONFIG_ZERO_LATENCY_IRQS is not set
# CONFIG_SW_VECTOR_RELAY is not set
# CONFIG_CORTEX_M_DWT is not set
# CONFIG_CORTEX_M_DEBUG_MONITOR_HOOK is not set
# CONFIG_TRAP_UNALIGNED_ACCESS is not set
# end of ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33/M55 options

CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE=y
# CONFIG_NULL_POINTER_EXCEPTION_DETECTION_DWT is not set
# CONFIG_NULL_POINTER_EXCEPTION_DETECTION_MPU is not set
CONFIG_ARM_TRUSTZONE_M=y
# CONFIG_MPU_STACK_GUARD is not set
# CONFIG_MPU_DISABLE_BACKGROUND_MAP is not set
# CONFIG_CUSTOM_SECTION_ALIGN is not set
CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE=32
CONFIG_CPU_HAS_NRF_IDAU=y
CONFIG_HAS_SWO=y
# end of ARM Options

CONFIG_ARM=y
CONFIG_ARCH_IS_SET=y

#
# General Architecture Options
#
# CONFIG_SEMIHOST is not set
# CONFIG_ARCH_LOG_LEVEL_OFF is not set
# CONFIG_ARCH_LOG_LEVEL_ERR is not set
# CONFIG_ARCH_LOG_LEVEL_WRN is not set
# CONFIG_ARCH_LOG_LEVEL_INF is not set
# CONFIG_ARCH_LOG_LEVEL_DBG is not set
CONFIG_ARCH_LOG_LEVEL_DEFAULT=y
CONFIG_ARCH_LOG_LEVEL=3
CONFIG_LITTLE_ENDIAN=y
# CONFIG_TRUSTED_EXECUTION_SECURE is not set
# CONFIG_TRUSTED_EXECUTION_NONSECURE is not set
# CONFIG_USERSPACE is not set
CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT=100
CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES=16
CONFIG_GEN_PRIV_STACKS=y
# CONFIG_STACK_GROWS_UP is not set
# CONFIG_FRAME_POINTER is not set

#
# Interrupt Configuration
#
CONFIG_ISR_TABLES_LOCAL_DECLARATION_SUPPORTED=y
# CONFIG_ISR_TABLES_LOCAL_DECLARATION is not set
CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS=y
# CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_CODE is not set
CONFIG_EXCEPTION_DEBUG=y
# CONFIG_SIMPLIFIED_EXCEPTION_CODES is not set
# end of Interrupt Configuration
# end of General Architecture Options

CONFIG_ARCH_HAS_TIMING_FUNCTIONS=y
CONFIG_ARCH_HAS_TRUSTED_EXECUTION=y
CONFIG_ARCH_HAS_STACK_PROTECTION=y
CONFIG_ARCH_HAS_USERSPACE=y
CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT=y
CONFIG_ARCH_HAS_RAMFUNC_SUPPORT=y
CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION=y
CONFIG_ARCH_SUPPORTS_COREDUMP=y
CONFIG_ARCH_SUPPORTS_COREDUMP_THREADS=y
CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT=y
CONFIG_ARCH_SUPPORTS_ROM_START=y
CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO=y
CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE=y
CONFIG_ARCH_HAS_SUSPEND_TO_RAM=y
CONFIG_ARCH_HAS_THREAD_ABORT=y
CONFIG_ARCH_HAS_CODE_DATA_RELOCATION=y
CONFIG_CPU_HAS_TEE=y
CONFIG_CPU_HAS_FPU=y
CONFIG_CPU_HAS_MPU=y
CONFIG_MPU=y
# CONFIG_MPU_LOG_LEVEL_OFF is not set
# CONFIG_MPU_LOG_LEVEL_ERR is not set
# CONFIG_MPU_LOG_LEVEL_WRN is not set
# CONFIG_MPU_LOG_LEVEL_INF is not set
# CONFIG_MPU_LOG_LEVEL_DBG is not set
CONFIG_MPU_LOG_LEVEL_DEFAULT=y
CONFIG_MPU_LOG_LEVEL=3
CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS=y
CONFIG_MPU_GAP_FILLING=y
CONFIG_SRAM_REGION_PERMISSIONS=y

#
# DSP Options
#
# end of DSP Options

#
# Floating Point Options
#
CONFIG_FPU_SHARING=y
# end of Floating Point Options

#
# Cache Options
#
# end of Cache Options

CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS=y
CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN=y

#
# General Kernel Options
#
# CONFIG_KERNEL_LOG_LEVEL_OFF is not set
# CONFIG_KERNEL_LOG_LEVEL_ERR is not set
# CONFIG_KERNEL_LOG_LEVEL_WRN is not set
# CONFIG_KERNEL_LOG_LEVEL_INF is not set
# CONFIG_KERNEL_LOG_LEVEL_DBG is not set
CONFIG_KERNEL_LOG_LEVEL_DEFAULT=y
CONFIG_KERNEL_LOG_LEVEL=3
CONFIG_MULTITHREADING=y
CONFIG_NUM_COOP_PRIORITIES=16
CONFIG_NUM_PREEMPT_PRIORITIES=15
CONFIG_MAIN_THREAD_PRIORITY=0
CONFIG_COOP_ENABLED=y
CONFIG_PREEMPT_ENABLED=y
CONFIG_PRIORITY_CEILING=-127
# CONFIG_SCHED_DEADLINE is not set
CONFIG_THREAD_STACK_INFO=y
# CONFIG_THREAD_CUSTOM_DATA is not set
# CONFIG_DYNAMIC_THREAD is not set
CONFIG_SCHED_DUMB=y
# CONFIG_SCHED_SCALABLE is not set
# CONFIG_SCHED_MULTIQ is not set
# CONFIG_WAITQ_SCALABLE is not set
CONFIG_WAITQ_DUMB=y

#
# Misc Kernel related options
#
CONFIG_ERRNO=y
# end of Misc Kernel related options

#
# Kernel Debugging and Metrics
#
CONFIG_BOOT_DELAY=0
# CONFIG_BOOT_CLEAR_SCREEN is not set
# CONFIG_THREAD_MONITOR is not set
# CONFIG_THREAD_NAME is not set
# CONFIG_THREAD_RUNTIME_STATS is not set
# end of Kernel Debugging and Metrics

# CONFIG_OBJ_CORE is not set

#
# System Work Queue Options
#
# CONFIG_SYSTEM_WORKQUEUE_NO_YIELD is not set
# end of System Work Queue Options

#
# Barrier Operations
#
CONFIG_BARRIER_OPERATIONS_ARCH=y
# end of Barrier Operations

#
# Atomic Operations
#
CONFIG_ATOMIC_OPERATIONS_BUILTIN=y
# end of Atomic Operations

#
# Timer API Options
#
# CONFIG_TIMESLICING is not set
# end of Timer API Options

#
# Other Kernel Object Options
#
# CONFIG_POLL is not set
# CONFIG_MEM_SLAB_TRACE_MAX_UTILIZATION is not set
CONFIG_NUM_MBOX_ASYNC_MSGS=10
# CONFIG_EVENTS is not set
# CONFIG_PIPES is not set
CONFIG_KERNEL_MEM_POOL=y
# CONFIG_HEAP_MEM_POOL_IGNORE_MIN is not set
# end of Other Kernel Object Options

CONFIG_SWAP_NONATOMIC=y
CONFIG_SYS_CLOCK_EXISTS=y
CONFIG_TIMEOUT_64BIT=y
CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS=365

#
# Security Options
#
# end of Security Options

#
# Memory Domains
#
CONFIG_ARCH_MEM_DOMAIN_SUPPORTS_ISOLATED_STACKS=y
CONFIG_MEM_DOMAIN_ISOLATED_STACKS=y
# end of Memory Domains

#
# SMP Options
#
CONFIG_MP_NUM_CPUS=1
# CONFIG_TICKET_SPINLOCKS is not set
# end of SMP Options

CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE=y
# CONFIG_THREAD_LOCAL_STORAGE is not set
CONFIG_TOOLCHAIN_SUPPORTS_STATIC_INIT_GNU=y
# CONFIG_STATIC_INIT_GNU is not set
# CONFIG_BOOTARGS is not set
# end of General Kernel Options

#
# Device Options
#
# CONFIG_DEVICE_DEPS is not set
# CONFIG_DEVICE_MUTABLE is not set
# CONFIG_DEVICE_DT_METADATA is not set
# end of Device Options

#
# Initialization Priorities
#
CONFIG_KERNEL_INIT_PRIORITY_OBJECTS=30
CONFIG_KERNEL_INIT_PRIORITY_LIBC=35
CONFIG_KERNEL_INIT_PRIORITY_DEFAULT=40
CONFIG_KERNEL_INIT_PRIORITY_DEVICE=50
CONFIG_APPLICATION_INIT_PRIORITY=90
# end of Initialization Priorities

#
# Virtual Memory Support
#
# end of Virtual Memory Support

#
# SoC and Board Hooks
#
# CONFIG_SOC_PREP_HOOK is not set
# CONFIG_SOC_EARLY_INIT_HOOK is not set
# CONFIG_SOC_LATE_INIT_HOOK is not set
# CONFIG_BOARD_EARLY_INIT_HOOK is not set
# CONFIG_BOARD_LATE_INIT_HOOK is not set
# end of SoC and Board Hooks

#
# Device Drivers
#
# CONFIG_ADC is not set
# CONFIG_AUDIO is not set
# CONFIG_AUXDISPLAY is not set
# CONFIG_BBRAM is not set
# CONFIG_FLASH is not set
# CONFIG_CAN is not set
# CONFIG_CHARGER is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_OFF is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_ERR is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_WRN is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_INF is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_DBG is not set
CONFIG_CLOCK_CONTROL_LOG_LEVEL_DEFAULT=y
CONFIG_CLOCK_CONTROL_LOG_LEVEL=3
CONFIG_CLOCK_CONTROL_NRF=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_RC is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_SYNTH is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_500PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_250PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_150PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_100PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_75PPM is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_30PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_20PPM is not set
CONFIG_CLOCK_CONTROL_NRF_ACCURACY=50
# CONFIG_CLOCK_CONTROL_FIXED_RATE_CLOCK is not set
# CONFIG_COMPARATOR is not set
CONFIG_CONSOLE_INPUT_MAX_LINE_LEN=128
CONFIG_CONSOLE_HAS_DRIVER=y
# CONFIG_CONSOLE_HANDLER is not set
CONFIG_CONSOLE_INIT_PRIORITY=60
CONFIG_UART_CONSOLE=y
# CONFIG_UART_CONSOLE_DEBUG_SERVER_HOOKS is not set
# CONFIG_UART_CONSOLE_MCUMGR is not set
# CONFIG_RAM_CONSOLE is not set
# CONFIG_IPM_CONSOLE_SENDER is not set
# CONFIG_IPM_CONSOLE_RECEIVER is not set
# CONFIG_UART_MCUMGR is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_OFF is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_ERR is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_WRN is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_INF is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_DBG is not set
CONFIG_UART_CONSOLE_LOG_LEVEL_DEFAULT=y
CONFIG_UART_CONSOLE_LOG_LEVEL=3
# CONFIG_EFI_CONSOLE is not set
# CONFIG_COREDUMP_DEVICE is not set
# CONFIG_CRYPTO is not set
# CONFIG_DAC is not set
# CONFIG_DAI is not set
# CONFIG_DISK_DRIVERS is not set
# CONFIG_DISPLAY is not set
# CONFIG_DMA is not set
# CONFIG_DP_DRIVER is not set
# CONFIG_EDAC is not set
# CONFIG_EEPROM is not set
# CONFIG_ENTROPY_GENERATOR is not set
# CONFIG_ESPI is not set

#
# Firmware drivers
#
# CONFIG_ARM_SCMI is not set
# end of Firmware drivers

# CONFIG_FPGA is not set
# CONFIG_FUEL_GAUGE is not set
# CONFIG_GNSS is not set
# CONFIG_GPIO_LOG_LEVEL_OFF is not set
# CONFIG_GPIO_LOG_LEVEL_ERR is not set
# CONFIG_GPIO_LOG_LEVEL_WRN is not set
# CONFIG_GPIO_LOG_LEVEL_INF is not set
# CONFIG_GPIO_LOG_LEVEL_DBG is not set
CONFIG_GPIO_LOG_LEVEL_DEFAULT=y
CONFIG_GPIO_LOG_LEVEL=3
# CONFIG_GPIO_GET_DIRECTION is not set
# CONFIG_GPIO_GET_CONFIG is not set
# CONFIG_GPIO_HOGS is not set
# CONFIG_GPIO_ENABLE_DISABLE_INTERRUPT is not set
CONFIG_GPIO_NRFX=y
CONFIG_GPIO_NRFX_INTERRUPT=y
# CONFIG_HAPTICS is not set
# CONFIG_HWINFO is not set
# CONFIG_HWSPINLOCK is not set
# CONFIG_I2S is not set
# CONFIG_I3C is not set

#
# Interrupt controller drivers
#
CONFIG_INTC_INIT_PRIORITY=40
# CONFIG_INTC_LOG_LEVEL_OFF is not set
# CONFIG_INTC_LOG_LEVEL_ERR is not set
# CONFIG_INTC_LOG_LEVEL_WRN is not set
# CONFIG_INTC_LOG_LEVEL_INF is not set
# CONFIG_INTC_LOG_LEVEL_DBG is not set
CONFIG_INTC_LOG_LEVEL_DEFAULT=y
CONFIG_INTC_LOG_LEVEL=3
# end of Interrupt controller drivers

# CONFIG_IPM is not set
# CONFIG_KSCAN is not set
# CONFIG_LED is not set
# CONFIG_LED_STRIP is not set
# CONFIG_LORA is not set
CONFIG_MBOX=y
CONFIG_MBOX_NRFX_IPC=y
CONFIG_MBOX_INIT_PRIORITY=40
# CONFIG_MBOX_LOG_LEVEL_OFF is not set
# CONFIG_MBOX_LOG_LEVEL_ERR is not set
# CONFIG_MBOX_LOG_LEVEL_WRN is not set
# CONFIG_MBOX_LOG_LEVEL_INF is not set
# CONFIG_MBOX_LOG_LEVEL_DBG is not set
CONFIG_MBOX_LOG_LEVEL_DEFAULT=y
CONFIG_MBOX_LOG_LEVEL=3
# CONFIG_MDIO is not set
# CONFIG_MIPI_DBI is not set

#
# Miscellaneous Drivers
#
# CONFIG_TIMEAWARE_GPIO is not set
# end of Miscellaneous Drivers

# CONFIG_MM_DRV is not set
# CONFIG_MSPI is not set
# CONFIG_PCIE is not set
# CONFIG_PCIE_ENDPOINT is not set
# CONFIG_PECI is not set
# CONFIG_PINCTRL_LOG_LEVEL_OFF is not set
# CONFIG_PINCTRL_LOG_LEVEL_ERR is not set
# CONFIG_PINCTRL_LOG_LEVEL_WRN is not set
# CONFIG_PINCTRL_LOG_LEVEL_INF is not set
# CONFIG_PINCTRL_LOG_LEVEL_DBG is not set
CONFIG_PINCTRL_LOG_LEVEL_DEFAULT=y
CONFIG_PINCTRL_LOG_LEVEL=3
CONFIG_PINCTRL_STORE_REG=y
# CONFIG_PINCTRL_DYNAMIC is not set
CONFIG_PINCTRL_NRF=y
# CONFIG_PM_CPU_OPS is not set
# CONFIG_PS2 is not set
# CONFIG_PTP_CLOCK is not set
# CONFIG_PWM is not set
# CONFIG_RETAINED_MEM is not set
# CONFIG_RTC is not set
# CONFIG_SDHC is not set

#
# Capabilities
#
CONFIG_SERIAL_HAS_DRIVER=y
CONFIG_SERIAL_SUPPORT_ASYNC=y
CONFIG_SERIAL_SUPPORT_INTERRUPT=y
# CONFIG_UART_LOG_LEVEL_OFF is not set
# CONFIG_UART_LOG_LEVEL_ERR is not set
# CONFIG_UART_LOG_LEVEL_WRN is not set
# CONFIG_UART_LOG_LEVEL_INF is not set
# CONFIG_UART_LOG_LEVEL_DBG is not set
CONFIG_UART_LOG_LEVEL_DEFAULT=y
CONFIG_UART_LOG_LEVEL=3
# CONFIG_UART_LINE_CTRL is not set
# CONFIG_UART_DRV_CMD is not set
# CONFIG_UART_WIDE_DATA is not set
# CONFIG_UART_PIPE is not set
# CONFIG_UART_ASYNC_RX_HELPER is not set

#
# Serial Drivers
#
CONFIG_UART_NRFX=y
CONFIG_UART_NRFX_UARTE=y
CONFIG_UART_NRFX_UARTE_LEGACY_SHIM=y
CONFIG_UART_0_ENHANCED_POLL_OUT=y
# CONFIG_UART_0_NRF_PARITY_BIT is not set
CONFIG_UART_0_NRF_TX_BUFFER_SIZE=32
# CONFIG_SMBUS is not set
# CONFIG_STEPPER is not set

#
# Timer drivers
#
# CONFIG_TIMER_READS_ITS_FREQUENCY_AT_RUNTIME is not set
# CONFIG_SYSTEM_CLOCK_SLOPPY_IDLE is not set
CONFIG_SYSTEM_CLOCK_INIT_PRIORITY=0
CONFIG_TICKLESS_CAPABLE=y
CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT=y
# CONFIG_NRF_RTC_TIMER_TRIGGER_OVERFLOW is not set
# CONFIG_SYSTEM_CLOCK_NO_WAIT is not set
# CONFIG_SYSTEM_CLOCK_WAIT_FOR_AVAILABILITY is not set
CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY=y
# end of Timer drivers

# CONFIG_USB_BC12 is not set
# CONFIG_UDC_DRIVER is not set
# CONFIG_UHC_DRIVER is not set
# CONFIG_UVB is not set
# CONFIG_USB_DEVICE_DRIVER is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_OFF is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_ERR is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_WRN is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_INF is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_DBG is not set
CONFIG_NRF_USBD_COMMON_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_USBD_COMMON_LOG_LEVEL=3
# CONFIG_NRF_USBD_COMMON is not set
# CONFIG_USBC_TCPC_DRIVER is not set
# CONFIG_USBC_LOG_LEVEL_OFF is not set
# CONFIG_USBC_LOG_LEVEL_ERR is not set
# CONFIG_USBC_LOG_LEVEL_WRN is not set
# CONFIG_USBC_LOG_LEVEL_INF is not set
# CONFIG_USBC_LOG_LEVEL_DBG is not set
CONFIG_USBC_LOG_LEVEL_DEFAULT=y
CONFIG_USBC_LOG_LEVEL=3
# CONFIG_USBC_VBUS_DRIVER is not set
# CONFIG_USBC_PPC_DRIVER is not set
# CONFIG_VIDEO is not set
# CONFIG_VIRTUALIZATION is not set
# CONFIG_W1 is not set
# CONFIG_TEE is not set
# end of Device Drivers

# CONFIG_REQUIRES_FULL_LIBC is not set
# CONFIG_REQUIRES_FLOAT_PRINTF is not set
CONFIG_FULL_LIBC_SUPPORTED=y
CONFIG_MINIMAL_LIBC_SUPPORTED=y
CONFIG_NEWLIB_LIBC_SUPPORTED=y
CONFIG_PICOLIBC_SUPPORTED=y

#
# C Library
#
# CONFIG_MINIMAL_LIBC is not set
# CONFIG_PICOLIBC is not set
CONFIG_NEWLIB_LIBC=y
# CONFIG_EXTERNAL_LIBC is not set
CONFIG_HAS_NEWLIB_LIBC_NANO=y
CONFIG_COMMON_LIBC_ABORT=y
# CONFIG_COMMON_LIBC_ASCTIME_R is not set
# CONFIG_COMMON_LIBC_CTIME_R is not set
# CONFIG_COMMON_LIBC_MALLOC is not set
# CONFIG_COMMON_LIBC_REMOVE is not set
CONFIG_NEWLIB_LIBC_MIN_REQUIRED_HEAP_SIZE=2048
CONFIG_NEWLIB_LIBC_FLOAT_PRINTF=y
# CONFIG_NEWLIB_LIBC_FLOAT_SCANF is not set
# CONFIG_NEWLIB_LIBC_HEAP_LISTENER is not set
# CONFIG_NEWLIB_LIBC_CUSTOM_SBRK is not set
CONFIG_STDOUT_CONSOLE=y
CONFIG_NEED_LIBC_MEM_PARTITION=y
# end of C Library

#
# C++ Language Support
#
# CONFIG_CPP is not set
# end of C++ Language Support

CONFIG_CRC=y

#
# Additional libraries
#

#
# Hash Function Support
#
# CONFIG_SYS_HASH_FUNC32 is not set
# end of Hash Function Support

#
# Hashmap (Hash Table) Support
#
# CONFIG_SYS_HASH_MAP is not set
# end of Hashmap (Hash Table) Support

#
# Heap and Memory Allocation
#
# CONFIG_SYS_HEAP_STRESS is not set
# CONFIG_SYS_HEAP_INFO is not set
CONFIG_SYS_HEAP_ALLOC_LOOPS=3
# CONFIG_SYS_HEAP_RUNTIME_STATS is not set
# CONFIG_SYS_HEAP_LISTENER is not set
# CONFIG_SYS_HEAP_SMALL_ONLY is not set
# CONFIG_SYS_HEAP_BIG_ONLY is not set
CONFIG_SYS_HEAP_AUTO=y
# CONFIG_MULTI_HEAP is not set
# CONFIG_SHARED_MULTI_HEAP is not set
# end of Heap and Memory Allocation

#
# Memory Blocks
#
# CONFIG_SYS_MEM_BLOCKS is not set
# end of Memory Blocks

# CONFIG_NET_BUF is not set

#
# OS Support Library
#
# CONFIG_FDTABLE is not set
CONFIG_ZVFS_OPEN_MAX=4
# CONFIG_PRINTK_SYNC is not set
CONFIG_MPSC_PBUF=y
# CONFIG_SPSC_PBUF is not set
# CONFIG_MPSC_CLEAR_ALLOCATED is not set
CONFIG_HAS_POWEROFF=y
CONFIG_POWEROFF=y
CONFIG_CBPRINTF_COMPLETE=y
# CONFIG_CBPRINTF_NANO is not set
CONFIG_CBPRINTF_FULL_INTEGRAL=y
# CONFIG_CBPRINTF_REDUCED_INTEGRAL is not set
CONFIG_CBPRINTF_FP_SUPPORT=y
# CONFIG_CBPRINTF_FP_A_SUPPORT is not set
# CONFIG_CBPRINTF_FP_ALWAYS_A is not set
CONFIG_CBPRINTF_N_SPECIFIER=y
# CONFIG_CBPRINTF_LIBC_SUBSTS is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_OFF is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_ERR is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_WRN is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_INF is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DBG is not set
CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DEFAULT=y
CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL=3
# CONFIG_CBPRINTF_PACKAGE_LONGDOUBLE is not set
CONFIG_CBPRINTF_CONVERT_CHECK_PTR=y
# CONFIG_ZVFS is not set
# end of OS Support Library

#
# POSIX API Support
#

#
# POSIX Options
#
# CONFIG_POSIX_API is not set
CONFIG_POSIX_AEP_CHOICE_NONE=y
# CONFIG_POSIX_AEP_CHOICE_BASE is not set
# CONFIG_POSIX_AEP_CHOICE_PSE51 is not set
# CONFIG_POSIX_AEP_CHOICE_PSE52 is not set
# CONFIG_POSIX_AEP_CHOICE_PSE53 is not set
# CONFIG_POSIX_ASYNCHRONOUS_IO is not set
# CONFIG_POSIX_BARRIERS is not set
# CONFIG_POSIX_C_LANG_SUPPORT_R is not set
# CONFIG_POSIX_C_LIB_EXT is not set

#
# POSIX device I/O
#
# CONFIG_POSIX_DEVICE_IO is not set
CONFIG_POSIX_OPEN_MAX=4
# end of POSIX device I/O

# CONFIG_POSIX_FD_MGMT is not set
# CONFIG_POSIX_FILE_SYSTEM is not set

#
# POSIX memory
#
CONFIG_POSIX_PAGE_SIZE=0x40
# CONFIG_POSIX_SHARED_MEMORY_OBJECTS is not set
# CONFIG_POSIX_MAPPED_FILES is not set
# CONFIG_POSIX_MEMORY_PROTECTION is not set
# end of POSIX memory

# CONFIG_POSIX_MESSAGE_PASSING is not set
# CONFIG_POSIX_SINGLE_PROCESS is not set
# CONFIG_POSIX_MULTI_PROCESS is not set
# CONFIG_POSIX_THREADS is not set
# CONFIG_POSIX_READER_WRITER_LOCKS is not set

#
# POSIX scheduler options
#
# CONFIG_POSIX_PRIORITY_SCHEDULING is not set
# end of POSIX scheduler options

# CONFIG_POSIX_SEMAPHORES is not set

#
# POSIX signals
#
# CONFIG_POSIX_REALTIME_SIGNALS is not set
# CONFIG_POSIX_SIGNALS is not set
# end of POSIX signals

# CONFIG_POSIX_SPIN_LOCKS is not set

#
# POSIX synchronized I/O
#
# CONFIG_POSIX_FSYNC is not set
# CONFIG_POSIX_SYNCHRONIZED_IO is not set
# end of POSIX synchronized I/O

# CONFIG_POSIX_TIMERS is not set

#
# X/Open system interfaces
#
# CONFIG_XOPEN_STREAMS is not set
# CONFIG_XSI_SYSTEM_LOGGING is not set
# CONFIG_XSI_THREADS_EXT is not set
# end of X/Open system interfaces

#
# Miscellaneous POSIX-related options
#
# CONFIG_EVENTFD is not set
# end of Miscellaneous POSIX-related options

#
# Deprecated POSIX options
#
CONFIG_EVENTFD_MAX=0
# CONFIG_FNMATCH is not set
CONFIG_MAX_PTHREAD_COUNT=0
CONFIG_MAX_PTHREAD_KEY_COUNT=0
CONFIG_MAX_TIMER_COUNT=0
CONFIG_MSG_COUNT_MAX=0
# CONFIG_POSIX_CLOCK is not set
# CONFIG_POSIX_FS is not set
CONFIG_POSIX_LIMITS_RTSIG_MAX=0
CONFIG_POSIX_MAX_FDS=4
CONFIG_POSIX_MAX_OPEN_FILES=4
# CONFIG_POSIX_MQUEUE is not set
# CONFIG_POSIX_PUTMSG is not set
# CONFIG_POSIX_SIGNAL is not set
# CONFIG_POSIX_SYSCONF is not set
# CONFIG_POSIX_UNAME is not set
# CONFIG_PTHREAD is not set
# CONFIG_PTHREAD_BARRIER is not set
# CONFIG_PTHREAD_COND is not set
# CONFIG_PTHREAD_IPC is not set
# CONFIG_PTHREAD_KEY is not set
# CONFIG_PTHREAD_MUTEX is not set
# CONFIG_PTHREAD_RWLOCK is not set
# CONFIG_PTHREAD_SPINLOCK is not set
# CONFIG_TIMER is not set
CONFIG_TIMER_DELAYTIMER_MAX=0
CONFIG_SEM_NAMELEN_MAX=0
CONFIG_SEM_VALUE_MAX=0
# end of Deprecated POSIX options
# end of POSIX Options

#
# POSIX Shell Utilities
#
# end of POSIX Shell Utilities
# end of POSIX API Support

# CONFIG_OPENAMP_RSC_TABLE is not set
# CONFIG_SMF is not set
CONFIG_LIBGCC_RTLIB=y

#
# Utility Library
#
# CONFIG_JSON_LIBRARY is not set
# CONFIG_RING_BUFFER is not set
CONFIG_NOTIFY=y
# CONFIG_BASE64 is not set
CONFIG_ONOFF=y
# CONFIG_WINSTREAM is not set
# CONFIG_UTF8 is not set
# end of Utility Library
# end of Additional libraries

#
# Subsystems and OS Services
#
# CONFIG_BINDESC is not set
# CONFIG_BT is not set

#
# Controller Area Network (CAN) bus subsystem
#
# end of Controller Area Network (CAN) bus subsystem

# CONFIG_CONSOLE_SUBSYS is not set
# CONFIG_DAP is not set

#
# System Monitoring Options
#
# CONFIG_THREAD_ANALYZER is not set
# end of System Monitoring Options

#
# Debugging Options
#
# CONFIG_DEBUG is not set
# CONFIG_STACK_USAGE is not set
# CONFIG_STACK_SENTINEL is not set
CONFIG_PRINTK=y
CONFIG_EARLY_CONSOLE=y
# CONFIG_FORCE_NO_ASSERT is not set
CONFIG_ASSERT_VERBOSE=y
# CONFIG_ASSERT_NO_FILE_INFO is not set
# CONFIG_ASSERT_NO_COND_INFO is not set
# CONFIG_ASSERT_NO_MSG_INFO is not set
# CONFIG_ASSERT_TEST is not set
# CONFIG_OVERRIDE_FRAME_POINTER_DEFAULT is not set
# CONFIG_DEBUG_INFO is not set
# CONFIG_DEBUG_THREAD_INFO is not set
# CONFIG_DEBUG_COREDUMP is not set
# CONFIG_SYMTAB is not set
# end of Debugging Options

# CONFIG_MIPI_STP_DECODER is not set
# CONFIG_CS_TRACE_DEFMT is not set
# CONFIG_DISK_ACCESS is not set
# CONFIG_DSP is not set
# CONFIG_EMUL is not set
# CONFIG_CHARACTER_FRAMEBUFFER is not set

#
# File Systems
#
# CONFIG_FILE_SYSTEM is not set
# CONFIG_ZMS is not set
# end of File Systems

#
# Inter Processor Communication
#
# CONFIG_RPMSG_SERVICE is not set
# CONFIG_IPC_SERVICE is not set
# end of Inter Processor Communication

# CONFIG_JWT is not set
# CONFIG_LLEXT is not set

#
# Linkable loadable Extension Development Kit (EDK)
#
CONFIG_LLEXT_EDK_NAME="llext-edk"
# CONFIG_LLEXT_EDK_USERSPACE_ONLY is not set
# end of Linkable loadable Extension Development Kit (EDK)

#
# Logging
#
CONFIG_LOG_CORE_INIT_PRIORITY=0
CONFIG_LOG_MODE_DEFERRED=y
# CONFIG_LOG_MODE_IMMEDIATE is not set
# CONFIG_LOG_MODE_MINIMAL is not set
# CONFIG_LOG_FRONTEND is not set
# CONFIG_LOG_FRONTEND_OPT_API is not set
# CONFIG_LOG_CUSTOM_HEADER is not set
# CONFIG_LOG_MULTIDOMAIN is not set

#
# Logging levels filtering
#
# CONFIG_LOG_RUNTIME_FILTERING is not set
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_OVERRIDE_LEVEL=0
CONFIG_LOG_MAX_LEVEL=4
# end of Logging levels filtering

#
# Processing
#
CONFIG_LOG_PRINTK=y
CONFIG_LOG_MODE_OVERFLOW=y
# CONFIG_LOG_BLOCK_IN_THREAD is not set
CONFIG_LOG_PROCESS_TRIGGER_THRESHOLD=10
CONFIG_LOG_PROCESS_THREAD=y
CONFIG_LOG_PROCESS_THREAD_STARTUP_DELAY_MS=0
CONFIG_LOG_PROCESS_THREAD_SLEEP_MS=1000
CONFIG_LOG_PROCESS_THREAD_STACK_SIZE=768
# CONFIG_LOG_PROCESS_THREAD_CUSTOM_PRIORITY is not set
CONFIG_LOG_TRACE_SHORT_TIMESTAMP=y
# CONFIG_LOG_TIMESTAMP_64BIT is not set
# CONFIG_LOG_SPEED is not set
# end of Processing

#
# Output Formatting
#

#
# Prepend non-hexdump log message with function name
#
# CONFIG_LOG_FUNC_NAME_PREFIX_ERR is not set
# CONFIG_LOG_FUNC_NAME_PREFIX_WRN is not set
# CONFIG_LOG_FUNC_NAME_PREFIX_INF is not set
CONFIG_LOG_FUNC_NAME_PREFIX_DBG=y
# end of Prepend non-hexdump log message with function name

# CONFIG_LOG_MIPI_SYST_ENABLE is not set
# CONFIG_LOG_THREAD_ID_PREFIX is not set
# CONFIG_LOG_CUSTOM_FORMAT_SUPPORT is not set
CONFIG_LOG_BACKEND_SHOW_COLOR=y
# CONFIG_LOG_INFO_COLOR_GREEN is not set
# CONFIG_LOG_DBG_COLOR_BLUE is not set
CONFIG_LOG_TAG_MAX_LEN=0
CONFIG_LOG_BACKEND_SUPPORTS_FORMAT_TIMESTAMP=y
CONFIG_LOG_BACKEND_FORMAT_TIMESTAMP=y
# CONFIG_LOG_OUTPUT_FORMAT_LINUX_TIMESTAMP is not set
# CONFIG_LOG_OUTPUT_FORMAT_CUSTOM_TIMESTAMP is not set
# end of Output Formatting

#
# Backends
#
# CONFIG_LOG_BACKEND_SWO is not set
CONFIG_LOG_BACKEND_UART=y
CONFIG_LOG_BACKEND_UART_BUFFER_SIZE=1
CONFIG_LOG_BACKEND_UART_AUTOSTART=y
CONFIG_LOG_BACKEND_UART_OUTPUT_TEXT=y
# CONFIG_LOG_BACKEND_UART_OUTPUT_DICTIONARY is not set
# CONFIG_LOG_BACKEND_UART_OUTPUT_CUSTOM is not set
CONFIG_LOG_BACKEND_UART_OUTPUT_DEFAULT=0
# CONFIG_LOG_BACKEND_IPC_SERVICE is not set
# end of Backends

#
# Misc
#
CONFIG_LOG_USE_VLA=y
CONFIG_LOG_SIMPLE_MSG_OPTIMIZE=y
# CONFIG_LOG_ALWAYS_RUNTIME is not set
# CONFIG_LOG_FMT_SECTION is not set
# CONFIG_LOG_FMT_STRING_VALIDATE is not set
# CONFIG_LOG_USE_TAGGED_ARGUMENTS is not set
# CONFIG_LOG_MEM_UTILIZATION is not set
CONFIG_LOG_FAILURE_REPORT_PERIOD=1000
# end of Misc

CONFIG_LOG_OUTPUT=y
# end of Logging

CONFIG_MEM_ATTR=y
# CONFIG_MEM_ATTR_HEAP is not set

#
# Device Management
#

#
# Host command handler subsystem
#
# CONFIG_EC_HOST_CMD is not set
# end of Host command handler subsystem

# CONFIG_OSDP is not set
# end of Device Management

# CONFIG_MODBUS is not set
# CONFIG_MODEM_MODULES is not set

#
# Networking
#
# CONFIG_NETWORKING is not set
# end of Networking

#
# Power Management
#
# CONFIG_PM_POLICY_LATENCY_STANDALONE is not set
# CONFIG_PM_DEVICE_LOG_LEVEL_OFF is not set
# CONFIG_PM_DEVICE_LOG_LEVEL_ERR is not set
# CONFIG_PM_DEVICE_LOG_LEVEL_WRN is not set
# CONFIG_PM_DEVICE_LOG_LEVEL_INF is not set
# CONFIG_PM_DEVICE_LOG_LEVEL_DBG is not set
CONFIG_PM_DEVICE_LOG_LEVEL_DEFAULT=y
CONFIG_PM_DEVICE_LOG_LEVEL=3
# CONFIG_PM_DEVICE_RUNTIME is not set
# end of Power Management

#
# Portability
#
# end of Portability

# CONFIG_PROFILING is not set

#
# Random Number Generators
#
# CONFIG_TEST_RANDOM_GENERATOR is not set
CONFIG_TIMER_RANDOM_INITIAL_STATE=123456789
# end of Random Number Generators

# CONFIG_RTIO is not set

#
# SD
#
# CONFIG_MMC_STACK is not set
# CONFIG_SDMMC_STACK is not set
# CONFIG_SDIO_STACK is not set
# end of SD

# CONFIG_SETTINGS is not set
# CONFIG_SHELL is not set
# CONFIG_STATS is not set

#
# Storage
#
# CONFIG_STREAM_FLASH is not set
# end of Storage

# CONFIG_TASK_WDT is not set

#
# Testing
#
# CONFIG_ZTEST is not set
# CONFIG_ZTEST_MOCKING is not set
# CONFIG_ZTRESS is not set
# CONFIG_TEST is not set
# CONFIG_FORCE_COVERAGE is not set
# CONFIG_TEST_USERSPACE is not set
# end of Testing

# CONFIG_TIMING_FUNCTIONS is not set
# CONFIG_TRACING is not set
# CONFIG_USB_DEVICE_STACK is not set
# CONFIG_USB_DEVICE_STACK_NEXT is not set
# CONFIG_USB_HOST_STACK is not set
# CONFIG_USBC_STACK is not set
# CONFIG_ZBUS is not set
# CONFIG_MODULES is not set
# end of Subsystems and OS Services

CONFIG_TOOLCHAIN_ZEPHYR_0_17=y
CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE=y
CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_GNU_EXTENSIONS=y

#
# Build and Link Features
#

#
# Linker Options
#
# CONFIG_LINKER_ORPHAN_SECTION_PLACE is not set
CONFIG_LINKER_ORPHAN_SECTION_WARN=y
# CONFIG_LINKER_ORPHAN_SECTION_ERROR is not set
CONFIG_ROM_END_OFFSET=0
CONFIG_LD_LINKER_SCRIPT_SUPPORTED=y
CONFIG_LD_LINKER_TEMPLATE=y
# CONFIG_CMAKE_LINKER_GENERATOR is not set
# CONFIG_HAVE_CUSTOM_LINKER_SCRIPT is not set
CONFIG_LINKER_SORT_BY_ALIGNMENT=y

#
# Linker Sections
#
# CONFIG_LINKER_USE_BOOT_SECTION is not set
# CONFIG_LINKER_USE_PINNED_SECTION is not set
CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT=y
CONFIG_LINKER_LAST_SECTION_ID=y
CONFIG_LINKER_LAST_SECTION_ID_PATTERN=0xE015E015
CONFIG_LINKER_USE_RELAX=y
# end of Linker Sections

CONFIG_LINKER_ITERABLE_SUBALIGN=4
CONFIG_LINKER_DEVNULL_SUPPORT=y
# CONFIG_LINKER_DEVNULL_MEMORY is not set
# end of Linker Options

#
# Compiler Options
#
# CONFIG_STD_C90 is not set
CONFIG_STD_C99=y
# CONFIG_STD_C11 is not set
# CONFIG_STD_C17 is not set
# CONFIG_STD_C23 is not set
CONFIG_TOOLCHAIN_SUPPORTS_GNU_EXTENSIONS=y
# CONFIG_GNU_C_EXTENSIONS is not set
# CONFIG_CODING_GUIDELINE_CHECK is not set
# CONFIG_COMPILER_FREESTANDING is not set
CONFIG_SIZE_OPTIMIZATIONS=y
# CONFIG_SIZE_OPTIMIZATIONS_AGGRESSIVE is not set
# CONFIG_SPEED_OPTIMIZATIONS is not set
# CONFIG_DEBUG_OPTIMIZATIONS is not set
# CONFIG_NO_OPTIMIZATIONS is not set
# CONFIG_COMPILER_WARNINGS_AS_ERRORS is not set
# CONFIG_COMPILER_SAVE_TEMPS is not set
CONFIG_COMPILER_TRACK_MACRO_EXPANSION=y
CONFIG_COMPILER_COLOR_DIAGNOSTICS=y
# CONFIG_FORTIFY_SOURCE_NONE is not set
CONFIG_FORTIFY_SOURCE_COMPILE_TIME=y
# CONFIG_FORTIFY_SOURCE_RUN_TIME is not set
CONFIG_COMPILER_OPT=""
# CONFIG_MISRA_SANE is not set
# end of Compiler Options

# CONFIG_ASSERT_ON_ERRORS is not set
# CONFIG_NO_RUNTIME_CHECKS is not set
CONFIG_RUNTIME_ERROR_CHECKS=y

#
# Build Options
#
CONFIG_KERNEL_BIN_NAME="zephyr"
CONFIG_OUTPUT_STAT=y
# CONFIG_OUTPUT_SYMBOLS is not set
# CONFIG_OUTPUT_DISASSEMBLY is not set
CONFIG_OUTPUT_PRINT_MEMORY_USAGE=y
# CONFIG_CLEANUP_INTERMEDIATE_FILES is not set
CONFIG_BUILD_GAP_FILL_PATTERN=0xFF
# CONFIG_BUILD_OUTPUT_HEX_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_EXE is not set
# CONFIG_BUILD_OUTPUT_S19 is not set
# CONFIG_BUILD_OUTPUT_S19_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_UF2 is not set
# CONFIG_BUILD_OUTPUT_STRIPPED is not set
# CONFIG_BUILD_OUTPUT_COMPRESS_DEBUG_SECTIONS is not set
# CONFIG_BUILD_ALIGN_LMA is not set
# CONFIG_APPLICATION_DEFINED_SYSCALL is not set
# CONFIG_MAKEFILE_EXPORTS is not set
# CONFIG_BUILD_OUTPUT_META is not set
CONFIG_BUILD_OUTPUT_STRIP_PATHS=y
CONFIG_CHECK_INIT_PRIORITIES=y
# CONFIG_EMIT_ALL_SYSCALLS is not set
# end of Build Options

CONFIG_WARN_DEPRECATED=y
CONFIG_ENFORCE_ZEPHYR_STDINT=y
# end of Build and Link Features

#
# Boot Options
#
# CONFIG_IS_BOOTLOADER is not set
# CONFIG_BOOTLOADER_BOSSA is not set
# end of Boot Options

#
# Compatibility
#
CONFIG_LEGACY_GENERATED_INCLUDE_PATH=y
# end of Compatibility
# end of Zephyr Kernel

# CONFIG_NUS_CMD_LOG_LEVEL_OFF is not set
# CONFIG_NUS_CMD_LOG_LEVEL_ERR is not set
# CONFIG_NUS_CMD_LOG_LEVEL_WRN is not set
# CONFIG_NUS_CMD_LOG_LEVEL_INF is not set
# CONFIG_NUS_CMD_LOG_LEVEL_DBG is not set
CONFIG_NUS_CMD_LOG_LEVEL_DEFAULT=y
CONFIG_NUS_CMD_LOG_LEVEL=3
CONFIG_LIGHT_SWITCH_CONFIGURE_TX_POWER=y
CONFIG_LIGHT_SWITCH_TX_POWER=3
