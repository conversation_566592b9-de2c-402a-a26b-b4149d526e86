/*
 * SW2600 Hybrid Solution: GPIO I2C + Correct M117 Protocol
 * Combines low power (GPIO I2C) with working temperature reading
 * Target: 3.5µA sleep current with working M117 temperature
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/i2s.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <math.h>
#include <stdlib.h>

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* GPIO I2C pins - M117 connections */
static const struct gpio_dt_spec gpio_sda = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio0)),
    .pin = 24,  // P0.24 - SDA pin (M117 connection)
    .dt_flags = GPIO_ACTIVE_HIGH
};
static const struct gpio_dt_spec gpio_scl = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 8,   // P1.08 - SCL pin (M117 connection)
    .dt_flags = GPIO_ACTIVE_HIGH
};

/* M117 Configuration - Correct Protocol */
#define M117_I2C_ADDR           0x44    // Correct address
#define M117_CMD_CONVERT_T      0xCC44  // Temperature conversion command
#define M117_CMD_READ_STATUS    0xF32D  // Read status command
#define M117_CMD_CLEAR_STATUS   0x3041  // Clear status command

/* T5848 Microphone Configuration */
#define I2S_BLOCK_SIZE            1600   // 25ms of data: 16000Hz * 2ch * 2byte * 0.025s
#define AUDIO_BUF_COUNT           4      // We only need a few buffers for synchronous reading
#define I2S_TIMEOUT_MS            2000   // Longer timeout for synchronous read

/* Audio Analysis Configuration */
#define AMPLITUDE_FULL_SCALE_16BIT 32768.0f
#define MIN_AMPLITUDE              0.0001f
#define SPL_CALIBRATION_OFFSET     119.0f

/* Measurement Configuration */
#define NUM_BLOCKS_TO_DISCARD      4     // Discard first 100ms for stabilization
#define NUM_BLOCKS_TO_COLLECT      20    // Collect 20 blocks for a 0.5s measurement
#define NUM_DBFS_VALUES_TO_TRIM    2     // Trim the 2 lowest and 2 highest dBFS values
#define SAMPLES_PER_BLOCK          (1600 / 4) // 1600 bytes / (2ch*2byte/sample) = 400 samples/ch

K_MEM_SLAB_DEFINE(mic_slab, I2S_BLOCK_SIZE, AUDIO_BUF_COUNT, 4);

/* Static buffer for audio processing to avoid stack allocation */
static int16_t audio_processing_buffer[SAMPLES_PER_BLOCK];
static float dbfs_processing_buffer[NUM_BLOCKS_TO_COLLECT];

/* Microphone power control */
static const struct gpio_dt_spec mic_vdd = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 4,   // P1.04 - MIC-VDD pin
    .dt_flags = GPIO_ACTIVE_HIGH
};

/* Audio analysis helper functions */
static int float_compare(const void *a, const void *b)
{
    float fa = *(const float*) a;
    float fb = *(const float*) b;
    if (fa < fb) {
        return -1;
    }
    if (fa > fb) {
        return 1;
    }
    return 0;
}

static float calculate_block_dbfs(const int16_t *samples, uint32_t count)
{
    if (count == 0) {
        return -120.0f;
    }

    /* DC offset removal */
    int64_t sum = 0;
    for (uint32_t i = 0; i < count; i++) {
        sum += samples[i];
    }
    int16_t dc_offset = sum / count;

    /* RMS calculation */
    uint64_t sum_sq = 0;
    for (uint32_t i = 0; i < count; i++) {
        int32_t sample_value = samples[i] - dc_offset;
        sum_sq += (uint64_t)sample_value * sample_value;
    }

    if (sum_sq == 0) {
        return -120.0f;
    }

    float rms = sqrtf((float)sum_sq / count);
    rms = (rms > MIN_AMPLITUDE) ? rms : MIN_AMPLITUDE;

    return 20.0f * log10f(rms / AMPLITUDE_FULL_SCALE_16BIT);
}

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* T5848 Microphone Functions */
static int init_microphone(void)
{
    if (!gpio_is_ready_dt(&mic_vdd)) {
        LOG_ERR("Mic VDD GPIO device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&mic_vdd, GPIO_OUTPUT_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure mic VDD GPIO: %d", ret);
        return ret;
    }

    // Power on microphone and wait for stabilization
    k_sleep(K_MSEC(100));

    LOG_INF("Microphone powered on");
    return 0;
}

static int stop_microphone(void)
{
    int ret = gpio_pin_set_dt(&mic_vdd, 0);
    if (ret < 0) {
        LOG_ERR("Failed to power off mic VDD GPIO: %d", ret);
        return ret;
    }

    LOG_INF("Microphone powered off");
    return 0;
}

static int measure_sound_level(float *spl)
{
    const struct device *i2s_dev = DEVICE_DT_GET(DT_NODELABEL(i2s0));
    if (!device_is_ready(i2s_dev)) {
        LOG_ERR("I2S device not ready");
        return -ENODEV;
    }

    // Initialize microphone power
    int ret = init_microphone();
    if (ret < 0) {
        return ret;
    }

    // Configure I2S
    struct i2s_config i2s_cfg = {
        .word_size = 16,
        .channels = 2,
        .format = I2S_FMT_DATA_FORMAT_I2S,
        .options = I2S_OPT_BIT_CLK_MASTER | I2S_OPT_FRAME_CLK_MASTER,
        .frame_clk_freq = 16000,
        .block_size = I2S_BLOCK_SIZE,
        .timeout = I2S_TIMEOUT_MS,
        .mem_slab = &mic_slab,
    };

    ret = i2s_configure(i2s_dev, I2S_DIR_RX, &i2s_cfg);
    if (ret < 0) {
        LOG_ERR("I2S configuration failed: %d", ret);
        stop_microphone();
        return ret;
    }

    // Start I2S
    ret = i2s_trigger(i2s_dev, I2S_DIR_RX, I2S_TRIGGER_START);
    if (ret < 0) {
        LOG_ERR("Failed to start I2S: %d", ret);
        stop_microphone();
        return ret;
    }

    // Discard initial blocks for stabilization
    LOG_INF("Waiting for microphone to stabilize...");
    for (int i = 0; i < NUM_BLOCKS_TO_DISCARD; i++) {
        void *rx_block;
        size_t size;
        ret = i2s_read(i2s_dev, &rx_block, &size);
        if (ret < 0) {
            LOG_ERR("Failed to read and discard audio block: %d", ret);
            i2s_trigger(i2s_dev, I2S_DIR_RX, I2S_TRIGGER_DROP);
            stop_microphone();
            return ret;
        }
        k_mem_slab_free(&mic_slab, rx_block);
    }

    uint32_t collected_count = 0;

    LOG_INF("Collecting %d audio blocks...", NUM_BLOCKS_TO_COLLECT);
    for (int i = 0; i < NUM_BLOCKS_TO_COLLECT; i++) {
        void *rx_block;
        size_t size;
        ret = i2s_read(i2s_dev, &rx_block, &size);
        if (ret < 0) {
            LOG_ERR("Failed to read audio block: %d", ret);
            break;
        }

        int16_t *interleaved_samples = (int16_t *)rx_block;
        uint32_t sample_pairs = size / 4;

        // Use static buffer instead of stack allocation
        uint32_t samples_to_copy = (sample_pairs < SAMPLES_PER_BLOCK) ? sample_pairs : SAMPLES_PER_BLOCK;

        for (uint32_t j = 0; j < samples_to_copy; j++) {
            audio_processing_buffer[j] = interleaved_samples[j * 2 + 1]; // Extract right channel
        }

        dbfs_processing_buffer[collected_count++] = calculate_block_dbfs(audio_processing_buffer, samples_to_copy);
        k_mem_slab_free(&mic_slab, rx_block);
    }

    // Stop I2S and power down microphone
    i2s_trigger(i2s_dev, I2S_DIR_RX, I2S_TRIGGER_DROP);
    stop_microphone();

    if (collected_count <= (NUM_DBFS_VALUES_TO_TRIM * 2)) {
        LOG_ERR("Not enough data collected (%u) to produce a stable result.", collected_count);
        *spl = 0.0f;
        return -EIO;
    }

    LOG_INF("Processing %u collected dBFS values...", collected_count);

    // Sort the dBFS values to trim outliers
    qsort(dbfs_values, collected_count, sizeof(float), float_compare);

    // Average the trimmed values
    float total_dbfs = 0.0f;
    uint32_t values_to_average = collected_count - (NUM_DBFS_VALUES_TO_TRIM * 2);
    for (uint32_t i = NUM_DBFS_VALUES_TO_TRIM; i < collected_count - NUM_DBFS_VALUES_TO_TRIM; i++) {
        total_dbfs += dbfs_values[i];
    }
    float average_dbfs = total_dbfs / values_to_average;

    *spl = average_dbfs + SPL_CALIBRATION_OFFSET;

    LOG_INF("Sound level: %.2f dB", (double)*spl);
    return 0;
}

/* GPIO I2C bit-banging functions */
static void gpio_i2c_delay(void)
{
    k_busy_wait(10); // 10us delay for reliable timing
}

static void gpio_i2c_sda_high(void)
{
    gpio_pin_configure_dt(&gpio_sda, GPIO_INPUT | GPIO_PULL_UP);
    gpio_i2c_delay();
}

static void gpio_i2c_sda_low(void)
{
    gpio_pin_configure_dt(&gpio_sda, GPIO_OUTPUT_LOW);
    gpio_i2c_delay();
}

static void gpio_i2c_scl_high(void)
{
    gpio_pin_configure_dt(&gpio_scl, GPIO_INPUT | GPIO_PULL_UP);
    gpio_i2c_delay();
}

static void gpio_i2c_scl_low(void)
{
    gpio_pin_configure_dt(&gpio_scl, GPIO_OUTPUT_LOW);
    gpio_i2c_delay();
}

static int gpio_i2c_read_sda(void)
{
    return gpio_pin_get_dt(&gpio_sda);
}

static void gpio_i2c_start(void)
{
    gpio_i2c_sda_high();
    gpio_i2c_scl_high();
    gpio_i2c_sda_low();
    gpio_i2c_scl_low();
}

static void gpio_i2c_stop(void)
{
    gpio_i2c_sda_low();
    gpio_i2c_scl_high();
    gpio_i2c_sda_high();
}

static int gpio_i2c_write_byte(uint8_t data)
{
    for (int i = 7; i >= 0; i--) {
        if (data & (1 << i)) {
            gpio_i2c_sda_high();
        } else {
            gpio_i2c_sda_low();
        }
        gpio_i2c_scl_high();
        gpio_i2c_scl_low();
    }
    
    // Read ACK
    gpio_i2c_sda_high();
    gpio_i2c_scl_high();
    int ack = gpio_i2c_read_sda();
    gpio_i2c_scl_low();
    
    return ack == 0 ? 0 : -1; // ACK should be 0
}

static uint8_t gpio_i2c_read_byte(bool send_ack)
{
    uint8_t data = 0;
    
    gpio_i2c_sda_high(); // Release SDA for reading
    
    for (int i = 7; i >= 0; i--) {
        gpio_i2c_scl_high();
        if (gpio_i2c_read_sda()) {
            data |= (1 << i);
        }
        gpio_i2c_scl_low();
    }
    
    // Send ACK/NACK
    if (send_ack) {
        gpio_i2c_sda_low();  // ACK
    } else {
        gpio_i2c_sda_high(); // NACK
    }
    gpio_i2c_scl_high();
    gpio_i2c_scl_low();
    
    return data;
}

/* Initialize GPIO I2C */
static int gpio_i2c_init(void)
{
    if (!gpio_is_ready_dt(&gpio_sda) || !gpio_is_ready_dt(&gpio_scl)) {
        LOG_ERR("GPIO devices not ready");
        return -ENODEV;
    }
    
    // Initialize pins as inputs with pull-up (idle state)
    int ret = gpio_pin_configure_dt(&gpio_sda, GPIO_INPUT | GPIO_PULL_UP);
    if (ret < 0) {
        LOG_ERR("Failed to configure SDA pin: %d", ret);
        return ret;
    }
    
    ret = gpio_pin_configure_dt(&gpio_scl, GPIO_INPUT | GPIO_PULL_UP);
    if (ret < 0) {
        LOG_ERR("Failed to configure SCL pin: %d", ret);
        return ret;
    }
    
    // Wait for pins to stabilize
    k_msleep(10);
    
    LOG_INF("GPIO I2C initialized (SDA: P0.24, SCL: P1.08)");
    return 0;
}

/* Send M117 command using GPIO I2C */
static int gpio_i2c_send_m117_command(uint16_t command)
{
    gpio_i2c_start();
    
    // Write M117 address + write bit
    int ret = gpio_i2c_write_byte((M117_I2C_ADDR << 1) | 0);
    if (ret < 0) {
        LOG_ERR("Failed to write M117 address");
        gpio_i2c_stop();
        return ret;
    }
    
    // Write command MSB
    ret = gpio_i2c_write_byte((command >> 8) & 0xFF);
    if (ret < 0) {
        LOG_ERR("Failed to write command MSB");
        gpio_i2c_stop();
        return ret;
    }
    
    // Write command LSB
    ret = gpio_i2c_write_byte(command & 0xFF);
    if (ret < 0) {
        LOG_ERR("Failed to write command LSB");
        gpio_i2c_stop();
        return ret;
    }
    
    gpio_i2c_stop();
    return 0;
}

/* Read M117 data using GPIO I2C */
static int gpio_i2c_read_m117_data(uint8_t *data, int len)
{
    gpio_i2c_start();
    
    // Write M117 address + read bit
    int ret = gpio_i2c_write_byte((M117_I2C_ADDR << 1) | 1);
    if (ret < 0) {
        LOG_ERR("Failed to write M117 read address");
        gpio_i2c_stop();
        return ret;
    }
    
    // Read data bytes
    for (int i = 0; i < len; i++) {
        bool send_ack = (i < len - 1); // ACK for all but last byte
        data[i] = gpio_i2c_read_byte(send_ack);
    }
    
    gpio_i2c_stop();
    return 0;
}

/* CRC8 calculation for M117 */
static uint8_t calculate_crc8(uint8_t *data, uint8_t len)
{
    uint8_t crc = 0xFF;
    uint8_t polynomial = 0x31;

    for (uint8_t i = 0; i < len; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ polynomial;
            } else {
                crc = (crc << 1);
            }
        }
    }
    return crc;
}

/* Read M117 temperature using correct protocol + GPIO I2C */
static int read_m117_temperature_gpio(float *temperature)
{
    int ret;
    uint8_t temp_data[3];
    
    LOG_INF("Starting M117 temperature conversion...");
    
    // Send temperature conversion command
    ret = gpio_i2c_send_m117_command(M117_CMD_CONVERT_T);
    if (ret < 0) {
        LOG_ERR("Failed to send conversion command: %d", ret);
        return ret;
    }
    
    // Wait for conversion (15ms for high repeatability)
    k_msleep(15);
    
    // Read temperature data
    ret = gpio_i2c_read_m117_data(temp_data, 3);
    if (ret < 0) {
        LOG_ERR("Failed to read temperature data: %d", ret);
        return ret;
    }
    
    // Verify CRC
    uint8_t crc = calculate_crc8(temp_data, 2);
    if (crc != temp_data[2]) {
        LOG_WRN("CRC mismatch: calculated=0x%02X, received=0x%02X", crc, temp_data[2]);
        // Continue anyway - CRC might not be critical
    }
    
    // Convert temperature (M117 correct formula)
    int16_t raw_temp = (temp_data[0] << 8) | temp_data[1];
    *temperature = ((float)raw_temp / 256.0f) + 40.0f;  // +40, not -40
    
    LOG_INF("Raw temp data: 0x%02X%02X, CRC: 0x%02X", 
            temp_data[0], temp_data[1], temp_data[2]);
    LOG_INF("Temperature: %.2f°C", (double)*temperature);
    
    return 0;
}

/* Collect temperature data using GPIO I2C */
static void collect_temperature_data(void)
{
    LOG_INF("=== GPIO I2C M117 Temperature Collection ===");
    
    int ret = gpio_i2c_init();
    if (ret < 0) {
        LOG_ERR("GPIO I2C init failed: %d", ret);
        return;
    }
    
    float temperature = 0.0f;
    ret = read_m117_temperature_gpio(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature reading failed: %d", ret);
    }
    
    // Configure pins as inputs with pull-up for low power
    gpio_pin_configure_dt(&gpio_sda, GPIO_INPUT | GPIO_PULL_UP);
    gpio_pin_configure_dt(&gpio_scl, GPIO_INPUT | GPIO_PULL_UP);
    
    LOG_INF("=== Temperature Collection Complete ===");
}

/* Collect sound data using T5848 microphone */
static void collect_sound_data(void)
{
    LOG_INF("=== T5848 Microphone Sound Collection ===");

    float sound_level = 0.0f;
    int ret = measure_sound_level(&sound_level);
    if (ret < 0) {
        LOG_ERR("Sound measurement failed: %d", ret);
    } else {
        LOG_INF("Sound level: %.2f dB", (double)sound_level);
    }

    LOG_INF("=== Sound Collection Complete ===");
}

/* Suspend all peripherals */
static void suspend_all_peripherals(void)
{
    LOG_INF("Suspending peripherals...");
    
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
}

int main(void)
{
    LOG_INF("\n=== SW2600 HYBRID SOLUTION ===");
    LOG_INF("GPIO I2C + Correct M117 Protocol");
    LOG_INF("Target: 3.5µA sleep current");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }
    
    /* Collect temperature data using GPIO I2C */
    collect_temperature_data();

    /* Collect sound data using T5848 microphone */
    collect_sound_data();

    /* Wait before sleep */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Entering ultra-low power mode...");
    LOG_INF("Press button to wake up");
    
    /* Small delay for UART output */
    k_msleep(200);
    
    /* Suspend all peripherals */
    suspend_all_peripherals();
    
    /* Final delay */
    k_msleep(100);
    
    /* Enter system off mode */
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running!");
    }
    return 0;
}
