#ifndef ADXL38X_SENSOR_H
#define ADXL38X_SENSOR_H

#include <stdint.h>
#include <stdbool.h>

/* 前向声明 */
typedef struct {
    int16_t x;
    int16_t y;
    int16_t z;
} raw_accel_data_t;

/* ADXL38X寄存器地址定义 - 与adxl38x.h保持一致 */
#define ADXL38X_DEVID_AD                0x00
#define ADXL38X_DEVID_MST               0x01
#define ADXL38X_PART_ID                 0x02
#define ADXL38X_STATUS0                 0x11
#define ADXL38X_STATUS1                 0x12
#define ADXL38X_STATUS2                 0x13
#define ADXL38X_STATUS3                 0x14
#define ADXL38X_XDATA_H                 0x15
#define ADXL38X_XDATA_L                 0x16
#define ADXL38X_YDATA_H                 0x17
#define ADXL38X_YDATA_L                 0x18
#define ADXL38X_ZDATA_H                 0x19
#define ADXL38X_ZDATA_L                 0x1A
#define ADXL38X_TDATA_H                 0x1B
#define ADXL38X_TDATA_L                 0x1C
#define ADXL38X_FIFO_DATA               0x1D
#define ADXL38X_FIFO_STATUS0            0x1E
#define ADXL38X_FIFO_STATUS1            0x1F
#define ADXL38X_MISC0                   0x20
#define ADXL38X_OP_MODE                 0x26
#define ADXL38X_DIG_EN                  0x27
#define ADXL38X_REG_RESET               0x2A
#define ADXL38X_FIFO_CFG0               0x30
#define ADXL38X_FIFO_CFG1               0x31
#define ADXL38X_SNSR_AXIS_EN            0x38
#define ADXL38X_FILTER                  0x50
#define ADXL38X_INT0_MAP0			    0x2B
#define ADXL38X_INT0_MAP1			    0x2C
#define ADXL38X_INT1_MAP0			    0x2D
#define ADXL38X_INT1_MAP1			    0x2E
#define ADXL38X_INT0				    0x5D
#define ADXL38X_INT1				    0x5E

/* ADXL38X复位和设备ID值 */
#define ADXL38X_RESET_CODE              0x52
#define ADXL38X_RESET_DEVID_AD          0xAD
#define ADXL38X_RESET_DEVID_MST         0x1D
#define ADXL38X_RESET_PART_ID           0x17

/* ADXL38X掩码定义 */
#define ADXL38X_MASK_RANGE              0xC0
#define ADXL38X_MASK_OP_MODE            0x0F
#define ADXL38X_MASK_CHEN_DIG_EN        0xF0
#define ADXL38X_SLF_TST_CTRL_MSK        0xE0
#define ADXL38X_FIFOCFG_FIFOMODE_MSK    0x30

/* SPI定义 */
#define ADXL382_NODE DT_NODELABEL(adxl382)

/* 传感器转换比例 - 基于官方adxl38x.h */
/* ADXL382 - 完全匹配官方定义 */
#define ADXL382_ACC_SCALE_FACTOR_GEE_MUL    5000        // 官方：500ug/LSB * 10
#define ADXL38X_ACC_SCALE_FACTOR_GEE_DIV    10000000    // 官方通用分母
#define ADXL382_ACC_SENSITIVITY             2000        // 官方：2000 LSB/g at ±15g
#define ADXL38X_TEMP_OFFSET                 295         // 官方：295 LSB at 0°C
#define ADXL38X_TEMP_SCALE_NUM              102         // 官方：10.2 LSB/°C * 10
#define ADXL38X_TEMP_SCALE_DEN              10          // 官方分母

/* ADXL38X工作模式定义 - 基于官方adxl38x.h */
enum adxl38x_op_mode {
    ADXL38X_MODE_STDBY = 0,                 /* 待机模式 */
    ADXL38X_MODE_HRT_SND = 1,              /* 心跳模式 */
    ADXL38X_MODE_ULP = 2,                  /* 超低功耗模式 */
    ADXL38X_MODE_VLP = 3,                  /* 极低功耗模式 */
    ADXL38X_MODE_LP = 4,                   /* 低功耗模式 */
    ADXL38X_MODE_RBW = 8,                  /* 减少带宽模式 */
    ADXL38X_MODE_HP = 12,                  /* 高性能模式 */
};

/* ADXL38X量程定义 - 基于官方adxl38x.h */
enum adxl38x_range {
    ADXL380_RANGE_4G = 0,                  /* ADXL380: ±4g */
    ADXL382_RANGE_15G = 0,                 /* ADXL382: ±15g */
    ADXL380_RANGE_8G = 1,                  /* ADXL380: ±8g */
    ADXL382_RANGE_30G = 1,                 /* ADXL382: ±30g */
    ADXL380_RANGE_16G = 2,                 /* ADXL380: ±16g */
    ADXL382_RANGE_60G = 2                  /* ADXL382: ±60g */
};

/* ADXL38X FIFO模式定义 */
#define ADXL38X_FIFO_DISABLE      0   /* FIFO禁用 */
#define ADXL38X_FIFO_NORMAL       1   /* 普通FIFO模式 */
#define ADXL38X_FIFO_STREAM       2   /* 流模式 */
#define ADXL38X_FIFO_TRIGGER      3   /* 触发模式 */

#define ADXL38X_SPI_READ          		0x01
#define ADXL38X_SPI_WRITE         		0x00

// 在 adxl38x_sensor.h 中
#define ADXL38X_ST_LIMIT_DENOMINATOR 10  // 添加缺失的常量定义

// 新增：用于存储原始加速度数据的结构体
struct RawAccelData {
    int16_t x; // X轴原始数据
    int16_t y; // Y轴原始数据
    int16_t z; // Z轴原始数据
};

// 声明缺失的函数
int adxl38x_sensor_set_ch_select(uint8_t ch_select);

/* 通道选择定义 */
enum adxl38x_ch_select {
    ADXL38X_CH_DSB_ALL = 0,
    ADXL38X_CH_EN_X = 1,
    ADXL38X_CH_EN_Y = 2,
    ADXL38X_CH_EN_XY = 3,
    ADXL38X_CH_EN_Z = 4,
    ADXL38X_CH_EN_YZ = 6,
    ADXL38X_CH_EN_XYZ = 7,
    ADXL38X_CH_EN_T = 8,
    ADXL38X_CH_EN_ZT = 12,
    ADXL38X_CH_EN_YZT = 14,
    ADXL38X_CH_EN_XYZT = 15
};

/* 官方数据转换相关定义 */
#define ADXL38X_NEG_ACC_MSK             0xFFFF0000  // 负数扩展掩码
static const uint8_t adxl38x_scale_mul[3] = {1, 2, 4};  // 量程倍数

/* 官方数据转换函数声明 */
int64_t adxl38x_accel_conv_official(uint16_t raw_accel, enum adxl38x_range range);
float adxl38x_raw_to_gees_official(uint16_t raw_accel, enum adxl38x_range range);

/**
 * @brief 初始化ADXL38X传感器
 *
 * @param op_mode 工作模式
 * @param range 量程设置
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_init(uint8_t op_mode, uint8_t range);

/**
 * @brief 读取ADXL38X传感器的加速度数据
 *
 * @param x_g X轴加速度 (g值)
 * @param y_g Y轴加速度 (g值)
 * @param z_g Z轴加速度 (g值)
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_read_acceleration(float *x_g, float *y_g, float *z_g);

/**
 * @brief 采集ADXL382振动数据
 *
 * @param data 数据缓冲区
 * @param max_count 最大采集数量
 * @return int 成功返回采集的数据数量，失败返回负数
 */
int adxl38x_collect_data(raw_accel_data_t *data, uint32_t max_count);

/**
 * @brief 读取ADXL38X传感器的温度数据
 *
 * @param temp_c 温度 (摄氏度)
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_read_temperature(float *temp_c);

/**
 * @brief 执行ADXL38X的自检测试
 * 
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_self_test(void);

/**
 * @brief 设置ADXL38X传感器的工作模式
 * 
 * @param op_mode 工作模式
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_set_mode(uint8_t op_mode);

/**
 * @brief 设置ADXL38X传感器的量程
 * 
 * @param range 量程设置
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_set_range(uint8_t range);

/**
 * @brief 配置ADXL38X传感器的FIFO
 * 
 * @param num_samples 样本数量
 * @param fifo_mode FIFO模式
 * @param ch_id_enable 通道ID启用标志
 * @param read_reset 读取重置标志
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_config_fifo(uint16_t num_samples, uint8_t fifo_mode, bool ch_id_enable, bool read_reset);

/**
 * @brief 使用FIFO和中断读取并处理指定数量的原始XYZ轴加速度数据。
 *
 * @param fifo_watermark FIFO 水印级别 (1-320).
 * @param odr_hz 传感器配置的名义ODR (Hz). (信息性参数，用于日志记录)
 * @param target_samples_to_collect 目标采集的 RawAccelData 样本数量。
 * @param max_wait_time_ms 采集目标样本数量的最大等待时间 (毫秒)。
 * @param num_axes_enabled 启用轴的数量 (1 或 3).
 * @param raw_data_output_buffer 指向用于存储采集到的 RawAccelData 的缓冲区。
 * @param max_buffer_samples raw_data_output_buffer 的最大容量 (以 RawAccelData 结构体数量计)。
 * @param[out] samples_collected_ptr 指向用于存储实际采集并存入缓冲区的 RawAccelData 数量的变量的指针。
 * @return int 成功返回0，失败返回错误码 (例如 -ETIMEDOUT 如果超时)。
 */
int adxl38x_sensor_stream_raw_data_fifo_int(
    uint16_t fifo_watermark,
    uint16_t odr_hz,
    uint32_t target_samples_to_collect,        // 修改：目标样本数
    uint32_t max_wait_time_ms,                 // 新增：最大等待时间
    uint8_t num_axes_enabled,
    struct RawAccelData *raw_data_output_buffer, 
    uint32_t max_buffer_samples,
    uint32_t *samples_collected_ptr);

/* 兼容性函数声明 */
typedef struct {
    float accel_rms_x;
    float accel_rms_y;
    float accel_rms_z;
    float velocity_rms_x;
    float velocity_rms_y;
    float velocity_rms_z;
    float displacement_pp_x;
    float displacement_pp_y;
    float displacement_pp_z;
    uint32_t main_freq_x;
    uint32_t main_freq_y;
    uint32_t main_freq_z;
    float main_amp_x;
    float main_amp_y;
    float main_amp_z;
} vibration_result_t;

/**
 * @brief 兼容性函数：采集振动数据并计算结果
 *
 * @param result 振动分析结果
 * @return int 成功返回0，失败返回错误码
 */
int adxl382_collect_vibration_data(vibration_result_t *result);

#endif /* ADXL38X_SENSOR_H */
