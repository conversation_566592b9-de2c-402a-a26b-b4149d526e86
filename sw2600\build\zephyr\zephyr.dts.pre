# 0 "C:/ncs/v2.9.0-zigbee/zephyr/misc/empty_file.c"
# 0 "<built-in>"
# 0 "<command-line>"
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340dk_nrf5340_cpuapp.dts" 1






/dts-v1/;
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp_qkaa.dtsi" 1 3 4






# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/mem.h" 1 3 4
# 8 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp_qkaa.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp.dtsi" 1 3 4






# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/armv8-m.dtsi" 1 3 4


# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/skeleton.dtsi" 1 3 4
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/skeleton.dtsi" 3 4
/ {
 #address-cells = <1>;
 #size-cells = <1>;
 chosen { };
 aliases { };
};
# 4 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/armv8-m.dtsi" 2 3 4

/ {
 soc {
  #address-cells = <1>;
  #size-cells = <1>;
  compatible = "simple-bus";
  interrupt-parent = <&nvic>;
  ranges;

  nvic: interrupt-controller@e000e100 {
   #address-cells = <1>;
   compatible = "arm,v8m-nvic";
   reg = <0xe000e100 0xc00>;
   interrupt-controller;
   #interrupt-cells = <2>;
  };

  systick: timer@e000e010 {
   compatible = "arm,armv8m-systick";
   reg = <0xe000e010 0x10>;
  };
 };
};
# 8 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 1 3 4






# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/adc.h" 1 3 4
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/adc.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/dt-util.h" 1 3 4
# 19 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/dt-util.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_macro.h" 1 3 4
# 34 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_macro.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 1 3 4
# 18 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_loops.h" 1 3 4
# 1083 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_loops.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_listify.h" 1 3 4
# 1084 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_loops.h" 2 3 4
# 19 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 2 3 4
# 162 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal_is_eq.h" 1 3 4
# 163 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 2 3 4
# 193 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal_util_inc.h" 1 3 4
# 194 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 2 3 4


# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal_util_dec.h" 1 3 4
# 197 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 2 3 4


# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal_util_x2.h" 1 3 4
# 200 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_internal.h" 2 3 4
# 35 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/sys/util_macro.h" 2 3 4
# 20 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/dt-util.h" 2 3 4
# 10 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/adc.h" 2 3 4
# 8 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/gpio/gpio.h" 1 3 4
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/i2c/i2c.h" 1 3 4
# 10 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h" 1 3 4
# 11 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h" 1 3 4
# 12 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/pwm/pwm.h" 1 3 4
# 13 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4

# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/freq.h" 1 3 4
# 15 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/override.dtsi" 1 3 4
# 16 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 2 3 4
# 24 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf_common.dtsi" 3 4
/ {
 pinctrl: pin-controller {




  compatible = "nordic,nrf-pinctrl";
 };

 rng_hci: entropy_bt_hci {
  compatible = "zephyr,bt-hci-entropy";
  status = "okay";
 };

 sw_pwm: sw-pwm {
  compatible = "nordic,nrf-sw-pwm";
  status = "disabled";
  generator = <&timer1>;
  clock-prescaler = <0>;
  #pwm-cells = <3>;
 };
};
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp.dtsi" 2 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h" 1 3 4
# 10 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h" 1 3 4
# 10 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h" 3 4
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc.h" 1 3 4
# 11 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h" 2 3 4
# 11 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h" 2 3 4
# 10 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp.dtsi" 2 3 4

/ {
 cpus {
  #address-cells = <1>;
  #size-cells = <0>;

  cpu0: cpu@0 {
   device_type = "cpu";
   compatible = "arm,cortex-m33f";
   reg = <0>;
   #address-cells = <1>;
   #size-cells = <1>;

   itm: itm@e0000000 {
    compatible = "arm,armv8m-itm";
    reg = <0xe0000000 0x1000>;
    swo-ref-frequency = <64000000>;
   };

   mpu: mpu@e000ed90 {
    compatible = "arm,armv8m-mpu";
    reg = <0xe000ed90 0x40>;
   };
  };
 };

 chosen {
  zephyr,entropy = &cryptocell;
  zephyr,flash-controller = &flash_controller;
 };

 soc {
  ficr: ficr@ff0000 {
   compatible = "nordic,nrf-ficr";
   reg = <0xff0000 0x1000>;
   #nordic,ficr-cells = <1>;
   status = "okay";
  };

  uicr: uicr@ff8000 {
   compatible = "nordic,nrf-uicr";
   reg = <0xff8000 0x1000>;
   status = "okay";
  };

  sram0: memory@20000000 {
   compatible = "mmio-sram";
  };

  peripheral@50000000 {
   #address-cells = <1>;
   #size-cells = <1>;
   ranges = <0x0 0x50000000 0x10000000>;




# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp_peripherals.dtsi" 1 3 4






# 1 "C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/dt-bindings/regulator/nrf5x.h" 1 3 4
# 8 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp_peripherals.dtsi" 2 3 4

dcnf: dcnf@0 {
 compatible = "nordic,nrf-dcnf";
 reg = <0x0 0x1000>;
 status = "okay";
};

oscillators: oscillator@4000 {
 compatible = "nordic,nrf-oscillators";
 reg = <0x4000 0x1000>;
 status = "okay";
};

regulators: regulator@4000 {
 compatible = "nordic,nrf53x-regulators";
 reg = <0x4000 0x1000>;
 status = "okay";
 #address-cells = <1>;
 #size-cells = <1>;

 vregmain: regulator@4704 {
  compatible = "nordic,nrf5x-regulator";
  reg = <0x4704 0x1>;
  status = "okay";
  regulator-name = "VREGMAIN";
  regulator-initial-mode = <0>;
 };

 vregradio: regulator@4904 {
  compatible = "nordic,nrf5x-regulator";
  reg = <0x4904 0x1>;
  status = "okay";
  regulator-name = "VREGRADIO";
  regulator-initial-mode = <0>;
 };

 vregh: regulator@4b00 {
  compatible = "nordic,nrf53x-regulator-hv";
  reg = <0x4b00 0x44>;
  status = "disabled";
  regulator-name = "VREGH";
 };
};

clock: clock@5000 {
 compatible = "nordic,nrf-clock";
 reg = <0x5000 0x1000>;
 interrupts = <5 1>;
 status = "okay";
};

power: power@5000 {
 compatible = "nordic,nrf-power";
 reg = <0x5000 0x1000>;
 ranges = <0x0 0x5000 0x1000>;
 interrupts = <5 1>;
 status = "okay";
 #address-cells = <1>;
 #size-cells = <1>;

 gpregret1: gpregret1@51c {
  #address-cells = <1>;
  #size-cells = <1>;
  compatible = "nordic,nrf-gpregret";
  reg = <0x51c 0x1>;
  status = "okay";
 };

 gpregret2: gpregret2@520 {
  #address-cells = <1>;
  #size-cells = <1>;
  compatible = "nordic,nrf-gpregret";
  reg = <0x520 0x1>;
  status = "okay";
 };
};

reset: reset-controller@5000 {
 compatible = "nordic,nrf-reset";
 reg = <0x5000 0x1000>;
 status = "okay";
};

ctrlap: ctrlap@6000 {
 compatible = "nordic,nrf-ctrlapperi";
 reg = <0x6000 0x1000>;
 status = "okay";
};

i2c0: i2c@8000 {





 compatible = "nordic,nrf-twim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0x8000 0x1000>;
 interrupts = <8 1>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
 zephyr,pm-device-runtime-auto;
};

spi0: spi@8000 {






 compatible = "nordic,nrf-spim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0x8000 0x1000>;
 interrupts = <8 1>;
 max-frequency = <((8) * 1000 * 1000)>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
};

uart0: uart@8000 {
 compatible = "nordic,nrf-uarte";
 reg = <0x8000 0x1000>;
 interrupts = <8 1>;
 status = "disabled";
};

i2c1: i2c@9000 {






 compatible = "nordic,nrf-twim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0x9000 0x1000>;
 interrupts = <9 1>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
 zephyr,pm-device-runtime-auto;
};

spi1: spi@9000 {






 compatible = "nordic,nrf-spim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0x9000 0x1000>;
 interrupts = <9 1>;
 max-frequency = <((8) * 1000 * 1000)>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
};

uart1: uart@9000 {
 compatible = "nordic,nrf-uarte";
 reg = <0x9000 0x1000>;
 interrupts = <9 1>;
 status = "disabled";
};

spi4: spi@a000 {
 compatible = "nordic,nrf-spim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0xa000 0x1000>;
 interrupts = <10 1>;
 max-frequency = <((32) * 1000 * 1000)>;
 easydma-maxcnt-bits = <16>;
 rx-delay-supported;
 rx-delay = <2>;
 status = "disabled";
};

i2c2: i2c@b000 {






 compatible = "nordic,nrf-twim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0xb000 0x1000>;
 interrupts = <11 1>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
 zephyr,pm-device-runtime-auto;
};

spi2: spi@b000 {






 compatible = "nordic,nrf-spim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0xb000 0x1000>;
 interrupts = <11 1>;
 max-frequency = <((8) * 1000 * 1000)>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
};

uart2: uart@b000 {
 compatible = "nordic,nrf-uarte";
 reg = <0xb000 0x1000>;
 interrupts = <11 1>;
 status = "disabled";
};

i2c3: i2c@c000 {






 compatible = "nordic,nrf-twim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0xc000 0x1000>;
 interrupts = <12 1>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
 zephyr,pm-device-runtime-auto;
};

spi3: spi@c000 {






 compatible = "nordic,nrf-spim";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0xc000 0x1000>;
 interrupts = <12 1>;
 max-frequency = <((8) * 1000 * 1000)>;
 easydma-maxcnt-bits = <16>;
 status = "disabled";
};

uart3: uart@c000 {
 compatible = "nordic,nrf-uarte";
 reg = <0xc000 0x1000>;
 interrupts = <12 1>;
 status = "disabled";
};

adc: adc@e000 {
 compatible = "nordic,nrf-saadc";
 reg = <0xe000 0x1000>;
 interrupts = <14 1>;
 status = "disabled";
 #io-channel-cells = <1>;
};

timer0: timer@f000 {
 compatible = "nordic,nrf-timer";
 status = "disabled";
 reg = <0xf000 0x1000>;
 cc-num = <6>;
 max-bit-width = <32>;
 interrupts = <15 1>;
 prescaler = <0>;
};

timer1: timer@10000 {
 compatible = "nordic,nrf-timer";
 status = "disabled";
 reg = <0x10000 0x1000>;
 cc-num = <6>;
 max-bit-width = <32>;
 interrupts = <16 1>;
 prescaler = <0>;
};

timer2: timer@11000 {
 compatible = "nordic,nrf-timer";
 status = "disabled";
 reg = <0x11000 0x1000>;
 cc-num = <6>;
 max-bit-width = <32>;
 interrupts = <17 1>;
 prescaler = <0>;
};

rtc0: rtc@14000 {
 compatible = "nordic,nrf-rtc";
 reg = <0x14000 0x1000>;
 cc-num = <4>;
 interrupts = <20 1>;
 status = "disabled";
 clock-frequency = <32768>;
 prescaler = <1>;
};

rtc1: rtc@15000 {
 compatible = "nordic,nrf-rtc";
 reg = <0x15000 0x1000>;
 cc-num = <4>;
 interrupts = <21 1>;
 status = "disabled";
 clock-frequency = <32768>;
 prescaler = <1>;
};

dppic0: dppic: dppic@17000 {
 compatible = "nordic,nrf-dppic";
 reg = <0x17000 0x1000>;
 status = "okay";
};

wdt: wdt0: watchdog@18000 {
 compatible = "nordic,nrf-wdt";
 reg = <0x18000 0x1000>;
 interrupts = <24 1>;
 status = "okay";
};

wdt1: watchdog@19000 {
 compatible = "nordic,nrf-wdt";
 reg = <0x19000 0x1000>;
 interrupts = <25 1>;
 status = "disabled";
};

comp: comparator@1a000 {




 compatible = "nordic,nrf-comp";
 reg = <0x1a000 0x1000>;
 interrupts = <26 1>;
 status = "disabled";
};

egu0: egu@1b000 {
 compatible = "nordic,nrf-egu";
 reg = <0x1b000 0x1000>;
 interrupts = <27 1>;
 status = "okay";
};

egu1: egu@1c000 {
 compatible = "nordic,nrf-egu";
 reg = <0x1c000 0x1000>;
 interrupts = <28 1>;
 status = "okay";
};

egu2: egu@1d000 {
 compatible = "nordic,nrf-egu";
 reg = <0x1d000 0x1000>;
 interrupts = <29 1>;
 status = "okay";
};

egu3: egu@1e000 {
 compatible = "nordic,nrf-egu";
 reg = <0x1e000 0x1000>;
 interrupts = <30 1>;
 status = "okay";
};

egu4: egu@1f000 {
 compatible = "nordic,nrf-egu";
 reg = <0x1f000 0x1000>;
 interrupts = <31 1>;
 status = "okay";
};

egu5: egu@20000 {
 compatible = "nordic,nrf-egu";
 reg = <0x20000 0x1000>;
 interrupts = <32 1>;
 status = "okay";
};

pwm0: pwm@21000 {
 compatible = "nordic,nrf-pwm";
 reg = <0x21000 0x1000>;
 interrupts = <33 1>;
 status = "disabled";
 #pwm-cells = <3>;
};

pwm1: pwm@22000 {
 compatible = "nordic,nrf-pwm";
 reg = <0x22000 0x1000>;
 interrupts = <34 1>;
 status = "disabled";
 #pwm-cells = <3>;
};

pwm2: pwm@23000 {
 compatible = "nordic,nrf-pwm";
 reg = <0x23000 0x1000>;
 interrupts = <35 1>;
 status = "disabled";
 #pwm-cells = <3>;
};

pwm3: pwm@24000 {
 compatible = "nordic,nrf-pwm";
 reg = <0x24000 0x1000>;
 interrupts = <36 1>;
 status = "disabled";
 #pwm-cells = <3>;
};

pdm0: pdm@26000 {
 compatible = "nordic,nrf-pdm";
 reg = <0x26000 0x1000>;
 interrupts = <38 1>;
 status = "disabled";
};

i2s0: i2s@28000 {
 compatible = "nordic,nrf-i2s";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0x28000 0x1000>;
 interrupts = <40 1>;
 status = "disabled";
};

mbox: ipc: mbox@2a000 {
 compatible = "nordic,mbox-nrf-ipc", "nordic,nrf-ipc";
 reg = <0x2a000 0x1000>;
 tx-mask = <0x0000ffff>;
 rx-mask = <0x0000ffff>;
 interrupts = <42 1>;
 #mbox-cells = <1>;
 status = "okay";
};

qspi: qspi@2b000 {
 compatible = "nordic,nrf-qspi";
 #address-cells = <1>;
 #size-cells = <0>;
 reg = <0x2b000 0x1000>, <0x10000000 0x10000000>;
 reg-names = "qspi", "qspi_mm";
 interrupts = <43 1>;
 status = "disabled";
};

nfct: nfct@2d000 {
 compatible = "nordic,nrf-nfct";
 reg = <0x2d000 0x1000>;
 interrupts = <45 1>;
 status = "disabled";
};

mutex: mutex@30000 {
 compatible = "nordic,nrf-mutex";
 reg = <0x30000 0x1000>;
 status = "okay";
};

qdec0: qdec@33000 {
 compatible = "nordic,nrf-qdec";
 reg = <0x33000 0x1000>;
 interrupts = <51 1>;
 status = "disabled";
};

qdec1: qdec@34000 {
 compatible = "nordic,nrf-qdec";
 reg = <0x34000 0x1000>;
 interrupts = <52 1>;
 status = "disabled";
};

usbd: usbd@36000 {
 compatible = "nordic,nrf-usbd";
 reg = <0x36000 0x1000>;
 interrupts = <54 1>;
 num-bidir-endpoints = <1>;
 num-in-endpoints = <7>;
 num-out-endpoints = <7>;
 num-isoin-endpoints = <1>;
 num-isoout-endpoints = <1>;
 status = "disabled";
};

usbreg: regulator@37000 {
 compatible = "nordic,nrf-usbreg";
 reg = <0x37000 0x1000>;
 interrupts = <55 1>;
 status = "okay";
};

flash_controller: flash-controller@39000 {
 compatible = "nordic,nrf53-flash-controller";
 reg = <0x39000 0x1000>;
 partial-erase;

 #address-cells = <1>;
 #size-cells = <1>;


 flash0: flash@0 {
  compatible = "soc-nv-flash";
  erase-block-size = <4096>;
  write-block-size = <4>;
 };
};

kmu: kmu@39000 {
 compatible = "nordic,nrf-kmu";
 reg = <0x39000 0x1000>;
 interrupts = <57 1>;
 status = "okay";
};

vmc: vmc@81000 {
 compatible = "nordic,nrf-vmc";
 reg = <0x81000 0x1000>;
 status = "okay";
};

gpio0: gpio@842500 {
 compatible = "nordic,nrf-gpio";
 gpio-controller;
 reg = <0x842500 0x300>;
 #gpio-cells = <2>;
 status = "disabled";
 port = <0>;
 gpiote-instance = <&gpiote>;
};

gpio1: gpio@842800 {
 compatible = "nordic,nrf-gpio";
 gpio-controller;
 reg = <0x842800 0x300>;
 #gpio-cells = <2>;
 ngpios = <16>;
 status = "disabled";
 port = <1>;
 gpiote-instance = <&gpiote>;
};

ieee802154: ieee802154 {
 compatible = "nordic,nrf-ieee802154";
 status = "disabled";
};
# 68 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp.dtsi" 2 3 4
  };



  spu: spu@50003000 {
   compatible = "nordic,nrf-spu";
   reg = <0x50003000 0x1000>;
   interrupts = <3 1>;
   status = "okay";
  };






  gpiote: gpiote0: gpiote@5000d000 {
   compatible = "nordic,nrf-gpiote";
   reg = <0x5000d000 0x1000>;
   interrupts = <13 5>;
   status = "disabled";
   instance = <0>;
  };


  gpiote1: gpiote@4002f000 {
   compatible = "nordic,nrf-gpiote";
   reg = <0x4002f000 0x1000>;
   interrupts = <47 5>;
   status = "disabled";
   instance = <1>;
  };

  cryptocell: crypto@50844000 {
   compatible = "nordic,cryptocell", "arm,cryptocell-312";
   reg = <0x50844000 0x1000>, <0x50845000 0x1000>;
   reg-names = "wrapper", "core";
   interrupts = <68 1>;
   status = "okay";
  };
 };


 ipc {
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp_ipc.dtsi" 1 3 4






ipc0: ipc0 {
 compatible = "zephyr,ipc-openamp-static-vrings";
 memory-region = <&sram0_shared>;
 mboxes = <&mbox 0>, <&mbox 1>;
 mbox-names = "tx", "rx";
 role = "host";
 status = "okay";

 bt_hci_ipc0: bt_hci_ipc0 {
  compatible = "zephyr,bt-hci-ipc";
  status = "okay";
 };
};
# 113 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp.dtsi" 2 3 4
 };
};

&nvic {
 arm,num-irq-priority-bits = <3>;
};

&systick {

 status = "disabled";
};
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/dts/arm/nordic/nrf5340_cpuapp_qkaa.dtsi" 2 3 4

&flash0 {
 reg = <0x00000000 ((1024) * 1024)>;
};

&sram0 {
 reg = <0x20000000 ((512) * 1024)>;
};

/ {
 soc {
  compatible = "nordic,nrf5340-cpuapp-qkaa", "nordic,nrf5340-cpuapp",
        "nordic,nrf53", "simple-bus";
 };
};
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340dk_nrf5340_cpuapp.dts" 2
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340_cpuapp_common.dtsi" 1





# 1 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340dk_common.dtsi" 1






/ {
 leds {
  compatible = "gpio-leds";
  led0: led_0 {
   gpios = <&gpio0 28 (1 << 0)>;
   label = "Green LED 0";
  };
  led1: led_1 {
   gpios = <&gpio0 29 (1 << 0)>;
   label = "Green LED 1";
  };
  led2: led_2 {
   gpios = <&gpio0 30 (1 << 0)>;
   label = "Green LED 2";
  };
  led3: led_3 {
   gpios = <&gpio0 31 (1 << 0)>;
   label = "Green LED 3";
  };
 };

 buttons {
  compatible = "gpio-keys";
  button0: button_0 {
   gpios = <&gpio0 23 ((1 << 4) | (1 << 0))>;
   label = "Push button 1";
   zephyr,code = <11>;
  };
  button1: button_1 {
   gpios = <&gpio0 24 ((1 << 4) | (1 << 0))>;
   label = "Push button 2";
   zephyr,code = <2>;
  };
  button2: button_2 {
   gpios = <&gpio0 8 ((1 << 4) | (1 << 0))>;
   label = "Push button 3";
   zephyr,code = <3>;
  };
  button3: button_3 {
   gpios = <&gpio0 9 ((1 << 4) | (1 << 0))>;
   label = "Push button 4";
   zephyr,code = <4>;
  };
 };

 arduino_header: connector {
  compatible = "arduino-header-r3";
  #gpio-cells = <2>;
  gpio-map-mask = <0xffffffff 0xffffffc0>;
  gpio-map-pass-thru = <0 0x3f>;
  gpio-map = <0 0 &gpio0 4 0>,
      <1 0 &gpio0 5 0>,
      <2 0 &gpio0 6 0>,
      <3 0 &gpio0 7 0>,
      <4 0 &gpio0 25 0>,
      <5 0 &gpio0 26 0>,
      <6 0 &gpio1 0 0>,
      <7 0 &gpio1 1 0>,
      <8 0 &gpio1 4 0>,
      <9 0 &gpio1 5 0>,
      <10 0 &gpio1 6 0>,
      <11 0 &gpio1 7 0>,
      <12 0 &gpio1 8 0>,
      <13 0 &gpio1 9 0>,
      <14 0 &gpio1 10 0>,
      <15 0 &gpio1 11 0>,
      <16 0 &gpio1 12 0>,
      <17 0 &gpio1 13 0>,
      <18 0 &gpio1 14 0>,
      <19 0 &gpio1 15 0>,
      <20 0 &gpio1 2 0>,
      <21 0 &gpio1 3 0>;
 };


 aliases {
  led0 = &led0;
  led1 = &led1;
  led2 = &led2;
  led3 = &led3;
  sw0 = &button0;
  sw1 = &button1;
  sw2 = &button2;
  sw3 = &button3;
  bootloader-led0 = &led0;
  mcuboot-button0 = &button0;
  mcuboot-led0 = &led0;
 };
};
# 7 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340_cpuapp_common.dtsi" 2
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340_cpuapp_common-pinctrl.dtsi" 1





&pinctrl {
 i2c1_default: i2c1_default {
  group1 {
   psels = <((((((1) * 32U) + (2)) & 0x1FFU) << 0U) | ((12U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (3)) & 0x1FFU) << 0U) | ((11U & 0xFFU) << 24U))>;
  };
 };

 i2c1_sleep: i2c1_sleep {
  group1 {
   psels = <((((((1) * 32U) + (2)) & 0x1FFU) << 0U) | ((12U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (3)) & 0x1FFU) << 0U) | ((11U & 0xFFU) << 24U))>;
   low-power-enable;
  };
 };

 uart0_default: uart0_default {
  group1 {
   psels = <((((((0) * 32U) + (20)) & 0x1FFU) << 0U) | ((0U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (19)) & 0x1FFU) << 0U) | ((2U & 0xFFU) << 24U))>;
  };
  group2 {
   psels = <((((((0) * 32U) + (22)) & 0x1FFU) << 0U) | ((1U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (21)) & 0x1FFU) << 0U) | ((3U & 0xFFU) << 24U))>;
   bias-pull-up;
  };
 };

 uart0_sleep: uart0_sleep {
  group1 {
   psels = <((((((0) * 32U) + (20)) & 0x1FFU) << 0U) | ((0U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (22)) & 0x1FFU) << 0U) | ((1U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (19)) & 0x1FFU) << 0U) | ((2U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (21)) & 0x1FFU) << 0U) | ((3U & 0xFFU) << 24U))>;
   low-power-enable;
  };
 };

 pwm0_default: pwm0_default {
  group1 {
   psels = <((((((0) * 32U) + (28)) & 0x1FFU) << 0U) | ((22U & 0xFFU) << 24U))>;
  };
 };

 pwm0_sleep: pwm0_sleep {
  group1 {
   psels = <((((((0) * 32U) + (28)) & 0x1FFU) << 0U) | ((22U & 0xFFU) << 24U))>;
   low-power-enable;
  };
 };

 qspi_default: qspi_default {
  group1 {
   psels = <((((((0) * 32U) + (17)) & 0x1FFU) << 0U) | ((29U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (13)) & 0x1FFU) << 0U) | ((31U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (14)) & 0x1FFU) << 0U) | ((32U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (15)) & 0x1FFU) << 0U) | ((33U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (16)) & 0x1FFU) << 0U) | ((34U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (18)) & 0x1FFU) << 0U) | ((30U & 0xFFU) << 24U))>;
   nordic,drive-mode = <3U>;
  };
 };

 qspi_sleep: qspi_sleep {
  group1 {
   psels = <((((((0) * 32U) + (17)) & 0x1FFU) << 0U) | ((29U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (13)) & 0x1FFU) << 0U) | ((31U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (14)) & 0x1FFU) << 0U) | ((32U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (15)) & 0x1FFU) << 0U) | ((33U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (16)) & 0x1FFU) << 0U) | ((34U & 0xFFU) << 24U))>;
   low-power-enable;
  };
  group2 {
   psels = <((((((0) * 32U) + (18)) & 0x1FFU) << 0U) | ((30U & 0xFFU) << 24U))>;
   low-power-enable;
   bias-pull-up;
  };
 };

 uart1_default: uart1_default {
  group1 {
   psels = <((((((1) * 32U) + (1)) & 0x1FFU) << 0U) | ((0U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (11)) & 0x1FFU) << 0U) | ((2U & 0xFFU) << 24U))>;
  };
  group2 {
   psels = <((((((1) * 32U) + (0)) & 0x1FFU) << 0U) | ((1U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (10)) & 0x1FFU) << 0U) | ((3U & 0xFFU) << 24U))>;
   bias-pull-up;
  };
 };

 uart1_sleep: uart1_sleep {
  group1 {
   psels = <((((((1) * 32U) + (1)) & 0x1FFU) << 0U) | ((0U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (0)) & 0x1FFU) << 0U) | ((1U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (11)) & 0x1FFU) << 0U) | ((2U & 0xFFU) << 24U))>,
    <((((((0) * 32U) + (10)) & 0x1FFU) << 0U) | ((3U & 0xFFU) << 24U))>;
   low-power-enable;
  };
 };

 spi4_default: spi4_default {
  group1 {
   psels = <((((((1) * 32U) + (15)) & 0x1FFU) << 0U) | ((4U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (14)) & 0x1FFU) << 0U) | ((6U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (13)) & 0x1FFU) << 0U) | ((5U & 0xFFU) << 24U))>;
  };
 };

 spi4_sleep: spi4_sleep {
  group1 {
   psels = <((((((1) * 32U) + (15)) & 0x1FFU) << 0U) | ((4U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (14)) & 0x1FFU) << 0U) | ((6U & 0xFFU) << 24U))>,
    <((((((1) * 32U) + (13)) & 0x1FFU) << 0U) | ((5U & 0xFFU) << 24U))>;
   low-power-enable;
  };
 };

};
# 8 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340_cpuapp_common.dtsi" 2


/ {

 chosen {
  zephyr,console = &uart0;
  zephyr,shell-uart = &uart0;
  zephyr,uart-mcumgr = &uart0;
  zephyr,bt-mon-uart = &uart0;
  zephyr,bt-c2h-uart = &uart0;
  zephyr,bt-hci = &bt_hci_ipc0;
  nordic,802154-spinel-ipc = &ipc0;
  zephyr,ieee802154 = &ieee802154;
 };

 pwmleds {
  compatible = "pwm-leds";
  pwm_led0: pwm_led_0 {
   pwms = <&pwm0 0 (((20) * 1000UL) * 1000UL) (1 << 0)>;
  };
 };

 arduino_adc: analog-connector {
  compatible = "arduino,uno-adc";
  #io-channel-cells = <1>;
  io-channel-map = <0 &adc 0>,
     <1 &adc 1>,
     <2 &adc 2>,
     <3 &adc 3>,
     <4 &adc 4>,
     <5 &adc 5>;
 };

 gpio_fwd: nrf-gpio-forwarder {
  compatible = "nordic,nrf-gpio-forwarder";
  status = "okay";
  uart {
   gpios = <&gpio1 1 0>, <&gpio1 0 0>, <&gpio0 11 0>, <&gpio0 10 0>;
  };
 };


 aliases {
  pwm-led0 = &pwm_led0;
  watchdog0 = &wdt0;
 };
};

&vregmain {
 regulator-initial-mode = <1>;
};

&vregradio {
 regulator-initial-mode = <1>;
};

&vregh {
 status = "okay";
};

&adc {
 status = "okay";
};

&gpiote {
 status = "okay";
};

&gpio0 {
 status = "okay";
};

&gpio1 {
 status = "okay";
};

&i2c1 {
 compatible = "nordic,nrf-twim";
 status = "okay";
 pinctrl-0 = <&i2c1_default>;
 pinctrl-1 = <&i2c1_sleep>;
 pinctrl-names = "default", "sleep";
};

&uart0 {
 status = "okay";
 current-speed = <115200>;
 pinctrl-0 = <&uart0_default>;
 pinctrl-1 = <&uart0_sleep>;
 pinctrl-names = "default", "sleep";
};

&pwm0 {
 status = "okay";
 pinctrl-0 = <&pwm0_default>;
 pinctrl-1 = <&pwm0_sleep>;
 pinctrl-names = "default", "sleep";
};

&qspi {
 status = "okay";
 pinctrl-0 = <&qspi_default>;
 pinctrl-1 = <&qspi_sleep>;
 pinctrl-names = "default", "sleep";
 mx25r64: mx25r6435f@0 {
  compatible = "nordic,qspi-nor";
  reg = <0>;

  writeoc = "pp4io";

  readoc = "read4io";
  sck-frequency = <8000000>;
  jedec-id = [c2 28 17];
  sfdp-bfp = [
   e5 20 f1 ff ff ff ff 03 44 eb 08 6b 08 3b 04 bb
   ee ff ff ff ff ff 00 ff ff ff 00 ff 0c 20 0f 52
   10 d8 00 ff 23 72 f5 00 82 ed 04 cc 44 83 68 44
   30 b0 30 b0 f7 c4 d5 5c 00 be 29 ff f0 d0 ff ff
  ];
  size = <67108864>;
  has-dpd;
  t-enter-dpd = <10000>;
  t-exit-dpd = <35000>;
 };
};

arduino_serial: &uart1 {
 compatible = "nordic,nrf-uarte";
 current-speed = <115200>;
 pinctrl-0 = <&uart1_default>;
 pinctrl-1 = <&uart1_sleep>;
 pinctrl-names = "default", "sleep";
};

arduino_i2c: &i2c1 {};

arduino_spi: &spi4 {
 compatible = "nordic,nrf-spim";
 status = "okay";
 cs-gpios = <&arduino_header 16 (1 << 0)>;
 pinctrl-0 = <&spi4_default>;
 pinctrl-1 = <&spi4_sleep>;
 pinctrl-names = "default", "sleep";
};

&ieee802154 {
 status = "okay";
};

zephyr_udc0: &usbd {
 compatible = "nordic,nrf-usbd";
 status = "okay";
};


# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_cpuapp_partition.dtsi" 1 3 4
# 9 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_cpuapp_partition.dtsi" 3 4
&flash0 {
# 27 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_cpuapp_partition.dtsi" 3 4
 partitions {
  compatible = "fixed-partitions";
  #address-cells = <1>;
  #size-cells = <1>;

  boot_partition: partition@0 {
   label = "mcuboot";
   reg = <0x00000000 0x10000>;
  };
  slot0_partition: partition@10000 {
   label = "image-0";
   reg = <0x00010000 0x40000>;
  };
  slot0_ns_partition: partition@50000 {
   label = "image-0-nonsecure";
   reg = <0x00050000 0x30000>;
  };
  slot1_partition: partition@80000 {
   label = "image-1";
   reg = <0x00080000 0x40000>;
  };
  slot1_ns_partition: partition@c0000 {
   label = "image-1-nonsecure";
   reg = <0x000c0000 0x30000>;
  };
  tfm_ps_partition: partition@f0000 {
   label = "tfm-ps";
   reg = <0x000f0000 0x00004000>;
  };
  tfm_its_partition: partition@f4000 {
   label = "tfm-its";
   reg = <0x000f4000 0x00002000>;
  };
  tfm_otp_partition: partition@f6000 {
   label = "tfm-otp";
   reg = <0x000f6000 0x00002000>;
  };
  storage_partition: partition@f8000 {
   label = "storage";
   reg = <0x000f8000 0x00008000>;
  };
 };
};

/ {
# 80 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_cpuapp_partition.dtsi" 3 4
 reserved-memory {
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;

  sram0_image: image@20000000 {

   reg = <0x20000000 ((448) * 1024)>;
  };

  sram0_s: image_s@20000000 {

   reg = <0x20000000 0x40000>;
  };

  sram0_ns: image_ns@20040000 {

   reg = <0x20040000 0x40000>;
  };

  sram0_ns_app: image_ns_app@20040000 {

   reg = <0x20040000 0x30000>;
  };
 };
};

# 1 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_shared_sram_partition.dtsi" 1 3 4
# 20 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_shared_sram_partition.dtsi" 3 4
/ {
 chosen {
  zephyr,ipc_shm = &sram0_shared;
 };

 reserved-memory {
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;

  sram0_shared: memory@20070000 {

   reg = <0x20070000 0x10000>;
  };
 };
};
# 108 "C:/ncs/v2.9.0-zigbee/zephyr/dts/common/nordic/nrf5340_cpuapp_partition.dtsi" 2 3 4
# 164 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340_cpuapp_common.dtsi" 2
# 10 "C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk/nrf5340dk_nrf5340_cpuapp.dts" 2

/ {
 model = "Nordic NRF5340 DK NRF5340 Application";
 compatible = "nordic,nrf5340-dk-nrf5340-cpuapp";

 chosen {
  zephyr,sram = &sram0_image;
  zephyr,flash = &flash0;
  zephyr,code-partition = &slot0_partition;
  zephyr,sram-secure-partition = &sram0_s;
  zephyr,sram-non-secure-partition = &sram0_ns;
 };
};
# 0 "<command-line>" 2
# 1 "C:/ncs/zy/sw2600/boards/nrf5340dk_nrf5340_cpuapp.overlay" 1






 / {
 chosen {
  nordic,pm-ext-flash = &mx25r64;
  ncs,zigbee-timer = &timer2;
        zephyr,console = &uart0;
        zephyr,shell-uart = &uart0;
    };

    aliases {
        led0 = &led0;
        led1 = &led1;
        sw0 = &button0;
        bootloader-led0 = &led0;
        mcuboot-button0 = &button0;
        mcuboot-led0 = &led0;
        mic-power = &mic_power;
     adxl382-pwr-ctrl = &adxl382_pwr_ctrl;



        watchdog0 = &wdt0;
    };

    leds {
        compatible = "gpio-leds";
        led0: led_0 {
            gpios = <&gpio0 5 (1 << 0)>;
        };
        led1: led_1 {
            gpios = <&gpio0 6 (1 << 0)>;
        };
    };

    buttons {
        compatible = "gpio-keys";
        button0: button_0 {
            gpios = <&gpio0 7 ((1 << 0) | (1 << 4))>;
            label = "Push button 1";
        };
    };

    mic_power: mic_power {
        compatible = "regulator-fixed";
        label = "MIC_VDD";
        regulator-name = "mic-vdd";
        enable-gpios = <&gpio1 4 (0 << 0)>;
        startup-delay-us = <100000>;
        regulator-min-microvolt = <1800000>;
        regulator-max-microvolt = <1800000>;
        status = "okay";
    };

    adxl382_pwr_ctrl: adxl382-pwr-ctrl {
        compatible = "regulator-fixed";
        regulator-name = "tck106ag";
        enable-gpios = <&gpio0 26 (0 << 0)>;
        regulator-boot-on;
        regulator-always-on;
        startup-delay-us = <10000>;
        regulator-min-microvolt = <3300000>;
        regulator-max-microvolt = <3300000>;
        status = "okay";
    };

    gpio_fwd: nrf-gpio-forwarder {
  compatible = "nordic,nrf-gpio-forwarder";
  status = "okay";
  nrf21540-spi-if {
   gpios = <&gpio1 3 0>,
                   <&gpio0 29 0>,
                   <&gpio0 20 0>,
                   <&gpio0 28 0>;
  };

  nrf21540-gpio-if {
   gpios = <&gpio0 31 0>,
                   <&gpio0 30 0>,
                   <&gpio1 10 0>,
                   <&gpio1 11 0>;
  };
 };
};

&pinctrl {
    uart0_default: uart0_default {
        group1 {
            psels = <((((((1) * 32U) + (12)) & 0x1FFU) << 0U) | ((0U & 0xFFU) << 24U))>;
        };

        group2 {
            psels = <((((((1) * 32U) + (14)) & 0x1FFU) << 0U) | ((1U & 0xFFU) << 24U))>;
        };
    };

    uart0_sleep: uart0_sleep {
        group1 {
            psels = <((((((1) * 32U) + (12)) & 0x1FFU) << 0U) | ((0U & 0xFFU) << 24U))>,
                    <((((((1) * 32U) + (14)) & 0x1FFU) << 0U) | ((1U & 0xFFU) << 24U))>;
            low-power-enable;
        };
    };

    i2c1_default: i2c1_default {
        group1 {
            psels = <((((((1) * 32U) + (8)) & 0x1FFU) << 0U) | ((11U & 0xFFU) << 24U))>,
                    <((((((0) * 32U) + (24)) & 0x1FFU) << 0U) | ((12U & 0xFFU) << 24U))>;
        };
    };

    i2c1_sleep: i2c1_sleep {
        group1 {
            psels = <((((((1) * 32U) + (8)) & 0x1FFU) << 0U) | ((11U & 0xFFU) << 24U))>,
                    <((((((0) * 32U) + (24)) & 0x1FFU) << 0U) | ((12U & 0xFFU) << 24U))>;
            low-power-enable;
        };
    };

    i2s0_default: i2s0_default {
        group1 {
            psels = <((((((1) * 32U) + (6)) & 0x1FFU) << 0U) | ((13U & 0xFFU) << 24U))>,
                    <((((((0) * 32U) + (19)) & 0x1FFU) << 0U) | ((15U & 0xFFU) << 24U))>,
                    <((((((0) * 32U) + (21)) & 0x1FFU) << 0U) | ((17U & 0xFFU) << 24U))>;
            bias-pull-down;
            nordic,drive-mode = <3U>;
        };
    };

    i2s0_sleep: i2s0_sleep {
        group1 {
            psels = <((((((1) * 32U) + (6)) & 0x1FFU) << 0U) | ((13U & 0xFFU) << 24U))>,
                    <((((((0) * 32U) + (19)) & 0x1FFU) << 0U) | ((15U & 0xFFU) << 24U))>,
                    <((((((0) * 32U) + (21)) & 0x1FFU) << 0U) | ((17U & 0xFFU) << 24U))>;
            low-power-enable;
        };
    };

    spi2_default: spi2_default {
        group1 {
            psels = <((((((0) * 32U) + (23)) & 0x1FFU) << 0U) | ((4U & 0xFFU) << 24U))>,
                    <((((((1) * 32U) + (5)) & 0x1FFU) << 0U) | ((5U & 0xFFU) << 24U))>,
                    <((((((1) * 32U) + (7)) & 0x1FFU) << 0U) | ((6U & 0xFFU) << 24U))>;
        };
    };

    spi2_sleep: spi2_sleep {
        group1 {
            psels = <((((((0) * 32U) + (23)) & 0x1FFU) << 0U) | ((4U & 0xFFU) << 24U))>,
                    <((((((1) * 32U) + (5)) & 0x1FFU) << 0U) | ((5U & 0xFFU) << 24U))>,
                    <((((((1) * 32U) + (7)) & 0x1FFU) << 0U) | ((6U & 0xFFU) << 24U))>;
            low-power-enable;
        };
    };
};


&uart3 {
    status = "disabled";
};

&i2c1 {
    status = "disabled";
# 179 "C:/ncs/zy/sw2600/boards/nrf5340dk_nrf5340_cpuapp.overlay"
};

&i2s0 {
    status = "disabled";
};

&clock {
 status = "okay";
};

&spi2 {
    status = "disabled";
};

&vregmain {
 regulator-initial-mode = <1>;
};

&vregradio {
 regulator-initial-mode = <1>;
};

&gpio0 {
    status = "okay";
};

&gpio1 {
    status = "okay";
};

&uart0 {
    status = "okay";
    current-speed = <115200>;
};

&uart1 {
    status = "disabled";
};

&qspi {
    status = "disabled";
};

&spi4 {
    status = "disabled";
};

&button1 {
    status = "disabled";
};

&button2 {
    status = "disabled";
};

&button3 {
    status = "disabled";
};

&led2 {
    status = "disabled";
};

&led3 {
    status = "disabled";
};

&timer2 {
 status = "disabled";
};

&timer0 {
 status = "disabled";
};

&timer1 {
 status = "disabled";
};

&wdt0 {
    status = "disabled";
};

&pwm0 {
 status = "disabled";
};

&adc {
 status = "disabled";
};

&usbd {
 status = "disabled";
};

&qspi {
 status = "disabled";
};
# 0 "<command-line>" 2
# 1 "C:/ncs/v2.9.0-zigbee/zephyr/misc/empty_file.c"
