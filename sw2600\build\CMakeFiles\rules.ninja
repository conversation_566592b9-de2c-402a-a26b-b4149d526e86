# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: sw2600
# Configurations: MinSizeRel
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__app_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__app_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__zephyr_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__offsets_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_pre0_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__zephyr_pre0_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $FLAGS $LINK_FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES -L"c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard" -lm -lc -lgcc -lc && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_final_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__zephyr_final_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $FLAGS $LINK_FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES -L"c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard" -lm -lc -lgcc -lc && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__common_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__common_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__isr_tables_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__isr_tables_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__arch__arm__core_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__arch__arm__core__cortex_m_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__cortex_m_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__cortex_m_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__cortex_m__cmse_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__cortex_m__cmse_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__mpu_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__mpu_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__libc__newlib_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__libc__newlib_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__libc__common_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__libc__common_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__posix__options_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__posix__options_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__soc__nordic_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__soc__nordic_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__interrupt_controller_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__interrupt_controller_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__clock_control_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__clock_control_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__console_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__console_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__gpio_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__gpio_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__i2c_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__i2c_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__i2s_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__i2s_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__mbox_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__mbox_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__pinctrl_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__pinctrl_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__serial_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__serial_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__timer_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__timer_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__.2e.2e__nrf__drivers__hw_cc3xx_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__.2e.2e__nrf__drivers__hw_cc3xx_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__modules__hal_nordic__nrfx_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__modules__hal_nordic__nrfx_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__kernel_unscanned_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__kernel_MinSizeRel
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\b620d30767\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SC:\ncs\zy\sw2600 -BC:\ncs\zy\sw2600\build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning additional files.

rule CLEAN_ADDITIONAL
  command = "C:\Program Files\CMake\bin\cmake.exe" -DCONFIG=$CONFIG -P CMakeFiles\clean_additional.cmake
  description = Cleaning additional files...


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\ncs\toolchains\b620d30767\opt\bin\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\ncs\toolchains\b620d30767\opt\bin\ninja.exe -t targets
  description = All primary targets available:

