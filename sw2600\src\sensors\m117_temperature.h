/**
 * @file m117_temperature.h
 * @brief M117 Temperature Sensor Driver Header
 * 
 * GPIO I2C implementation for M117 temperature sensor
 * Optimized for ultra-low power consumption
 */

#ifndef M117_TEMPERATURE_H
#define M117_TEMPERATURE_H

#include <zephyr/kernel.h>
#include <zephyr/drivers/gpio.h>

/**
 * @brief Initialize M117 temperature sensor
 * @return 0 on success, negative error code on failure
 */
int m117_init(void);

/**
 * @brief Read temperature from M117 sensor
 * @param temperature Pointer to store temperature value in Celsius
 * @return 0 on success, negative error code on failure
 */
int m117_read_temperature(float *temperature);

/**
 * @brief Deinitialize M117 temperature sensor
 * @return 0 on success, negative error code on failure
 */
int m117_deinit(void);

#endif /* M117_TEMPERATURE_H */
