/*
 * Simple Power Manager for SW2600 Temperature Integration
 * 
 * Simplified version focusing on basic low power functionality
 * without complex dependencies
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/pm/device.h>
#include <zephyr/logging/log.h>
#include "simple_power_manager.h"

LOG_MODULE_REGISTER(simple_power_mgr, LOG_LEVEL_INF);

/* Global state */
static bool power_manager_initialized = false;

/**
 * @brief Initialize simple power manager
 */
int simple_power_manager_init(void)
{
    if (power_manager_initialized) {
        return 0;
    }
    
    LOG_INF("Simple power manager initialized");
    power_manager_initialized = true;
    
    return 0;
}

/**
 * @brief Suspend UART device
 */
static int suspend_uart(void)
{
    const struct device *uart_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    
    if (uart_dev && device_is_ready(uart_dev)) {
        int ret = pm_device_action_run(uart_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            LOG_WRN("Failed to suspend UART: %d", ret);
            return ret;
        }
        LOG_DBG("UART suspended");
    }
    
    return 0;
}

/**
 * @brief Suspend I2C device
 */
static int suspend_i2c(void)
{
    const struct device *i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            LOG_WRN("Failed to suspend I2C: %d", ret);
            return ret;
        }
        LOG_DBG("I2C suspended");
    }
    
    return 0;
}

/**
 * @brief Resume I2C device
 */
static int resume_i2c(void)
{
    const struct device *i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
        if (ret < 0) {
            LOG_WRN("Failed to resume I2C: %d", ret);
            return ret;
        }
        LOG_DBG("I2C resumed");
    }
    
    return 0;
}

/**
 * @brief Enter deep sleep mode
 */
int simple_power_manager_sleep(uint32_t sleep_duration_seconds)
{
    if (!power_manager_initialized) {
        LOG_ERR("Power manager not initialized");
        return -ENODEV;
    }
    
    LOG_INF("=== ENTERING DEEP SLEEP MODE ===");
    LOG_INF("Sleep duration: %d seconds", sleep_duration_seconds);
    LOG_INF("Expected current: ~3.5µA");
    
    /* Small delay to ensure log output */
    k_msleep(100);
    
    /* Suspend peripherals */
    suspend_i2c();
    suspend_uart();
    
    /* Enter system off mode */
    sys_poweroff();
    
    /* Should not reach here */
    LOG_ERR("Failed to enter deep sleep mode");
    return -EIO;
}

/**
 * @brief Resume peripherals after wake up
 */
int simple_power_manager_resume(void)
{
    if (!power_manager_initialized) {
        LOG_ERR("Power manager not initialized");
        return -ENODEV;
    }
    
    LOG_INF("=== RESUMING FROM SLEEP ===");
    
    /* Resume I2C device */
    resume_i2c();
    
    return 0;
}
