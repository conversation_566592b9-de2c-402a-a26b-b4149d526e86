/**
 * @file t5848_microphone.h
 * @brief T5848 Microphone Sensor Driver Header
 * 
 * I2S implementation for T5848 microphone sensor
 * Optimized for ultra-low power consumption
 */

#ifndef T5848_MICROPHONE_H
#define T5848_MICROPHONE_H

#include <zephyr/kernel.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/i2s.h>

/**
 * @brief Initialize T5848 microphone sensor
 * @return 0 on success, negative error code on failure
 */
int t5848_init(void);

/**
 * @brief Read sound level from T5848 microphone
 * @param sound_db Pointer to store sound level in dB
 * @return 0 on success, negative error code on failure
 */
int t5848_read_sound_level(float *sound_db);

/**
 * @brief Deinitialize T5848 microphone sensor
 * @return 0 on success, negative error code on failure
 */
int t5848_deinit(void);

#endif /* T5848_MICROPHONE_H */
