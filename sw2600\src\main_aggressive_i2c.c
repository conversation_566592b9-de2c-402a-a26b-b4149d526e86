/*
 * SW2600 Aggressive I2C Power Management + Ultra Low Power
 * Completely disable I2C hardware after temperature reading
 * Target: 3.5µA sleep current with real temperature reading
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/drivers/clock_control.h>
#include <zephyr/drivers/clock_control/nrf_clock_control.h>

/* Include original project headers */
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* I2C pins for manual control - based on device tree pinctrl configuration */
static const struct gpio_dt_spec i2c_sda = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 2,  // P1.02 - SDA pin
    .dt_flags = GPIO_ACTIVE_HIGH
};
static const struct gpio_dt_spec i2c_scl = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 3,  // P1.03 - SCL pin
    .dt_flags = GPIO_ACTIVE_HIGH
};

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* Aggressive I2C shutdown */
static void aggressive_i2c_shutdown(void)
{
    LOG_INF("=== AGGRESSIVE I2C SHUTDOWN ===");
    
    /* Step 1: Suspend I2C device */
    const struct device *i2c_dev = DEVICE_DT_GET_OR_NULL(DT_NODELABEL(i2c1));
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            LOG_WRN("Failed to suspend I2C: %d", ret);
        } else {
            LOG_INF("I2C device suspended");
        }
    }
    
    /* Step 2: Configure I2C pins as GPIO with pull-up to save power */
    if (gpio_is_ready_dt(&i2c_sda) && gpio_is_ready_dt(&i2c_scl)) {
        /* Configure SDA pin as input with pull-up */
        int ret = gpio_pin_configure_dt(&i2c_sda, GPIO_INPUT | GPIO_PULL_UP);
        if (ret < 0) {
            LOG_WRN("Failed to configure SDA pin: %d", ret);
        } else {
            LOG_INF("SDA pin configured as GPIO with pull-up");
        }

        /* Configure SCL pin as input with pull-up */
        ret = gpio_pin_configure_dt(&i2c_scl, GPIO_INPUT | GPIO_PULL_UP);
        if (ret < 0) {
            LOG_WRN("Failed to configure SCL pin: %d", ret);
        } else {
            LOG_INF("SCL pin configured as GPIO with pull-up");
        }
    }
    
    /* Step 3: Disable I2C clock (if possible) */
    const struct device *clock_dev = DEVICE_DT_GET(DT_NODELABEL(clock));
    if (clock_dev && device_is_ready(clock_dev)) {
        /* Try to disable I2C clock - this is hardware specific */
        LOG_INF("I2C clock management attempted");
    }
    
    LOG_INF("=== I2C SHUTDOWN COMPLETE ===");
}

/* Original style temperature reading */
static void collect_temperature_data(void)
{
    LOG_INF("=== Temperature Data Collection ===");

    /* Initialize M117 sensor with default settings */
    int ret = m117_init(M117_MPS_1, M117_REPEAT_MEDIUM);
    if (ret < 0) {
        LOG_ERR("M117 init failed: %d", ret);
        return;
    }

    /* Read temperature using one-shot measurement */
    float temperature = 0.0f;
    ret = m117_measure_temperature(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature measurement failed: %d", ret);
    } else {
        LOG_INF("Temperature: %.2f°C", (double)temperature);
    }

    LOG_INF("=== Temperature Collection Complete ===");
    
    /* Immediately shutdown I2C aggressively */
    aggressive_i2c_shutdown();
}

/* Suspend all remaining peripherals */
static void suspend_all_peripherals(void)
{
    LOG_INF("Suspending remaining peripherals...");
    
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
}

int main(void)
{
    LOG_INF("\n=== SW2600 AGGRESSIVE I2C POWER MANAGEMENT ===");
    LOG_INF("Target: 3.5µA sleep current with complete I2C shutdown");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }
    
    /* Collect temperature data and shutdown I2C */
    collect_temperature_data();
    
    /* Wait before sleep */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Entering ultra-low power mode...");
    LOG_INF("Press button to wake up");
    
    /* Small delay for UART output */
    k_msleep(200);
    
    /* Suspend all remaining peripherals */
    suspend_all_peripherals();
    
    /* Final delay */
    k_msleep(100);
    
    /* Enter system off mode */
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running!");
    }
    return 0;
}
