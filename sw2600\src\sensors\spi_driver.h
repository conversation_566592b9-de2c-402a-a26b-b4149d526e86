/**
 * @file spi_driver.h
 * @brief SPI Driver Header for ADXL382
 * 
 * Zephyr SPI driver wrapper for ADXL382 communication
 */

#ifndef SPI_DRIVER_H
#define SPI_DRIVER_H

#include <stdint.h>

/**
 * @brief Initialize SPI device for ADXL382
 * @return 0 on success, negative error code on failure
 */
int spi_driver_init(void);

/**
 * @brief Read ADXL382 register
 * @param reg_addr Register address
 * @param data Data buffer
 * @param bytes_number Number of bytes to read
 * @return 0 on success, negative error code on failure
 */
int spi_reg_read(uint8_t reg_addr, uint8_t *data, uint16_t bytes_number);

/**
 * @brief Write ADXL382 register
 * @param reg_addr Register address
 * @param data Data buffer
 * @param bytes_number Number of bytes to write
 * @return 0 on success, negative error code on failure
 */
int spi_reg_write(uint8_t reg_addr, const uint8_t *data, uint16_t bytes_number);

/**
 * @brief Update ADXL382 register bits
 * @param reg_addr Register address
 * @param mask Bit mask
 * @param data New data value
 * @return 0 on success, negative error code on failure
 */
int spi_reg_update_bits(uint8_t reg_addr, uint8_t mask, uint8_t data);

#endif /* SPI_DRIVER_H */
