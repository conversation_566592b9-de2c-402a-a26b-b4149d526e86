#
# Copyright (c) 2020 Nordic Semiconductor ASA
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#
# SW2600 Simple Low Power Configuration
# Based on Nordic system_off example for optimal power consumption

# ========== 基础系统配置 ==========
CONFIG_NCS_SAMPLES_DEFAULTS=n

# GPIO支持 (GPIO I2C需要)
CONFIG_GPIO=y

# I2C支持 (智能电源管理)
CONFIG_I2C=y
CONFIG_PM_DEVICE_RUNTIME=y

# I2S支持 (T5848麦克风)
CONFIG_I2S=y

# 时钟配置
CONFIG_CLOCK_CONTROL=y
CONFIG_CLOCK_CONTROL_NRF=y
CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL=y

# ========== 低功耗配置 (完全按照Nordic system_off示例) ==========
CONFIG_PM_DEVICE=y
CONFIG_GPIO=y
CONFIG_CRC=y
CONFIG_POWEROFF=y

# 禁用不必要的功能以降低功耗
CONFIG_TIMESLICING=n
CONFIG_THREAD_MONITOR=n
CONFIG_THREAD_NAME=n
CONFIG_THREAD_STACK_INFO=n
CONFIG_DEBUG=n
CONFIG_DEBUG_INFO=n
CONFIG_ASSERT=n

# 禁用未使用的外设
CONFIG_SPI=n
CONFIG_PWM=n
CONFIG_ADC=n
CONFIG_COUNTER=n

# ========== 调试配置 (临时启用详细日志) ==========
# 详细日志用于调试
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_BACKEND_UART=y
CONFIG_PRINTK=y
CONFIG_CONSOLE=y
CONFIG_UART_CONSOLE=y
CONFIG_SERIAL=y
CONFIG_BOOT_BANNER=y

# 浮点支持
CONFIG_FPU=y
CONFIG_FP_HARDABI=y
CONFIG_CBPRINTF_FP_SUPPORT=y

# 数学库配置
CONFIG_NEWLIB_LIBC=y
CONFIG_NEWLIB_LIBC_FLOAT_PRINTF=y

# ========== 优化配置 ==========
CONFIG_SIZE_OPTIMIZATIONS=y

# ========== DK库支持 (暂时禁用) ==========
# CONFIG_DK_LIBRARY=y


