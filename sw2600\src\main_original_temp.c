/*
 * SW2600 Original Style Temperature + Ultra Low Power
 * Based on working reference project implementation
 * Target: 3.5µA sleep current with real temperature reading
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>

/* Include original project headers */
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* Original style temperature reading */
static void collect_temperature_data(void)
{
    LOG_INF("=== Temperature Data Collection ===");

    /* Initialize M117 sensor with default settings */
    int ret = m117_init(M117_MPS_1, M117_REPEAT_MEDIUM);
    if (ret < 0) {
        LOG_ERR("M117 init failed: %d", ret);
        return;
    }

    /* Read temperature using one-shot measurement */
    float temperature = 0.0f;
    ret = m117_measure_temperature(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature measurement failed: %d", ret);
    } else {
        LOG_INF("Temperature: %.2f°C", (double)temperature);
    }

    LOG_INF("=== Temperature Collection Complete ===");
}

/* Aggressive power management for I2C */
static void suspend_all_peripherals(void)
{
    LOG_INF("Suspending peripherals for power saving...");
    
    /* Suspend I2C device */
    const struct device *i2c_dev = DEVICE_DT_GET_OR_NULL(DT_NODELABEL(i2c1));
    if (i2c_dev && device_is_ready(i2c_dev)) {
        int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
        if (ret < 0) {
            LOG_WRN("Failed to suspend I2C: %d", ret);
        } else {
            LOG_INF("I2C suspended");
        }
    }
    
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
}

int main(void)
{
    LOG_INF("\n=== SW2600 ORIGINAL STYLE TEMP + ULTRA LOW POWER ===");
    LOG_INF("Target: 3.5µA sleep current with real temperature");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }
    
    /* Collect temperature data using original implementation */
    collect_temperature_data();
    
    /* Wait before sleep */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Entering ultra-low power mode...");
    LOG_INF("Press button to wake up");
    
    /* Small delay for UART output */
    k_msleep(200);
    
    /* Suspend all peripherals aggressively */
    suspend_all_peripherals();
    
    /* Final delay */
    k_msleep(100);
    
    /* Enter system off mode */
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running!");
    }
    return 0;
}
