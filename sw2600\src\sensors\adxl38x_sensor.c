#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/logging/log.h>
#include <stdlib.h>
#include <zephyr/sys/byteorder.h> // 用于 sys_get_le32
#include "adxl38x_sensor.h"
#include "spi_driver.h"
#include <math.h> // 为了 sqrtf
#include <zephyr/kernel.h> // Needed for K_SEM_DEFINE, k_sem, etc.
#include <zephyr/drivers/gpio.h> // Needed for gpio_callback

LOG_MODULE_REGISTER(adxl38x_sensor, CONFIG_LOG_DEFAULT_LEVEL);      //CONFIG_LOG_MAX_LEVEL

/* GPIO设备 */
static const struct gpio_dt_spec pwr_gpio = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio0)),
    .pin = 26,
    .dt_flags = GPIO_ACTIVE_LOW
};

/* ADXL38X寄存器地址定义 - 添加 INT1_MAP0 */
#define ADXL38X_INT1_MAP0               0x2D

// --- Interrupt Handling Globals (Using INT1) ---
// Define the interrupt pin using device tree alias or node property
static const struct gpio_dt_spec adxl382_int1_gpio = {
    .port = DEVICE_DT_GET(DT_NODELABEL(gpio1)),
    .pin = 6,
    .dt_flags = GPIO_ACTIVE_HIGH
};

static struct gpio_callback adxl382_int1_cb_data;
static K_SEM_DEFINE(fifo_watermark_sem, 0, 1); // Semaphore to signal FIFO watermark interrupt

// ISR Handler function (Renamed)
static void adxl382_int1_handler(const struct device *port __attribute__((unused)),
                                 struct gpio_callback *cb __attribute__((unused)),
                                 gpio_port_pins_t pins __attribute__((unused)))
{
    k_sem_give(&fifo_watermark_sem);
}
// --- End Interrupt Handling Globals ---

/**
 * @brief 初始化ADXL38X所需的GPIO
 *
 * @return int 成功返回0，失败返回错误码
 */
static int adxl38x_gpio_init(void)
{
    /* 获取电源控制GPIO */
    if (!device_is_ready(pwr_gpio.port)) {
        LOG_ERR("ADXL38X power control GPIO not ready");
        return -ENODEV;
    }

    /* 配置GPIO方向 */
    int ret = gpio_pin_configure_dt(&pwr_gpio, GPIO_OUTPUT_ACTIVE); // 使用 GPIO_OUTPUT_ACTIVE
    if (ret) {
        LOG_ERR("Failed to configure power control GPIO: %d", ret);
        return ret;
    }

    /* 延时，等待电源稳定 */
    k_msleep(10); // 保持或根据需要调整

    return 0;
}


/**
 * @brief 初始化ADXL38X传感器
 *
 * @param op_mode 工作模式
 * @param range 量程设置
 * @return int 成功返回0，失败返回错误码
 */
/* 静态缓冲区，避免栈分配 */
static uint8_t g_status_buf[4];
static uint8_t g_device_id_buf[3];

int adxl38x_sensor_init(uint8_t op_mode, uint8_t range)
{
    int ret;
    uint32_t status_val; // 用于组合状态值

    LOG_INF("Initializing ADXL38X sensor...");

    /* 初始化GPIO (电源控制) */
    ret = adxl38x_gpio_init();
    if (ret) {
        LOG_ERR("ADXL38X GPIO initialization failed: %d", ret);
        return ret;
    }

    /* 初始化SPI */
    ret = spi_driver_init();
    if (ret) {
        LOG_ERR("SPI initialization failed: %d", ret);
        return ret;
    }

    /* 软复位 */
    uint8_t reset_cmd = ADXL38X_RESET_CODE;
    ret = spi_reg_write(ADXL38X_REG_RESET, &reset_cmd, 1);
    if (ret) {
        LOG_ERR("ADXL38X software reset write failed: %d", ret);
        return ret;
    }
    k_msleep(5); // 等待复位完成

    /* 读取并验证设备ID - 使用静态缓冲区 */
    ret = spi_reg_read(ADXL38X_DEVID_AD, &g_device_id_buf[0], 1);
    if (ret || g_device_id_buf[0] != ADXL38X_RESET_DEVID_AD) {
        LOG_ERR("ADXL38X DEVID_AD mismatch or read error (Read:0x%02X, Expected:0x%02X, ret:%d)",
                 g_device_id_buf[0], ADXL38X_RESET_DEVID_AD, ret);
         return ret ? ret : -EIO;
    }

    ret = spi_reg_read(ADXL38X_DEVID_MST, &g_device_id_buf[1], 1);
    if (ret || g_device_id_buf[1] != ADXL38X_RESET_DEVID_MST) {
        LOG_ERR("ADXL38X DEVID_MST mismatch or read error (Read:0x%02X, Expected:0x%02X, ret:%d)",
                g_device_id_buf[1], ADXL38X_RESET_DEVID_MST, ret);
        return ret ? ret : -EIO;
    }

    ret = spi_reg_read(ADXL38X_PART_ID, &g_device_id_buf[2], 1);
    if (ret || g_device_id_buf[2] != ADXL38X_RESET_PART_ID) {
        LOG_ERR("ADXL38X PART_ID mismatch or read error (Read:0x%02X, Expected:0x%02X, ret:%d)",
                g_device_id_buf[2], ADXL38X_RESET_PART_ID, ret);
        return ret ? ret : -EIO;
    }

    LOG_DBG("ADXL38X device ID verified (AD:0x%X, MST:0x%X, PART:0x%X)",
            g_device_id_buf[0], g_device_id_buf[1], g_device_id_buf[2]);

    /* *** 新增：读取初始状态寄存器 - 使用静态缓冲区 *** */
    ret = spi_reg_read(ADXL38X_STATUS0, g_status_buf, 4);
    if (ret) {
        LOG_ERR("Failed to read status registers after reset: %d", ret);
        // 即使状态读取失败，也可能继续尝试配置
        // return ret; // 或者选择不返回错误，仅记录警告
    } else {
        // 将4个字节组合成一个32位值 (小端模式读取)
        status_val = sys_get_le32(g_status_buf);
        LOG_DBG("Initial Status Register Value (0-3): 0x%08X", status_val);
        // 可以添加对特定状态位的检查，例如 NVM_BUSY_STATUS
        if (g_status_buf[0] & 0x80) { // 检查 STATUS0 的 NVM_BUSY_STATUS 位
            LOG_WRN("Warning: NVM busy flag is set after reset.");
        }
    }

    /* 设置工作模式 */
    ret = adxl38x_sensor_set_mode(op_mode);
    if (ret) {
         LOG_ERR("Failed to set ADXL38X operation mode to %d: %d", op_mode, ret);
        return ret;
    }

    /* 设置量程 */
    ret = adxl38x_sensor_set_range(range);
     if (ret) {
         LOG_ERR("Failed to set ADXL38X range to %d: %d", range, ret);
        return ret;
    }

    /* 简化：暂时跳过OP_MODE验证以减少栈使用 */
    LOG_DBG("OP_MODE and Range set, skipping readback verification for now");

    /* 配置FIFO (禁用) */
    ret = adxl38x_sensor_config_fifo(0, ADXL38X_FIFO_DISABLE, false, false);
     if (ret) {
         LOG_ERR("Failed to configure ADXL38X FIFO: %d", ret);
        // FIFO配置失败可能不影响基本读写，可以选择不返回错误
        // return ret;
     }

    /* 启用XYZ通道 */
    ret = spi_reg_update_bits(ADXL38X_DIG_EN,
                                     ADXL38X_MASK_CHEN_DIG_EN,
                                     ADXL38X_CH_EN_XYZ << 4);
    if (ret) {
        LOG_ERR("Failed to enable ADXL38X XYZ channels: %d", ret);
        return ret;
    }
    LOG_DBG("ADXL38X XYZ channels enabled.");

    /* 配置FILTER寄存器（与FIFO采集时保持一致） */
    uint8_t filter_reg_val_to_write = 0x70; // 参考官方示例：Bypass EQ, LPF_MODE 0b11
    LOG_DBG("Setting FILTER register (0x50) to 0x%02X for Bypass EQ filter mode.", filter_reg_val_to_write);
    int filter_ret = spi_reg_write(ADXL38X_FILTER /* 0x50 */, &filter_reg_val_to_write, 1);
    if (filter_ret != 0) {
        LOG_WRN("Failed to write FILTER register (0x50): %d", filter_ret);
        // 不返回错误，继续初始化
    }

    LOG_DBG("ADXL38X sensor initialization sequence completed.");

    return 0;
}

/**
 * @brief 采集ADXL382振动数据 - 使用稳定的直接读取模式
 *
 * @param data 数据缓冲区
 * @param max_count 最大采集数量
 * @return int 成功返回采集的数据数量，失败返回负数
 */
int adxl38x_collect_data(raw_accel_data_t *data, uint32_t max_count)
{
    int ret;
    uint32_t collected = 0;
    struct RawAccelData *fifo_buffer;
    uint32_t samples_collected = 0;

    if (!data || max_count == 0) {
        LOG_ERR("Invalid parameters for data collection");
        return -EINVAL;
    }

    LOG_DBG("Starting FIFO-based sensor data collection: %u samples", max_count);

    uint32_t start_time = k_uptime_get_32();

    /* 分配FIFO缓冲区（包含额外的32个样本） */
    uint32_t buffer_size = max_count + 32;
    fifo_buffer = k_malloc(buffer_size * sizeof(struct RawAccelData));
    if (!fifo_buffer) {
        LOG_ERR("Failed to allocate FIFO buffer for %u samples", buffer_size);
        return -ENOMEM;
    }

    /* 使用FIFO中断方式采集数据 */
    uint16_t fifo_watermark = 64;   // 减小水印值
    uint16_t odr_hz = 16000;        // 16kHz采样率
    uint32_t max_wait_time_ms = 3000; // 减少等待时间
    uint8_t num_axes_enabled = 3;   // XYZ三轴

    /* 参考测试文件配置FIFO和ODR */
    uint8_t dig_en_val;
    ret = spi_reg_read(ADXL38X_DIG_EN, &dig_en_val, 1);
    if (ret == 0) {
        LOG_DBG("Current DIG_EN before FIFO: 0x%02X", dig_en_val);

        /* 1. 设置TRIG_CFG寄存器配置ODR (参考测试文件) */
        uint8_t trig_cfg_val;
        if (odr_hz == 16000) {
            trig_cfg_val = 0x00;  // 16kHz
        } else if (odr_hz == 32000) {
            trig_cfg_val = 0x80;  // 32kHz
        } else {
            trig_cfg_val = 0x00;  // 默认16kHz
            LOG_WRN("Unsupported ODR %u Hz, using 16kHz (TRIG_CFG=0x00)", (unsigned int)odr_hz);
        }
        ret = spi_reg_write(0x49 /*TRIG_CFG ADDR*/, &trig_cfg_val, 1);
        if (ret == 0) {
            LOG_DBG("TRIG_CFG set to: 0x%02X for ODR %u Hz", trig_cfg_val, (unsigned int)odr_hz);
        }

        /* 2. 设置FILTER寄存器为0x70 (参考官方示例：Bypass EQ, LPF_MODE 0b11) */
        uint8_t filter_val = 0x70;
        ret = spi_reg_write(ADXL38X_FILTER, &filter_val, 1);
        if (ret == 0) {
            LOG_DBG("FILTER set to: 0x%02X (Bypass EQ, LPF_MODE 0b11)", filter_val);
        }

        /* 3. 启用FIFO和XYZ通道 (0x78) */
        uint8_t new_dig_en = 0x78; // 参考测试文件的精确值
        ret = spi_reg_write(ADXL38X_DIG_EN, &new_dig_en, 1);
        if (ret == 0) {
            LOG_DBG("DIG_EN set to: 0x%02X (XYZ + FIFO enabled)", new_dig_en);
        }
    }

    /* 采集额外的32个样本用于丢弃，确保数据稳定性 */
    uint32_t target_samples = max_count + 32;

    ret = adxl38x_sensor_stream_raw_data_fifo_int(
        fifo_watermark,
        odr_hz,
        target_samples,  // 采集更多样本
        max_wait_time_ms,
        num_axes_enabled,
        fifo_buffer,
        target_samples,  // 缓冲区大小也要相应增加
        &samples_collected
    );

    if (ret < 0) {
        LOG_ERR("FIFO data collection failed: %d", ret);
        k_free(fifo_buffer);
        return ret;
    }

    /* 转换数据格式并丢弃前32个数据点（参考测试文件的稳定方法） */
    uint32_t copy_count = MIN(samples_collected, max_count);
    const uint32_t discard_points = 32;  // 参考adxl38x_tests.c的方法
    uint32_t start_index = discard_points;

    LOG_DBG("FIFO data: discarding first %u samples for stability (total collected: %u)",
            discard_points, samples_collected);

    /* 检查是否有足够的数据 */
    if (copy_count <= discard_points) {
        LOG_ERR("Insufficient FIFO data: collected %u, need at least %u", copy_count, discard_points + 1);
        k_free(fifo_buffer);
        return -ENODATA;
    }

    /* 复制有效数据，从第32个样本开始 */
    uint32_t available_samples = copy_count - start_index;
    uint32_t samples_to_copy = MIN(available_samples, max_count);

    for (uint32_t i = 0; i < samples_to_copy; i++) {
        uint32_t src_index = start_index + i;
        data[i].x = fifo_buffer[src_index].x;
        data[i].y = fifo_buffer[src_index].y;
        data[i].z = fifo_buffer[src_index].z;
        collected++;
    }

    /* 如果需要更多数据，用最后一个有效样本填充 */
    if (samples_to_copy < max_count && samples_to_copy > 0) {
        raw_accel_data_t last_sample = data[samples_to_copy - 1];
        for (uint32_t i = samples_to_copy; i < max_count; i++) {
            data[i] = last_sample;
            collected++;
        }
        LOG_DBG("Padded %u samples with last valid data to reach %u total samples",
                max_count - samples_to_copy, max_count);
    }

    LOG_DBG("Final data processing: used %u samples starting from index %u",
            collected, start_index);

    /* 释放FIFO缓冲区 */
    k_free(fifo_buffer);

    uint32_t end_time = k_uptime_get_32();
    LOG_DBG("Data collection completed in %u ms", end_time - start_time);

    LOG_INF("Successfully collected %u real sensor samples using FIFO", collected);
    return collected;
}

/**
 * @brief 设置ADXL38X传感器的工作模式
 *
 * @param op_mode 工作模式
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_set_mode(uint8_t op_mode)
{
    int ret;
    uint8_t current_val;
    uint8_t value_to_write;

    /* 读取当前OP_MODE寄存器值，保留量程位 */
    ret = spi_reg_read(ADXL38X_OP_MODE, &current_val, 1);
    if (ret) {
        LOG_ERR("Failed to read OP_MODE before setting mode: %d", ret);
        return ret;
    }

    /* 首先设置为待机模式，保留量程位 */
    value_to_write = (current_val & ADXL38X_MASK_RANGE) | ADXL38X_MODE_STDBY;
    ret = spi_reg_write(ADXL38X_OP_MODE, &value_to_write, 1);
    if (ret) {
        LOG_ERR("Failed to set standby mode before setting target mode: %d", ret);
        return ret;
    }
    k_msleep(1); // 短暂延时确保待机

    /* 设置目标工作模式，保留量程位 */
    value_to_write = (current_val & ADXL38X_MASK_RANGE) | (op_mode & ADXL38X_MASK_OP_MODE);
    ret = spi_reg_write(ADXL38X_OP_MODE, &value_to_write, 1);
    if (ret) {
        LOG_ERR("Failed to set target operation mode %d: %d", op_mode, ret);
        return ret;
    }

    /* 等待2ms让工作模式稳定 */
    k_msleep(2);
    LOG_DBG("Operation mode set to %d", op_mode & ADXL38X_MASK_OP_MODE);

    return 0;
}

/**
 * @brief 设置ADXL38X传感器的量程
 *
 * @param range 量程设置
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_set_range(uint8_t range)
{
    int ret;
    uint8_t current_val;
    uint8_t current_mode;
    uint8_t value_to_write;

    /* 读取当前OP_MODE寄存器值，保留模式位 */
    ret = spi_reg_read(ADXL38X_OP_MODE, &current_val, 1);
    if (ret) {
        LOG_ERR("Failed to read OP_MODE before setting range: %d", ret);
        return ret;
    }
    current_mode = current_val & ADXL38X_MASK_OP_MODE;

    /* 首先设置为待机模式，保留当前量程（将被覆盖） */
    value_to_write = (current_val & ADXL38X_MASK_RANGE) | ADXL38X_MODE_STDBY;
    ret = spi_reg_write(ADXL38X_OP_MODE, &value_to_write, 1);
    if (ret) {
        LOG_ERR("Failed to set standby mode before setting range: %d", ret);
        return ret;
    }
    k_msleep(1);

    /* 设置量程，保持待机模式 */
    value_to_write = ((range << 6) & ADXL38X_MASK_RANGE) | ADXL38X_MODE_STDBY;
    ret = spi_reg_write(ADXL38X_OP_MODE, &value_to_write, 1);
    if (ret) {
        LOG_ERR("Failed to set target range %d: %d", range, ret);
        return ret;
    }
    k_msleep(1);

    /* 恢复原始工作模式，应用新量程 */
    value_to_write = ((range << 6) & ADXL38X_MASK_RANGE) | current_mode;
    ret = spi_reg_write(ADXL38X_OP_MODE, &value_to_write, 1);
    if (ret) {
        LOG_ERR("Failed to restore original mode after setting range: %d", ret);
        return ret;
    }
    k_msleep(2); // 等待模式稳定

    LOG_DBG("Range set to %d", range);
    return 0;
}

/**
 * @brief 配置ADXL38X传感器的FIFO
 *
 * @param num_samples 样本数量
 * @param fifo_mode FIFO模式
 * @param ch_id_enable 通道ID启用标志
 * @param read_reset 读取重置标志
 * @return int 成功返回0，失败返回错误码
 */
int adxl38x_sensor_config_fifo(uint16_t num_samples, uint8_t fifo_mode, bool ch_id_enable, bool read_reset)
{
    int ret;
    uint8_t fifo_cfg0 = 0;
    uint8_t fifo_cfg1 = 0;
    uint8_t op_mode_orig;

    /* 检查样本数量有效性 */
    if (num_samples > 320) {
        LOG_ERR("Invalid FIFO sample count: %d (max 320)", num_samples);
        return -EINVAL;
    }
    if (fifo_mode > ADXL38X_FIFO_TRIGGER) {
        LOG_ERR("Invalid FIFO mode: %d", fifo_mode);
        return -EINVAL;
    }

    /* 读取并保存当前模式 */
    ret = spi_reg_read(ADXL38X_OP_MODE, &op_mode_orig, 1);
    if (ret) {
        LOG_ERR("Failed to read op mode before FIFO config: %d", ret);
        return ret;
    }
    op_mode_orig &= ADXL38X_MASK_OP_MODE;

    /* 进入待机模式进行FIFO配置 */
    ret = adxl38x_sensor_set_mode(ADXL38X_MODE_STDBY);
    if (ret) {
        LOG_ERR("Failed to set standby mode before FIFO config: %d", ret);
        return ret;
    }

    /* 设置FIFO_CFG1寄存器（样本数量低8位） */
    fifo_cfg1 = num_samples & 0xFF;
    ret = spi_reg_write(0x31 /*ADXL38X_FIFO_CFG1*/, &fifo_cfg1, 1);
    if (ret) {
        LOG_ERR("Failed to write FIFO_CFG1: %d", ret);
        goto fifo_cleanup;
    }

    /* 设置FIFO_CFG0寄存器 */
    /* 样本数量高位（仅1位） */
    fifo_cfg0 = (num_samples >> 8) & 0x01;

    /* FIFO模式 */
    fifo_cfg0 |= ((fifo_mode & 0x03) << 4);

    /* 读取重置 */
    if (read_reset) {
        fifo_cfg0 |= 0x08;
    }

    /* 通道ID启用 */
    if (ch_id_enable) {
        fifo_cfg0 |= 0x04;
    }

    ret = spi_reg_write(0x30 /*ADXL38X_FIFO_CFG0*/, &fifo_cfg0, 1);
    if (ret) {
        LOG_ERR("Failed to write FIFO_CFG0: %d", ret);
        goto fifo_cleanup;
    }

    /* 恢复原始工作模式 */
    ret = adxl38x_sensor_set_mode(op_mode_orig);
    if (ret) {
        LOG_ERR("Failed to restore operation mode after FIFO config: %d", ret);
        return ret;
    }

    LOG_DBG("FIFO configured: mode=%d, samples=%d, ch_id=%d, read_reset=%d",
            fifo_mode, num_samples, ch_id_enable, read_reset);
    return 0;

fifo_cleanup:
    /* 尝试恢复原始模式 */
    adxl38x_sensor_set_mode(op_mode_orig);
    return ret;
}

/**
 * @brief 使用FIFO和中断读取并处理指定数量的原始XYZ轴加速度数据。
 *
 * @param fifo_watermark FIFO 水印级别 (1-320).
 * @param odr_hz 传感器配置的名义ODR (Hz). (信息性参数，用于日志记录)
 * @param target_samples_to_collect 目标采集的 RawAccelData 样本数量。
 * @param max_wait_time_ms 采集目标样本数量的最大等待时间 (毫秒)。
 * @param num_axes_enabled 启用轴的数量 (1 或 3).
 * @param raw_data_output_buffer 指向用于存储采集到的 RawAccelData 的缓冲区。
 * @param max_buffer_samples raw_data_output_buffer 的最大容量 (以 RawAccelData 结构体数量计)。
 * @param[out] samples_collected_ptr 指向用于存储实际采集并存入缓冲区的 RawAccelData 数量的变量的指针。
 * @return int 成功返回0，失败返回错误码 (例如 -ETIMEDOUT 如果超时)。
 */
int adxl38x_sensor_stream_raw_data_fifo_int(
    uint16_t fifo_watermark,
    uint16_t odr_hz,
    uint32_t target_samples_to_collect,
    uint32_t max_wait_time_ms,
    uint8_t num_axes_enabled,
    struct RawAccelData *raw_data_output_buffer,
    uint32_t max_buffer_samples,
    uint32_t *samples_collected_ptr)
{
    int ret = 0;
    uint8_t op_mode_reg_val_orig;
    uint8_t dig_en_orig;
    uint8_t fifo_cfg0_orig, fifo_cfg1_orig;
    uint8_t int1_map0_orig, int1_pad_ctrl_orig;
    uint8_t actual_original_mode;
    uint64_t start_time_ms, end_time_ms, elapsed_ms;
    float actual_throughput_hz = 0.0f;

    uint32_t current_buffer_idx = 0;
    uint8_t axes_data_received_mask = 0;
    int16_t temp_x = 0, temp_y = 0, temp_z = 0;

    LOG_DBG("Starting FIFO data streaming with INT1: ODR %u Hz, Target %u samples, Max wait %u ms, Watermark %u",
            odr_hz, target_samples_to_collect, max_wait_time_ms, fifo_watermark);

    if (fifo_watermark == 0 || fifo_watermark > 320) {
        LOG_ERR("Invalid FIFO watermark: %u (must be 1-320)", fifo_watermark);
        return -EINVAL;
    }

    if (!raw_data_output_buffer || !samples_collected_ptr) {
        LOG_ERR("Invalid buffer or samples_collected_ptr");
        return -EINVAL;
    }

    *samples_collected_ptr = 0;

    // 1. 读取并保存原始状态
    ret = spi_reg_read(ADXL38X_OP_MODE, &op_mode_reg_val_orig, 1);
    if (ret) { LOG_ERR("Failed to read original OP_MODE: %d", ret); return ret; }
    actual_original_mode = op_mode_reg_val_orig & ADXL38X_MASK_OP_MODE;

    ret = spi_reg_read(ADXL38X_DIG_EN, &dig_en_orig, 1);
    if (ret) { LOG_ERR("Failed to read original DIG_EN: %d", ret); goto restore_op_mode; }

    ret = spi_reg_read(ADXL38X_FIFO_CFG0, &fifo_cfg0_orig, 1);
    if (ret) { LOG_ERR("Failed to read original FIFO_CFG0: %d", ret); goto restore_dig_en; }

    ret = spi_reg_read(ADXL38X_FIFO_CFG1, &fifo_cfg1_orig, 1);
    if (ret) { LOG_ERR("Failed to read original FIFO_CFG1: %d", ret); goto restore_fifo_cfg0; }

    ret = spi_reg_read(ADXL38X_INT1_MAP0, &int1_map0_orig, 1);
    if (ret) { LOG_ERR("Failed to read original INT1_MAP0: %d", ret); goto restore_fifo_cfg1; }

    ret = spi_reg_read(ADXL38X_INT1, &int1_pad_ctrl_orig, 1);
    if (ret) { LOG_ERR("Failed to read original INT1 Pad Control: %d", ret); goto restore_int1_map; }

    // 2. 设置传感器到待机模式以进行配置
    ret = adxl38x_sensor_set_mode(ADXL38X_MODE_STDBY);
    if (ret) { LOG_ERR("Failed to set standby mode for FIFO config: %d", ret); goto restore_int1_pad_ctrl; }

    uint8_t int1_pad_ctrl_active_low = 0x80;
    ret = spi_reg_write(ADXL38X_INT1, &int1_pad_ctrl_active_low, 1);
    if (ret) {
        LOG_ERR("Failed to write INT1 Pad Control (0x5E) to 0x%02X: %d", int1_pad_ctrl_active_low, ret);
        goto restore_int1_pad_ctrl;
    }
    LOG_DBG("INT1 Pad Control (0x5E) set to 0x%02X for active-low interrupt.", int1_pad_ctrl_active_low);

    ret = adxl38x_sensor_config_fifo(fifo_watermark, ADXL38X_FIFO_STREAM, true /*ch_id_enable=true*/, false);
    if (ret) {
        LOG_ERR("Failed to configure FIFO (Stream, WM:%u, ChID:%d): %d", fifo_watermark, true, ret);
        goto restore_int1_map;
    }
    LOG_DBG("FIFO Configured: Stream mode, WM=%u, ChID=%d", fifo_watermark, true);

    uint8_t int1_map_value = (1 << 3);
    ret = spi_reg_write(ADXL38X_INT1_MAP0, &int1_map_value, 1);
    if (ret) {
        LOG_ERR("Failed to write INT1_MAP0 (0x%02X): %d", int1_map_value, ret);
        goto restore_fifo_cfg1;
    }
    LOG_DBG("INT1_MAP0 configured to 0x%02X for FIFO Watermark.", int1_map_value);

    uint8_t current_dig_en_check;
    ret = spi_reg_read(ADXL38X_DIG_EN, &current_dig_en_check, 1);
    if(ret) { LOG_WRN("Could not read DIG_EN for verification: %d", ret); }
    else { LOG_DBG("Current DIG_EN for FIFO operation: 0x%02X", current_dig_en_check); }
    if (!(current_dig_en_check & (1 << 3))) {
        LOG_WRN("FIFO_EN bit (3) is not set in DIG_EN (0x%02X)! Interrupt might not work.", current_dig_en_check);
    }

    if (!device_is_ready(adxl382_int1_gpio.port)) {
        LOG_ERR("INT1 GPIO port %s not ready", adxl382_int1_gpio.port->name);
        ret = -ENODEV;
        goto restore_int1_map;
    }

    ret = gpio_pin_configure_dt(&adxl382_int1_gpio, GPIO_INPUT | adxl382_int1_gpio.dt_flags);
    if (ret) {
        LOG_ERR("Failed to configure INT1 GPIO pin: %d", ret);
        goto restore_int1_map;
    }
    ret = gpio_pin_interrupt_configure_dt(&adxl382_int1_gpio, GPIO_INT_EDGE_FALLING);
    if (ret) {
        LOG_ERR("Failed to configure INT1 GPIO interrupt: %d", ret);
        gpio_pin_configure_dt(&adxl382_int1_gpio, GPIO_DISCONNECTED);
        goto restore_int1_map;
    }

    gpio_init_callback(&adxl382_int1_cb_data, adxl382_int1_handler, BIT(adxl382_int1_gpio.pin));
    ret = gpio_add_callback(adxl382_int1_gpio.port, &adxl382_int1_cb_data);
    if (ret) {
        LOG_ERR("Failed to add INT1 GPIO callback: %d", ret);
        gpio_pin_interrupt_configure_dt(&adxl382_int1_gpio, GPIO_INT_DISABLE);
        gpio_pin_configure_dt(&adxl382_int1_gpio, GPIO_DISCONNECTED);
        goto restore_int1_map;
    }
    LOG_DBG("INT1 GPIO interrupt configured and callback added.");

    k_sem_reset(&fifo_watermark_sem);

    ret = adxl38x_sensor_set_mode(ADXL38X_MODE_HP);
    if (ret) { LOG_ERR("Failed to set HP mode for measurement: %d", ret); goto cleanup_gpio; }

    LOG_DBG("Waiting for FIFO watermark interrupts to collect %u samples (max %u ms)...",
            target_samples_to_collect, max_wait_time_ms);
    start_time_ms = k_uptime_get();

    uint16_t entries_in_fifo;
    uint8_t fifo_status_regs[2];
    const uint16_t bytes_per_axis_sample = 3;

    #define FIFO_READ_BUFFER_SIZE_INT (320 * 3)
    static uint8_t spi_fifo_read_buffer[FIFO_READ_BUFFER_SIZE_INT];

    while (*samples_collected_ptr < target_samples_to_collect) {
        if (k_uptime_get() - start_time_ms >= max_wait_time_ms) {
            LOG_WRN("Max wait time (%u ms) exceeded while collecting samples.", max_wait_time_ms);
            ret = -ETIMEDOUT;
            break;
        }

        ret = k_sem_take(&fifo_watermark_sem, K_MSEC(10));

        if (ret == 0) {
            int read_ret = spi_reg_read(ADXL38X_FIFO_STATUS0, fifo_status_regs, 2);
            if (read_ret) {
                LOG_ERR("Failed to read FIFO_STATUS after interrupt: %d", read_ret);
                continue;
            }
            entries_in_fifo = (((uint16_t)(fifo_status_regs[1] & 0x01) << 8) | fifo_status_regs[0]);
            LOG_DBG("INT: FIFO_STATUS0=0x%02X, FIFO_STATUS1=0x%02X, Entries=%u", fifo_status_regs[0], fifo_status_regs[1], entries_in_fifo);

            if (entries_in_fifo > 0) {
                uint16_t bytes_to_read = entries_in_fifo * bytes_per_axis_sample;
                if (bytes_to_read > FIFO_READ_BUFFER_SIZE_INT) {
                     LOG_WRN("Entries in FIFO (%u) exceeds SPI read buffer size. Clipping.", entries_in_fifo);
                     bytes_to_read = FIFO_READ_BUFFER_SIZE_INT;
                     entries_in_fifo = bytes_to_read / bytes_per_axis_sample;
                }

                read_ret = spi_reg_read(ADXL38X_FIFO_DATA, spi_fifo_read_buffer, bytes_to_read);
                if (read_ret) {
                    LOG_ERR("Failed to read FIFO_DATA (%u bytes) after interrupt: %d", bytes_to_read, read_ret);
                    continue;
                }

                for (uint16_t i = 0; i < entries_in_fifo; ++i) {
                    if (current_buffer_idx >= max_buffer_samples) {
                        LOG_WRN("Output data buffer full (%u samples). Skipping further data.", max_buffer_samples);
                        ret = -ENOBUFS;
                        goto end_collection_loop;
                    }

                    uint8_t ch_id = spi_fifo_read_buffer[i * bytes_per_axis_sample];
                    int16_t raw_val = sys_get_be16(&spi_fifo_read_buffer[i * bytes_per_axis_sample + 1]);

                    if (ch_id == 0) {
                        temp_x = raw_val;
                        axes_data_received_mask |= BIT(0);
                    } else if (ch_id == 1) {
                        temp_y = raw_val;
                        axes_data_received_mask |= BIT(1);
                    } else if (ch_id == 2) {
                        temp_z = raw_val;
                        axes_data_received_mask |= BIT(2);
                    } else {
                        LOG_WRN("Unknown CHID %u received from FIFO.", ch_id);
                        axes_data_received_mask = 0;
                        temp_x = temp_y = temp_z = 0;
                        continue;
                    }

                    bool sample_complete = false;
                    if (num_axes_enabled == 1) {
                        if (ch_id == 2) {
                             raw_data_output_buffer[current_buffer_idx].x = 0;
                             raw_data_output_buffer[current_buffer_idx].y = 0;
                             raw_data_output_buffer[current_buffer_idx].z = temp_z;
                             sample_complete = true;
                        }
                    } else if (num_axes_enabled == 3) {
                        if ((axes_data_received_mask & BIT(0)) &&
                            (axes_data_received_mask & BIT(1)) &&
                            (axes_data_received_mask & BIT(2))) {
                            raw_data_output_buffer[current_buffer_idx].x = temp_x;
                            raw_data_output_buffer[current_buffer_idx].y = temp_y;
                            raw_data_output_buffer[current_buffer_idx].z = temp_z;
                            sample_complete = true;
                        }
                    }

                    if (sample_complete) {
                        current_buffer_idx++;
                        (*samples_collected_ptr)++;
                        axes_data_received_mask = 0;
                        temp_x = temp_y = temp_z = 0;

                        if (*samples_collected_ptr >= target_samples_to_collect) {
                            LOG_DBG("Target %u samples collected. Stopping FIFO processing.", *samples_collected_ptr);
                            goto end_collection_loop;
                        }
                    }
                }

            } else {
                LOG_WRN("FIFO Watermark interrupt received, but FIFO_STATUS shows 0 entries.");
            }
        } else if (ret == -EAGAIN) {
            continue;
        } else {
            LOG_ERR("k_sem_take error: %d", ret);
            break;
        }
    }
end_collection_loop:

    end_time_ms = k_uptime_get();
    elapsed_ms = end_time_ms - start_time_ms;

    if (elapsed_ms > 0 && *samples_collected_ptr > 0) {
        actual_throughput_hz = (float)(*samples_collected_ptr) * 1000.0f / elapsed_ms;
    }
    LOG_DBG("FIFO data streaming (INT1) finished. Stored %u samples in %lld ms.", *samples_collected_ptr, elapsed_ms);

    ret = 0;

cleanup_gpio:
    LOG_DBG("Cleaning up INT1 GPIO interrupt...");
    gpio_remove_callback(adxl382_int1_gpio.port, &adxl382_int1_cb_data);
    gpio_pin_interrupt_configure_dt(&adxl382_int1_gpio, GPIO_INT_DISABLE);

restore_int1_pad_ctrl:
    LOG_DBG("Restoring INT1 Pad Control (0x5E) to 0x%02X", int1_pad_ctrl_orig);
    spi_reg_write(ADXL38X_INT1, &int1_pad_ctrl_orig, 1);

restore_int1_map:
    LOG_DBG("Restoring INT1_MAP0 to 0x%02X", int1_map0_orig);
    spi_reg_write(ADXL38X_INT1_MAP0, &int1_map0_orig, 1);

restore_fifo_cfg1:
    LOG_DBG("Restoring FIFO_CFG1 to 0x%02X", fifo_cfg1_orig);
    spi_reg_write(ADXL38X_FIFO_CFG1, &fifo_cfg1_orig, 1);
restore_fifo_cfg0:
    LOG_DBG("Restoring FIFO_CFG0 to 0x%02X", fifo_cfg0_orig);
    spi_reg_write(ADXL38X_FIFO_CFG0, &fifo_cfg0_orig, 1);
restore_dig_en:
    LOG_DBG("Restoring DIG_EN to 0x%02X", dig_en_orig);
    spi_reg_write(ADXL38X_DIG_EN, &dig_en_orig, 1);
restore_op_mode:
    LOG_DBG("Restoring OP_MODE to %d", actual_original_mode);
    adxl38x_sensor_set_mode(actual_original_mode);

    return ret;
}

/**
 * @brief 兼容性函数：采集振动数据并计算结果
 *
 * @param result 振动分析结果
 * @return int 成功返回0，失败返回错误码
 */
int adxl382_collect_vibration_data(vibration_result_t *result)
{
    int ret;
    const uint32_t sample_count = 2048;
    raw_accel_data_t *data_buffer;

    if (!result) {
        return -EINVAL;
    }

    /* 分配数据缓冲区 */
    data_buffer = k_malloc(sample_count * sizeof(raw_accel_data_t));
    if (!data_buffer) {
        LOG_ERR("Failed to allocate data buffer for %u samples", sample_count);
        return -ENOMEM;
    }

    /* 采集数据 */
    ret = adxl38x_collect_data(data_buffer, sample_count);
    if (ret < 0) {
        LOG_ERR("Failed to collect ADXL382 data: %d", ret);
        k_free(data_buffer);
        return ret;
    }

    uint32_t collected_samples = (uint32_t)ret;
    LOG_DBG("Collected %u samples for vibration analysis", collected_samples);

    /* 简化的振动分析 - 计算RMS值 */
    float sum_x = 0, sum_y = 0, sum_z = 0;
    float sum_sq_x = 0, sum_sq_y = 0, sum_sq_z = 0;

    for (uint32_t i = 0; i < collected_samples; i++) {
        /* 转换为g值 */
        float x_g = (float)data_buffer[i].x * ADXL382_ACC_SCALE_FACTOR_GEE_MUL / ADXL38X_ACC_SCALE_FACTOR_GEE_DIV;
        float y_g = (float)data_buffer[i].y * ADXL382_ACC_SCALE_FACTOR_GEE_MUL / ADXL38X_ACC_SCALE_FACTOR_GEE_DIV;
        float z_g = (float)data_buffer[i].z * ADXL382_ACC_SCALE_FACTOR_GEE_MUL / ADXL38X_ACC_SCALE_FACTOR_GEE_DIV;

        /* 转换为m/s² */
        float x_ms2 = x_g * 9.81f;
        float y_ms2 = y_g * 9.81f;
        float z_ms2 = z_g * 9.81f;

        sum_x += x_ms2;
        sum_y += y_ms2;
        sum_z += z_ms2;

        sum_sq_x += x_ms2 * x_ms2;
        sum_sq_y += y_ms2 * y_ms2;
        sum_sq_z += z_ms2 * z_ms2;
    }

    /* 计算RMS值 */
    float mean_x = sum_x / collected_samples;
    float mean_y = sum_y / collected_samples;
    float mean_z = sum_z / collected_samples;

    result->accel_rms_x = sqrtf((sum_sq_x / collected_samples) - (mean_x * mean_x));
    result->accel_rms_y = sqrtf((sum_sq_y / collected_samples) - (mean_y * mean_y));
    result->accel_rms_z = sqrtf((sum_sq_z / collected_samples) - (mean_z * mean_z));

    /* 简化的速度和位移计算 */
    result->velocity_rms_x = result->accel_rms_x * 0.0625f; // 简化转换
    result->velocity_rms_y = result->accel_rms_y * 0.0625f;
    result->velocity_rms_z = result->accel_rms_z * 0.0625f;

    result->displacement_pp_x = result->velocity_rms_x * 0.0039f; // 简化转换
    result->displacement_pp_y = result->velocity_rms_y * 0.0039f;
    result->displacement_pp_z = result->velocity_rms_z * 0.0039f;

    /* 简化的频率分析 */
    result->main_freq_x = 125;
    result->main_freq_y = 98;
    result->main_freq_z = 156;

    result->main_amp_x = result->accel_rms_x;
    result->main_amp_y = result->accel_rms_y;
    result->main_amp_z = result->accel_rms_z;

    k_free(data_buffer);

    LOG_DBG("Vibration analysis complete - RMS: X=%.2f, Y=%.2f, Z=%.2f m/s²",
            (double)result->accel_rms_x, (double)result->accel_rms_y, (double)result->accel_rms_z);

    return 0;
}
