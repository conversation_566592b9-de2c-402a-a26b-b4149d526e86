add_custom_target(${DEVICETREE_TARGET})

set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,entropy" "/soc/crypto@50844000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,flash-controller" "/soc/peripheral@50000000/flash-controller@39000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,console" "/soc/peripheral@50000000/uart@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,shell-uart" "/soc/peripheral@50000000/uart@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,uart-mcumgr" "/soc/peripheral@50000000/uart@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,bt-mon-uart" "/soc/peripheral@50000000/uart@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,bt-c2h-uart" "/soc/peripheral@50000000/uart@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,bt-hci" "/ipc/ipc0/bt_hci_ipc0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|nordic,802154-spinel-ipc" "/ipc/ipc0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,ieee802154" "/soc/peripheral@50000000/ieee802154")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,ipc_shm" "/reserved-memory/memory@20070000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,sram" "/reserved-memory/image@20000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,flash" "/soc/peripheral@50000000/flash-controller@39000/flash@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,code-partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,sram-secure-partition" "/reserved-memory/image_s@20000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|zephyr,sram-non-secure-partition" "/reserved-memory/image_ns@20040000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|nordic,pm-ext-flash" "/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_CHOSEN|ncs,zigbee-timer" "/soc/peripheral@50000000/timer@11000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|watchdog0" "/soc/peripheral@50000000/watchdog@18000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|led0" "/leds/led_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|bootloader-led0" "/leds/led_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|mcuboot-led0" "/leds/led_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|led1" "/leds/led_1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|led2" "/leds/led_2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|led3" "/leds/led_3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|sw0" "/buttons/button_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|mcuboot-button0" "/buttons/button_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|sw1" "/buttons/button_1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|sw2" "/buttons/button_2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|sw3" "/buttons/button_3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|pwm-led0" "/pwmleds/pwm_led_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|mic-power" "/mic_power")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_ALIAS|adxl382-pwr-ctrl" "/adxl382-pwr-ctrl")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/|compatible" "nordic,nrf5340-dk-nrf5340-cpuapp;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/chosen" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/chosen|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/chosen|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/chosen|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/aliases" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/aliases|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/aliases|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/aliases|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc|compatible" "nordic,nrf5340-cpuapp-qkaa;nordic,nrf5340-cpuapp;nordic,nrf53;simple-bus;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc|ranges" "None")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/interrupt-controller@e000e100" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|nvic" "/soc/interrupt-controller@e000e100")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|interrupt-controller" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|compatible" "arm,v8m-nvic;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|reg" "3758153984;3072;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|arm,num-irq-priority-bits" "3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/interrupt-controller@e000e100|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/interrupt-controller@e000e100|ADDR" "0xe000e100;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/interrupt-controller@e000e100|SIZE" "0xc00;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/timer@e000e010" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|systick" "/soc/timer@e000e010")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/timer@e000e010|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/timer@e000e010|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/timer@e000e010|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/timer@e000e010|compatible" "arm,armv8m-systick;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/timer@e000e010|reg" "3758153744;16;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/timer@e000e010|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/timer@e000e010|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/timer@e000e010|ADDR" "0xe000e010;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/timer@e000e010|SIZE" "0x10;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/ficr@ff0000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|ficr" "/soc/ficr@ff0000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/ficr@ff0000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/ficr@ff0000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/ficr@ff0000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/ficr@ff0000|compatible" "nordic,nrf-ficr;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/ficr@ff0000|reg" "16711680;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/ficr@ff0000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/ficr@ff0000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/ficr@ff0000|ADDR" "0xff0000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/ficr@ff0000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/uicr@ff8000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uicr" "/soc/uicr@ff8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|compatible" "nordic,nrf-uicr;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|reg" "16744448;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|nfct-pins-as-gpios" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/uicr@ff8000|gpio-as-nreset" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/uicr@ff8000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/uicr@ff8000|ADDR" "0xff8000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/uicr@ff8000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/memory@20000000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sram0" "/soc/memory@20000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/memory@20000000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/memory@20000000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/memory@20000000|compatible" "mmio-sram;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/memory@20000000|reg" "536870912;524288;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/memory@20000000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/memory@20000000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/memory@20000000|ADDR" "0x20000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/memory@20000000|SIZE" "0x80000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000|ranges" "None")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/dcnf@0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|dcnf" "/soc/peripheral@50000000/dcnf@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dcnf@0|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dcnf@0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dcnf@0|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dcnf@0|compatible" "nordic,nrf-dcnf;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dcnf@0|reg" "0;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dcnf@0|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/dcnf@0|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/dcnf@0|ADDR" "0x50000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/dcnf@0|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/oscillator@4000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|oscillators" "/soc/peripheral@50000000/oscillator@4000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/oscillator@4000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/oscillator@4000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/oscillator@4000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/oscillator@4000|compatible" "nordic,nrf-oscillators;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/oscillator@4000|reg" "16384;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/oscillator@4000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/oscillator@4000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/oscillator@4000|ADDR" "0x50004000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/oscillator@4000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/regulator@4000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|regulators" "/soc/peripheral@50000000/regulator@4000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000|compatible" "nordic,nrf53x-regulators;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000|reg" "16384;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000|ADDR" "0x50004000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/regulator@4000/regulator@4704" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|vregmain" "/soc/peripheral@50000000/regulator@4000/regulator@4704")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|compatible" "nordic,nrf5x-regulator;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|reg" "18180;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|regulator-name" "VREGMAIN")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4704|regulator-initial-mode" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4704|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4704|ADDR" "0x4704;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4704|SIZE" "0x1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/regulator@4000/regulator@4904" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|vregradio" "/soc/peripheral@50000000/regulator@4000/regulator@4904")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|compatible" "nordic,nrf5x-regulator;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|reg" "18692;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|regulator-name" "VREGRADIO")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4904|regulator-initial-mode" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4904|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4904|ADDR" "0x4904;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4904|SIZE" "0x1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/regulator@4000/regulator@4b00" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|vregh" "/soc/peripheral@50000000/regulator@4000/regulator@4b00")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|compatible" "nordic,nrf53x-regulator-hv;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|reg" "19200;68;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@4000/regulator@4b00|regulator-name" "VREGH")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4b00|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4b00|ADDR" "0x4b00;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@4000/regulator@4b00|SIZE" "0x44;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/clock@5000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|clock" "/soc/peripheral@50000000/clock@5000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|compatible" "nordic,nrf-clock;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|reg" "20480;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|interrupts" "5;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/clock@5000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/clock@5000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/clock@5000|ADDR" "0x50005000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/clock@5000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/power@5000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|power" "/soc/peripheral@50000000/power@5000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|compatible" "nordic,nrf-power;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|reg" "20480;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|interrupts" "5;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000|ADDR" "0x50005000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/power@5000/gpregret1@51c" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpregret1" "/soc/peripheral@50000000/power@5000/gpregret1@51c")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret1@51c|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret1@51c|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret1@51c|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret1@51c|compatible" "nordic,nrf-gpregret;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret1@51c|reg" "1308;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret1@51c|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000/gpregret1@51c|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000/gpregret1@51c|ADDR" "0x5000551c;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000/gpregret1@51c|SIZE" "0x1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/power@5000/gpregret2@520" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpregret2" "/soc/peripheral@50000000/power@5000/gpregret2@520")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret2@520|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret2@520|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret2@520|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret2@520|compatible" "nordic,nrf-gpregret;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret2@520|reg" "1312;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/power@5000/gpregret2@520|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000/gpregret2@520|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000/gpregret2@520|ADDR" "0x50005520;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/power@5000/gpregret2@520|SIZE" "0x1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/reset-controller@5000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|reset" "/soc/peripheral@50000000/reset-controller@5000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/reset-controller@5000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/reset-controller@5000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/reset-controller@5000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/reset-controller@5000|compatible" "nordic,nrf-reset;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/reset-controller@5000|reg" "20480;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/reset-controller@5000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/reset-controller@5000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/reset-controller@5000|ADDR" "0x50005000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/reset-controller@5000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/ctrlap@6000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|ctrlap" "/soc/peripheral@50000000/ctrlap@6000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ctrlap@6000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ctrlap@6000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ctrlap@6000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ctrlap@6000|compatible" "nordic,nrf-ctrlapperi;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ctrlap@6000|reg" "24576;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ctrlap@6000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/ctrlap@6000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/ctrlap@6000|ADDR" "0x50006000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/ctrlap@6000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/i2c@8000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2c0" "/soc/peripheral@50000000/i2c@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|zephyr,pm-device-runtime-auto" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|compatible" "nordic,nrf-twim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|reg" "32768;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|interrupts" "8;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|sq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|cq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|zephyr,concat-buf-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@8000|zephyr,flash-buf-max-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@8000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@8000|ADDR" "0x50008000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@8000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/spi@8000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi0" "/soc/peripheral@50000000/spi@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|compatible" "nordic,nrf-spim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|reg" "32768;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|interrupts" "8;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|overrun-character" "255")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|max-frequency" "8000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|anomaly-58-workaround" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@8000|rx-delay-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@8000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@8000|ADDR" "0x50008000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@8000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/uart@8000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart0" "/soc/peripheral@50000000/uart@8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|compatible" "nordic,nrf-uarte;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|reg" "32768;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|interrupts" "8;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|current-speed" "115200")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|hw-flow-control" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|pinctrl-names" "default;sleep;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|disable-rx" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|endtx-stoptx-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@8000|frame-timeout-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@8000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@8000|ADDR" "0x50008000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@8000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/i2c@9000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2c1" "/soc/peripheral@50000000/i2c@9000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|arduino_i2c" "/soc/peripheral@50000000/i2c@9000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|zephyr,pm-device-runtime-auto" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|compatible" "nordic,nrf-twim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|reg" "36864;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|interrupts" "9;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|sq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|cq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|pinctrl-names" "default;sleep;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|zephyr,concat-buf-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@9000|zephyr,flash-buf-max-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@9000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@9000|ADDR" "0x50009000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@9000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/spi@9000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi1" "/soc/peripheral@50000000/spi@9000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|compatible" "nordic,nrf-spim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|reg" "36864;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|interrupts" "9;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|overrun-character" "255")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|max-frequency" "8000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|anomaly-58-workaround" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@9000|rx-delay-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@9000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@9000|ADDR" "0x50009000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@9000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/uart@9000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart1" "/soc/peripheral@50000000/uart@9000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|arduino_serial" "/soc/peripheral@50000000/uart@9000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|compatible" "nordic,nrf-uarte;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|reg" "36864;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|interrupts" "9;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|current-speed" "115200")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|hw-flow-control" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|pinctrl-names" "default;sleep;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|disable-rx" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|endtx-stoptx-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@9000|frame-timeout-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@9000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@9000|ADDR" "0x50009000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@9000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/spi@a000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi4" "/soc/peripheral@50000000/spi@a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|arduino_spi" "/soc/peripheral@50000000/spi@a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|compatible" "nordic,nrf-spim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|reg" "40960;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|interrupts" "10;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|overrun-character" "255")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|pinctrl-names" "default;sleep;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|max-frequency" "32000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|anomaly-58-workaround" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|rx-delay-supported" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@a000|rx-delay" "2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@a000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@a000|ADDR" "0x5000a000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@a000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/i2c@b000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2c2" "/soc/peripheral@50000000/i2c@b000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|zephyr,pm-device-runtime-auto" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|compatible" "nordic,nrf-twim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|reg" "45056;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|interrupts" "11;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|sq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|cq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|zephyr,concat-buf-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@b000|zephyr,flash-buf-max-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@b000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@b000|ADDR" "0x5000b000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@b000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/spi@b000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi2" "/soc/peripheral@50000000/spi@b000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|compatible" "nordic,nrf-spim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|reg" "45056;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|interrupts" "11;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|overrun-character" "255")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|max-frequency" "8000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|anomaly-58-workaround" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@b000|rx-delay-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@b000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@b000|ADDR" "0x5000b000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@b000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/uart@b000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart2" "/soc/peripheral@50000000/uart@b000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|compatible" "nordic,nrf-uarte;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|reg" "45056;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|interrupts" "11;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|hw-flow-control" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|disable-rx" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|endtx-stoptx-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@b000|frame-timeout-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@b000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@b000|ADDR" "0x5000b000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@b000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/i2c@c000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2c3" "/soc/peripheral@50000000/i2c@c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|zephyr,pm-device-runtime-auto" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|compatible" "nordic,nrf-twim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|reg" "49152;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|interrupts" "12;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|sq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|cq-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|zephyr,concat-buf-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2c@c000|zephyr,flash-buf-max-size" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@c000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@c000|ADDR" "0x5000c000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2c@c000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/spi@c000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi3" "/soc/peripheral@50000000/spi@c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|compatible" "nordic,nrf-spim;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|reg" "49152;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|interrupts" "12;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|overrun-character" "255")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|max-frequency" "8000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|easydma-maxcnt-bits" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|anomaly-58-workaround" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/spi@c000|rx-delay-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@c000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@c000|ADDR" "0x5000c000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/spi@c000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/uart@c000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart3" "/soc/peripheral@50000000/uart@c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|compatible" "nordic,nrf-uarte;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|reg" "49152;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|interrupts" "12;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|hw-flow-control" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|disable-rx" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|endtx-stoptx-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/uart@c000|frame-timeout-supported" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@c000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@c000|ADDR" "0x5000c000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/uart@c000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/adc@e000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|adc" "/soc/peripheral@50000000/adc@e000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|compatible" "nordic,nrf-saadc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|reg" "57344;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|interrupts" "14;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/adc@e000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/adc@e000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/adc@e000|ADDR" "0x5000e000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/adc@e000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/timer@f000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|timer0" "/soc/peripheral@50000000/timer@f000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|compatible" "nordic,nrf-timer;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|reg" "61440;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|interrupts" "15;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|cc-num" "6")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|max-bit-width" "32")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|max-frequency" "16000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|prescaler" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@f000|zli" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@f000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@f000|ADDR" "0x5000f000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@f000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/timer@10000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|timer1" "/soc/peripheral@50000000/timer@10000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|compatible" "nordic,nrf-timer;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|reg" "65536;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|interrupts" "16;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|cc-num" "6")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|max-bit-width" "32")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|max-frequency" "16000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|prescaler" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@10000|zli" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@10000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@10000|ADDR" "0x50010000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@10000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/timer@11000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|timer2" "/soc/peripheral@50000000/timer@11000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|compatible" "nordic,nrf-timer;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|reg" "69632;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|interrupts" "17;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|cc-num" "6")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|max-bit-width" "32")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|max-frequency" "16000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|prescaler" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/timer@11000|zli" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@11000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@11000|ADDR" "0x50011000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/timer@11000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/rtc@14000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|rtc0" "/soc/peripheral@50000000/rtc@14000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|compatible" "nordic,nrf-rtc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|reg" "81920;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|interrupts" "20;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|clock-frequency" "32768")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|prescaler" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|cc-num" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|ppi-wrap" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|fixed-top" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@14000|zli" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/rtc@14000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/rtc@14000|ADDR" "0x50014000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/rtc@14000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/rtc@15000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|rtc1" "/soc/peripheral@50000000/rtc@15000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|compatible" "nordic,nrf-rtc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|reg" "86016;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|interrupts" "21;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|clock-frequency" "32768")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|prescaler" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|cc-num" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|ppi-wrap" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|fixed-top" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/rtc@15000|zli" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/rtc@15000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/rtc@15000|ADDR" "0x50015000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/rtc@15000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/dppic@17000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|dppic0" "/soc/peripheral@50000000/dppic@17000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|dppic" "/soc/peripheral@50000000/dppic@17000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dppic@17000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dppic@17000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dppic@17000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dppic@17000|compatible" "nordic,nrf-dppic;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dppic@17000|reg" "94208;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/dppic@17000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/dppic@17000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/dppic@17000|ADDR" "0x50017000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/dppic@17000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/watchdog@18000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|wdt" "/soc/peripheral@50000000/watchdog@18000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|wdt0" "/soc/peripheral@50000000/watchdog@18000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|compatible" "nordic,nrf-wdt;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|reg" "98304;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|interrupts" "24;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@18000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/watchdog@18000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/watchdog@18000|ADDR" "0x50018000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/watchdog@18000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/watchdog@19000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|wdt1" "/soc/peripheral@50000000/watchdog@19000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|compatible" "nordic,nrf-wdt;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|reg" "102400;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|interrupts" "25;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/watchdog@19000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/watchdog@19000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/watchdog@19000|ADDR" "0x50019000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/watchdog@19000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/comparator@1a000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|comp" "/soc/peripheral@50000000/comparator@1a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|compatible" "nordic,nrf-comp;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|reg" "106496;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|interrupts" "26;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/comparator@1a000|enable-hyst" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/comparator@1a000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/comparator@1a000|ADDR" "0x5001a000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/comparator@1a000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/egu@1b000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|egu0" "/soc/peripheral@50000000/egu@1b000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|compatible" "nordic,nrf-egu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|reg" "110592;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|interrupts" "27;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1b000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1b000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1b000|ADDR" "0x5001b000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1b000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/egu@1c000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|egu1" "/soc/peripheral@50000000/egu@1c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|compatible" "nordic,nrf-egu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|reg" "114688;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|interrupts" "28;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1c000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1c000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1c000|ADDR" "0x5001c000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1c000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/egu@1d000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|egu2" "/soc/peripheral@50000000/egu@1d000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|compatible" "nordic,nrf-egu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|reg" "118784;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|interrupts" "29;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1d000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1d000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1d000|ADDR" "0x5001d000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1d000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/egu@1e000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|egu3" "/soc/peripheral@50000000/egu@1e000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|compatible" "nordic,nrf-egu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|reg" "122880;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|interrupts" "30;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1e000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1e000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1e000|ADDR" "0x5001e000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1e000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/egu@1f000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|egu4" "/soc/peripheral@50000000/egu@1f000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|compatible" "nordic,nrf-egu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|reg" "126976;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|interrupts" "31;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@1f000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1f000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1f000|ADDR" "0x5001f000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@1f000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/egu@20000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|egu5" "/soc/peripheral@50000000/egu@20000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|compatible" "nordic,nrf-egu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|reg" "131072;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|interrupts" "32;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/egu@20000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@20000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@20000|ADDR" "0x50020000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/egu@20000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/pwm@21000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm0" "/soc/peripheral@50000000/pwm@21000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|compatible" "nordic,nrf-pwm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|reg" "135168;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|interrupts" "33;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|pinctrl-names" "default;sleep;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@21000|center-aligned" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@21000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@21000|ADDR" "0x50021000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@21000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/pwm@22000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm1" "/soc/peripheral@50000000/pwm@22000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|compatible" "nordic,nrf-pwm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|reg" "139264;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|interrupts" "34;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@22000|center-aligned" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@22000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@22000|ADDR" "0x50022000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@22000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/pwm@23000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm2" "/soc/peripheral@50000000/pwm@23000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|compatible" "nordic,nrf-pwm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|reg" "143360;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|interrupts" "35;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@23000|center-aligned" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@23000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@23000|ADDR" "0x50023000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@23000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/pwm@24000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm3" "/soc/peripheral@50000000/pwm@24000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|compatible" "nordic,nrf-pwm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|reg" "147456;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|interrupts" "36;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pwm@24000|center-aligned" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@24000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@24000|ADDR" "0x50024000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pwm@24000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/pdm@26000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pdm0" "/soc/peripheral@50000000/pdm@26000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|compatible" "nordic,nrf-pdm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|reg" "155648;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|interrupts" "38;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|clock-source" "PCLK32M_HFXO")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/pdm@26000|queue-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pdm@26000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pdm@26000|ADDR" "0x50026000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/pdm@26000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/i2s@28000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2s0" "/soc/peripheral@50000000/i2s@28000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|compatible" "nordic,nrf-i2s;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|reg" "163840;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|interrupts" "40;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/i2s@28000|clock-source" "PCLK32M_HFXO")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2s@28000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2s@28000|ADDR" "0x50028000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/i2s@28000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/mbox@2a000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|mbox" "/soc/peripheral@50000000/mbox@2a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|ipc" "/soc/peripheral@50000000/mbox@2a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|compatible" "nordic,mbox-nrf-ipc;nordic,nrf-ipc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|reg" "172032;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|interrupts" "42;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|tx-mask" "65535")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mbox@2a000|rx-mask" "65535")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/mbox@2a000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/mbox@2a000|ADDR" "0x5002a000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/mbox@2a000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/qspi@2b000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|qspi" "/soc/peripheral@50000000/qspi@2b000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|compatible" "nordic,nrf-qspi;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|reg" "176128;4096;268435456;268435456;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|reg-names" "qspi;qspi_mm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|interrupts" "43;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000|pinctrl-names" "default;sleep;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qspi@2b000|NUM" "2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qspi@2b000|ADDR" "0x5002b000;0x10000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qspi@2b000|SIZE" "0x1000;0x10000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|mx25r64" "/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|compatible" "nordic,qspi-nor;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|reg" "0;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|jedec-id" "194;40;23;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|size" "67108864")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|sfdp-bfp" "229;32;241;255;255;255;255;3;68;235;8;107;8;59;4;187;238;255;255;255;255;255;0;255;255;255;0;255;12;32;15;82;16;216;0;255;35;114;245;0;130;237;4;204;68;131;104;68;48;176;48;176;247;196;213;92;0;190;41;255;240;208;255;255;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|quad-enable-requirements" "S1B6")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|requires-ulbpr" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|has-dpd" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|t-enter-dpd" "10000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|t-exit-dpd" "35000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|readoc" "read4io")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|writeoc" "pp4io")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|address-size-32" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|ppsize-512" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|sck-delay" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|cpha" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|cpol" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|sck-frequency" "8000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|ADDR" "0x0;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0|SIZE" "NONE;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/nfct@2d000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|nfct" "/soc/peripheral@50000000/nfct@2d000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|compatible" "nordic,nrf-nfct;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|reg" "184320;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|interrupts" "45;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/nfct@2d000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/nfct@2d000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/nfct@2d000|ADDR" "0x5002d000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/nfct@2d000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/mutex@30000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|mutex" "/soc/peripheral@50000000/mutex@30000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mutex@30000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mutex@30000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mutex@30000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mutex@30000|compatible" "nordic,nrf-mutex;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mutex@30000|reg" "196608;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/mutex@30000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/mutex@30000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/mutex@30000|ADDR" "0x50030000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/mutex@30000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/qdec@33000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|qdec0" "/soc/peripheral@50000000/qdec@33000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|compatible" "nordic,nrf-qdec;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|reg" "208896;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|interrupts" "51;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@33000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qdec@33000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qdec@33000|ADDR" "0x50033000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qdec@33000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/qdec@34000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|qdec1" "/soc/peripheral@50000000/qdec@34000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|compatible" "nordic,nrf-qdec;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|reg" "212992;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|interrupts" "52;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/qdec@34000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qdec@34000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qdec@34000|ADDR" "0x50034000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/qdec@34000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/usbd@36000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|usbd" "/soc/peripheral@50000000/usbd@36000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|zephyr_udc0" "/soc/peripheral@50000000/usbd@36000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|compatible" "nordic,nrf-usbd;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|reg" "221184;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|interrupts" "54;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|num-bidir-endpoints" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|num-in-endpoints" "7")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|num-out-endpoints" "7")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|num-isoin-endpoints" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/usbd@36000|num-isoout-endpoints" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/usbd@36000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/usbd@36000|ADDR" "0x50036000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/usbd@36000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/regulator@37000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|usbreg" "/soc/peripheral@50000000/regulator@37000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|compatible" "nordic,nrf-usbreg;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|reg" "225280;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|interrupts" "55;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/regulator@37000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@37000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@37000|ADDR" "0x50037000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/regulator@37000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|flash_controller" "/soc/peripheral@50000000/flash-controller@39000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000|compatible" "nordic,nrf53-flash-controller;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000|reg" "233472;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000|partial-erase" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000|ADDR" "0x50039000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|flash0" "/soc/peripheral@50000000/flash-controller@39000/flash@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|compatible" "soc-nv-flash;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|reg" "0;1048576;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|erase-block-size" "4096")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0|write-block-size" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0|ADDR" "0x0;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0|SIZE" "0x100000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|boot_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0|label" "mcuboot")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0|reg" "0;65536;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0|ADDR" "0x0;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@0|SIZE" "0x10000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|slot0_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000|label" "image-0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000|reg" "65536;262144;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000|ADDR" "0x10000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@10000|SIZE" "0x40000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|slot0_ns_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000|label" "image-0-nonsecure")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000|reg" "327680;196608;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000|ADDR" "0x50000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@50000|SIZE" "0x30000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|slot1_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000|label" "image-1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000|reg" "524288;262144;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000|ADDR" "0x80000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@80000|SIZE" "0x40000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|slot1_ns_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000|label" "image-1-nonsecure")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000|reg" "786432;196608;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000|ADDR" "0xc0000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@c0000|SIZE" "0x30000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|tfm_ps_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000|label" "tfm-ps")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000|reg" "983040;16384;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000|ADDR" "0xf0000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f0000|SIZE" "0x4000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|tfm_its_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000|label" "tfm-its")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000|reg" "999424;8192;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000|ADDR" "0xf4000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f4000|SIZE" "0x2000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|tfm_otp_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000|label" "tfm-otp")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000|reg" "1007616;8192;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000|ADDR" "0xf6000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f6000|SIZE" "0x2000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|storage_partition" "/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000|label" "storage")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000|read-only" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000|reg" "1015808;32768;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000|ADDR" "0xf8000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/flash-controller@39000/flash@0/partitions/partition@f8000|SIZE" "0x8000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/kmu@39000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|kmu" "/soc/peripheral@50000000/kmu@39000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|compatible" "nordic,nrf-kmu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|reg" "233472;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|interrupts" "57;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/kmu@39000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/kmu@39000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/kmu@39000|ADDR" "0x50039000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/kmu@39000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/vmc@81000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|vmc" "/soc/peripheral@50000000/vmc@81000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/vmc@81000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/vmc@81000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/vmc@81000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/vmc@81000|compatible" "nordic,nrf-vmc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/vmc@81000|reg" "528384;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/vmc@81000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/vmc@81000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/vmc@81000|ADDR" "0x50081000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/vmc@81000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/gpio@842500" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpio0" "/soc/peripheral@50000000/gpio@842500")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|gpio-controller" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|ngpios" "32")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|compatible" "nordic,nrf-gpio;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|reg" "8660224;768;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842500|port" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/gpio@842500|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/gpio@842500|ADDR" "0x50842500;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/gpio@842500|SIZE" "0x300;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/gpio@842800" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpio1" "/soc/peripheral@50000000/gpio@842800")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|gpio-controller" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|ngpios" "16")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|compatible" "nordic,nrf-gpio;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|reg" "8660992;768;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/gpio@842800|port" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/gpio@842800|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/gpio@842800|ADDR" "0x50842800;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/gpio@842800|SIZE" "0x300;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/peripheral@50000000/ieee802154" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|ieee802154" "/soc/peripheral@50000000/ieee802154")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ieee802154|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ieee802154|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ieee802154|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ieee802154|compatible" "nordic,nrf-ieee802154;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/peripheral@50000000/ieee802154|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/ieee802154|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/ieee802154|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/peripheral@50000000/ieee802154|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/spu@50003000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spu" "/soc/spu@50003000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|compatible" "nordic,nrf-spu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|reg" "1342189568;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|interrupts" "3;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/spu@50003000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/spu@50003000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/spu@50003000|ADDR" "0x50003000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/spu@50003000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/gpiote@5000d000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpiote" "/soc/gpiote@5000d000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpiote0" "/soc/gpiote@5000d000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|compatible" "nordic,nrf-gpiote;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|reg" "1342230528;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|interrupts" "13;5;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@5000d000|instance" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/gpiote@5000d000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/gpiote@5000d000|ADDR" "0x5000d000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/gpiote@5000d000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/gpiote@4002f000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpiote1" "/soc/gpiote@4002f000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|compatible" "nordic,nrf-gpiote;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|reg" "1073934336;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|interrupts" "47;5;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/gpiote@4002f000|instance" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/gpiote@4002f000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/gpiote@4002f000|ADDR" "0x4002f000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/gpiote@4002f000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/soc/crypto@50844000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|cryptocell" "/soc/crypto@50844000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|compatible" "nordic,cryptocell;arm,cryptocell-312;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|reg" "1350844416;4096;1350848512;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|reg-names" "wrapper;core;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|interrupts" "68;1;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/soc/crypto@50844000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/crypto@50844000|NUM" "2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/crypto@50844000|ADDR" "0x50844000;0x50845000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/soc/crypto@50844000|SIZE" "0x1000;0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pinctrl" "/pin-controller")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller|compatible" "nordic,nrf-pinctrl;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2c1_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2c1_default" "/pin-controller/i2c1_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2c1_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|psels" "184549416;201326616;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2c1_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2c1_sleep" "/pin-controller/i2c1_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2c1_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|psels" "184549416;201326616;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2c1_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2c1_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart0_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart0_default" "/pin-controller/uart0_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart0_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|psels" "44;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart0_default/group2" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|bias-pull-up" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|psels" "16777262;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default/group2|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default/group2|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_default/group2|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart0_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart0_sleep" "/pin-controller/uart0_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart0_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|psels" "44;16777262;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart0_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/pwm0_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm0_default" "/pin-controller/pwm0_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/pwm0_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|psels" "369098780;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/pwm0_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm0_sleep" "/pin-controller/pwm0_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/pwm0_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|psels" "369098780;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/pwm0_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/pwm0_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/qspi_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|qspi_default" "/pin-controller/qspi_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/qspi_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|psels" "486539281;520093709;536870926;553648143;570425360;503316498;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|nordic,drive-mode" "3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/qspi_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|qspi_sleep" "/pin-controller/qspi_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/qspi_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|psels" "486539281;520093709;536870926;553648143;570425360;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/qspi_sleep/group2" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|bias-pull-up" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|psels" "503316498;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/qspi_sleep/group2|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep/group2|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep/group2|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/qspi_sleep/group2|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart1_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart1_default" "/pin-controller/uart1_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart1_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|psels" "33;33554443;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart1_default/group2" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|bias-pull-up" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|psels" "16777248;50331658;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_default/group2|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default/group2|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default/group2|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_default/group2|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart1_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|uart1_sleep" "/pin-controller/uart1_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/uart1_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|psels" "33;16777248;33554443;50331658;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/uart1_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/uart1_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi4_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi4_default" "/pin-controller/spi4_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi4_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|psels" "67108911;100663342;83886125;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi4_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi4_sleep" "/pin-controller/spi4_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi4_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|psels" "67108911;100663342;83886125;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi4_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi4_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2s0_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2s0_default" "/pin-controller/i2s0_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2s0_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|bias-pull-down" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|psels" "218103846;251658259;285212693;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|nordic,drive-mode" "3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2s0_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|i2s0_sleep" "/pin-controller/i2s0_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/i2s0_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|psels" "218103846;251658259;285212693;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/i2s0_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/i2s0_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi2_default" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi2_default" "/pin-controller/spi2_default")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_default|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_default|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_default|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi2_default/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|low-power-enable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|psels" "67108887;83886117;100663335;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_default/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_default/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_default/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_default/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi2_sleep" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|spi2_sleep" "/pin-controller/spi2_sleep")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_sleep|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_sleep|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_sleep|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pin-controller/spi2_sleep/group1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|bias-disable" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|bias-pull-up" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|bias-pull-down" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|low-power-enable" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|psels" "67108887;83886117;100663335;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|nordic,drive-mode" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pin-controller/spi2_sleep/group1|nordic,invert" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_sleep/group1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_sleep/group1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pin-controller/spi2_sleep/group1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/entropy_bt_hci" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|rng_hci" "/entropy_bt_hci")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/entropy_bt_hci|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/entropy_bt_hci|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/entropy_bt_hci|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/entropy_bt_hci|compatible" "zephyr,bt-hci-entropy;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/entropy_bt_hci|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/entropy_bt_hci|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/entropy_bt_hci|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/entropy_bt_hci|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/sw-pwm" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sw_pwm" "/sw-pwm")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/sw-pwm|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/sw-pwm|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/sw-pwm|status" "disabled")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/sw-pwm|compatible" "nordic,nrf-sw-pwm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/sw-pwm|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/sw-pwm|clock-prescaler" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/sw-pwm|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/sw-pwm|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/sw-pwm|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/cpus" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/cpus/cpu@0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|cpu0" "/cpus/cpu@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0|compatible" "arm,cortex-m33f;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0|reg" "0;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0|ADDR" "0x0;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0|SIZE" "NONE;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/cpus/cpu@0/itm@e0000000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|itm" "/cpus/cpu@0/itm@e0000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/itm@e0000000|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/itm@e0000000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/itm@e0000000|compatible" "arm,armv8m-itm;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/itm@e0000000|reg" "3758096384;4096;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/itm@e0000000|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/itm@e0000000|swo-ref-frequency" "64000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0/itm@e0000000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0/itm@e0000000|ADDR" "0xe0000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0/itm@e0000000|SIZE" "0x1000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/cpus/cpu@0/mpu@e000ed90" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|mpu" "/cpus/cpu@0/mpu@e000ed90")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/mpu@e000ed90|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/mpu@e000ed90|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/mpu@e000ed90|compatible" "arm,armv8m-mpu;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/mpu@e000ed90|reg" "3758157200;64;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/cpus/cpu@0/mpu@e000ed90|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0/mpu@e000ed90|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0/mpu@e000ed90|ADDR" "0xe000ed90;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/cpus/cpu@0/mpu@e000ed90|SIZE" "0x40;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/ipc" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/ipc/ipc0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|ipc0" "/ipc/ipc0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|compatible" "zephyr,ipc-openamp-static-vrings;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|mbox-names" "tx;rx;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0|role" "host")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc/ipc0|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc/ipc0|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc/ipc0|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/ipc/ipc0/bt_hci_ipc0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|bt_hci_ipc0" "/ipc/ipc0/bt_hci_ipc0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|compatible" "zephyr,bt-hci-ipc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|bt-hci-name" "IPC")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|bt-hci-bus" "BT_HCI_BUS_IPM")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|bt-hci-quirks" "BT_HCI_QUIRK_NO_AUTO_DLE;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|bt-hci-vs-ext" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/ipc/ipc0/bt_hci_ipc0|bt-hci-ipc-name" "nrf_bt_hci")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc/ipc0/bt_hci_ipc0|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc/ipc0/bt_hci_ipc0|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/ipc/ipc0/bt_hci_ipc0|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/leds" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/leds|compatible" "gpio-leds;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/leds/led_0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|led0" "/leds/led_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/leds/led_0|label" "Green LED 0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_0|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_0|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_0|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/leds/led_1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|led1" "/leds/led_1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/leds/led_1|label" "Green LED 1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/leds/led_2" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|led2" "/leds/led_2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/leds/led_2|label" "Green LED 2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_2|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_2|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_2|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/leds/led_3" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|led3" "/leds/led_3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/leds/led_3|label" "Green LED 3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_3|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_3|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/leds/led_3|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/buttons" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons|compatible" "gpio-keys;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons|debounce-interval-ms" "30")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons|polling-mode" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/buttons/button_0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|button0" "/buttons/button_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_0|label" "Push button 1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_0|zephyr,code" "11")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_0|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_0|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_0|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/buttons/button_1" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|button1" "/buttons/button_1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_1|label" "Push button 2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_1|zephyr,code" "2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_1|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_1|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_1|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/buttons/button_2" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|button2" "/buttons/button_2")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_2|label" "Push button 3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_2|zephyr,code" "3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_2|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_2|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_2|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/buttons/button_3" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|button3" "/buttons/button_3")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_3|label" "Push button 4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/buttons/button_3|zephyr,code" "4")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_3|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_3|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/buttons/button_3|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/connector" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|arduino_header" "/connector")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/connector|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/connector|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/connector|compatible" "arduino-header-r3;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/connector|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/connector|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/connector|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/connector|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pwmleds" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/pwmleds|compatible" "pwm-leds;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pwmleds|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pwmleds|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pwmleds|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/pwmleds/pwm_led_0" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|pwm_led0" "/pwmleds/pwm_led_0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pwmleds/pwm_led_0|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pwmleds/pwm_led_0|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/pwmleds/pwm_led_0|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/analog-connector" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|arduino_adc" "/analog-connector")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/analog-connector|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/analog-connector|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/analog-connector|compatible" "arduino,uno-adc;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/analog-connector|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/analog-connector|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/analog-connector|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/analog-connector|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/nrf-gpio-forwarder" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|gpio_fwd" "/nrf-gpio-forwarder")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/nrf-gpio-forwarder|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/nrf-gpio-forwarder|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/nrf-gpio-forwarder|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/nrf-gpio-forwarder|compatible" "nordic,nrf-gpio-forwarder;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/nrf-gpio-forwarder|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/nrf-gpio-forwarder/uart" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/uart|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/uart|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/uart|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/nrf-gpio-forwarder/nrf21540-spi-if" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/nrf21540-spi-if|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/nrf21540-spi-if|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/nrf21540-spi-if|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/nrf-gpio-forwarder/nrf21540-gpio-if" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/nrf21540-gpio-if|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/nrf21540-gpio-if|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/nrf-gpio-forwarder/nrf21540-gpio-if|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/reserved-memory" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/reserved-memory|ranges" "None")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/reserved-memory/image@20000000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sram0_image" "/reserved-memory/image@20000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/reserved-memory/image@20000000|reg" "536870912;458752;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image@20000000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image@20000000|ADDR" "0x20000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image@20000000|SIZE" "0x70000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/reserved-memory/image_s@20000000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sram0_s" "/reserved-memory/image_s@20000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/reserved-memory/image_s@20000000|reg" "536870912;262144;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_s@20000000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_s@20000000|ADDR" "0x20000000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_s@20000000|SIZE" "0x40000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/reserved-memory/image_ns@20040000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sram0_ns" "/reserved-memory/image_ns@20040000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/reserved-memory/image_ns@20040000|reg" "537133056;262144;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_ns@20040000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_ns@20040000|ADDR" "0x20040000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_ns@20040000|SIZE" "0x40000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/reserved-memory/image_ns_app@20040000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sram0_ns_app" "/reserved-memory/image_ns_app@20040000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/reserved-memory/image_ns_app@20040000|reg" "537133056;196608;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_ns_app@20040000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_ns_app@20040000|ADDR" "0x20040000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/image_ns_app@20040000|SIZE" "0x30000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/reserved-memory/memory@20070000" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|sram0_shared" "/reserved-memory/memory@20070000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/reserved-memory/memory@20070000|reg" "537329664;65536;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/memory@20070000|NUM" "1")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/memory@20070000|ADDR" "0x20070000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/reserved-memory/memory@20070000|SIZE" "0x10000;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/mic_power" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|mic_power" "/mic_power")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|compatible" "regulator-fixed;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|label" "MIC_VDD")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|regulator-name" "mic-vdd")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|regulator-min-microvolt" "1800000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|regulator-max-microvolt" "1800000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|regulator-always-on" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|regulator-boot-on" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/mic_power|startup-delay-us" "100000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/mic_power|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/mic_power|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/mic_power|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODE|/adxl382-pwr-ctrl" TRUE)
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_NODELABEL|adxl382_pwr_ctrl" "/adxl382-pwr-ctrl")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|wakeup-source" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|zephyr,pm-device-runtime-auto" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|status" "okay")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|compatible" "regulator-fixed;")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|zephyr,deferred-init" "False")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|regulator-name" "tck106ag")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|regulator-min-microvolt" "3300000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|regulator-max-microvolt" "3300000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|regulator-always-on" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|regulator-boot-on" "True")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_PROP|/adxl382-pwr-ctrl|startup-delay-us" "10000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/adxl382-pwr-ctrl|NUM" "0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/adxl382-pwr-ctrl|ADDR" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_REG|/adxl382-pwr-ctrl|SIZE" "")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf5340-dk-nrf5340-cpuapp" "/")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf5340-cpuapp-qkaa" "/soc")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf5340-cpuapp" "/soc")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf53" "/soc")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|simple-bus" "/soc")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arm,v8m-nvic" "/soc/interrupt-controller@e000e100")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arm,armv8m-systick" "/soc/timer@e000e010")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-ficr" "/soc/ficr@ff0000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-uicr" "/soc/uicr@ff8000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|mmio-sram" "/soc/memory@20000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-dcnf" "/soc/peripheral@50000000/dcnf@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-oscillators" "/soc/peripheral@50000000/oscillator@4000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf53x-regulators" "/soc/peripheral@50000000/regulator@4000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf5x-regulator" "/soc/peripheral@50000000/regulator@4000/regulator@4704;/soc/peripheral@50000000/regulator@4000/regulator@4904")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf53x-regulator-hv" "/soc/peripheral@50000000/regulator@4000/regulator@4b00")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-clock" "/soc/peripheral@50000000/clock@5000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-power" "/soc/peripheral@50000000/power@5000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-gpregret" "/soc/peripheral@50000000/power@5000/gpregret1@51c;/soc/peripheral@50000000/power@5000/gpregret2@520")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-reset" "/soc/peripheral@50000000/reset-controller@5000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-ctrlapperi" "/soc/peripheral@50000000/ctrlap@6000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-twim" "/soc/peripheral@50000000/i2c@8000;/soc/peripheral@50000000/i2c@9000;/soc/peripheral@50000000/i2c@b000;/soc/peripheral@50000000/i2c@c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-spim" "/soc/peripheral@50000000/spi@8000;/soc/peripheral@50000000/spi@9000;/soc/peripheral@50000000/spi@a000;/soc/peripheral@50000000/spi@b000;/soc/peripheral@50000000/spi@c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-uarte" "/soc/peripheral@50000000/uart@8000;/soc/peripheral@50000000/uart@9000;/soc/peripheral@50000000/uart@b000;/soc/peripheral@50000000/uart@c000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-saadc" "/soc/peripheral@50000000/adc@e000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-timer" "/soc/peripheral@50000000/timer@f000;/soc/peripheral@50000000/timer@10000;/soc/peripheral@50000000/timer@11000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-rtc" "/soc/peripheral@50000000/rtc@14000;/soc/peripheral@50000000/rtc@15000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-dppic" "/soc/peripheral@50000000/dppic@17000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-wdt" "/soc/peripheral@50000000/watchdog@18000;/soc/peripheral@50000000/watchdog@19000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-comp" "/soc/peripheral@50000000/comparator@1a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-egu" "/soc/peripheral@50000000/egu@1b000;/soc/peripheral@50000000/egu@1c000;/soc/peripheral@50000000/egu@1d000;/soc/peripheral@50000000/egu@1e000;/soc/peripheral@50000000/egu@1f000;/soc/peripheral@50000000/egu@20000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-pwm" "/soc/peripheral@50000000/pwm@21000;/soc/peripheral@50000000/pwm@22000;/soc/peripheral@50000000/pwm@23000;/soc/peripheral@50000000/pwm@24000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-pdm" "/soc/peripheral@50000000/pdm@26000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-i2s" "/soc/peripheral@50000000/i2s@28000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,mbox-nrf-ipc" "/soc/peripheral@50000000/mbox@2a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-ipc" "/soc/peripheral@50000000/mbox@2a000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-qspi" "/soc/peripheral@50000000/qspi@2b000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,qspi-nor" "/soc/peripheral@50000000/qspi@2b000/mx25r6435f@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-nfct" "/soc/peripheral@50000000/nfct@2d000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-mutex" "/soc/peripheral@50000000/mutex@30000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-qdec" "/soc/peripheral@50000000/qdec@33000;/soc/peripheral@50000000/qdec@34000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-usbd" "/soc/peripheral@50000000/usbd@36000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-usbreg" "/soc/peripheral@50000000/regulator@37000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf53-flash-controller" "/soc/peripheral@50000000/flash-controller@39000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|soc-nv-flash" "/soc/peripheral@50000000/flash-controller@39000/flash@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-kmu" "/soc/peripheral@50000000/kmu@39000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-vmc" "/soc/peripheral@50000000/vmc@81000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-gpio" "/soc/peripheral@50000000/gpio@842500;/soc/peripheral@50000000/gpio@842800")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-ieee802154" "/soc/peripheral@50000000/ieee802154")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-spu" "/soc/spu@50003000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-gpiote" "/soc/gpiote@5000d000;/soc/gpiote@4002f000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,cryptocell" "/soc/crypto@50844000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arm,cryptocell-312" "/soc/crypto@50844000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-pinctrl" "/pin-controller")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|zephyr,bt-hci-entropy" "/entropy_bt_hci")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-sw-pwm" "/sw-pwm")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arm,cortex-m33f" "/cpus/cpu@0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arm,armv8m-itm" "/cpus/cpu@0/itm@e0000000")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arm,armv8m-mpu" "/cpus/cpu@0/mpu@e000ed90")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|zephyr,ipc-openamp-static-vrings" "/ipc/ipc0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|zephyr,bt-hci-ipc" "/ipc/ipc0/bt_hci_ipc0")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|gpio-leds" "/leds")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|gpio-keys" "/buttons")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arduino-header-r3" "/connector")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|pwm-leds" "/pwmleds")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|arduino,uno-adc" "/analog-connector")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|nordic,nrf-gpio-forwarder" "/nrf-gpio-forwarder")
set_target_properties(${DEVICETREE_TARGET} PROPERTIES "DT_COMP|regulator-fixed" "/mic_power;/adxl382-pwr-ctrl")
