#
# Copyright (c) 2020 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

cmake_minimum_required(VERSION 3.20.0)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})

project(sw2600)

# NORDIC SDK APP START
target_sources(app PRIVATE
    # 原项目风格温度传感器 + 超低功耗
    src/main_original_temp.c
    # src/main_smart_temp.c
    # src/main_minimal_temp.c
    # src/main_temp_lowpower.c
    # src/main_ultra_simple.c
    # src/main_temp_low_power.c
    # src/main_simple_temp_test.c
    # src/main_temperature_working.c
    # src/main_debug_temperature.c
    # src/main_step1_temperature.c
    # src/main_step1_m117_working.c
    # src/main_step1_m117_simple.c
    # src/main_simple_power_test.c
    # src/main_step1_m117_v2.c
    # src/main_step1_m117.c
    # src/main_simple_working.c
    # src/main_ultra_low_power.c
    # src/main_power_test.c
    # src/main.c

    # 硬件驱动层 (HAL) - 重新启用I2C
    src/hal/zephyr_i2c_driver.c

    # 传感器层 - 重新启用M117温度传感器
    src/sensors/m117_sensor.c

    # 暂时注释掉其他模块
    # src/power/simple_power_manager.c
    # src/sensors/m117_sensor_simple.c
    # src/hal/zephyr_i2s_driver.c
    # src/hal/zephyr_spi_driver.c

    # 传感器层 - 最小化版本
    # src/sensors/m117_sensor.c
    # src/sensors/t5848_mic.c
    # src/sensors/adxl38x_sensor.c
    # src/sensors/sensor_manager.c
    # src/sensors/vibration_manager_sw2200.c

    # 计算处理层 - 基础版本
    # src/calculate/vibration_calc.c
    # src/calculate/fft_processor.c

    # 通信层 - Zigbee必需
    # src/communication/zigbee.c
    # src/communication/sw2200_protocol.c

    # 存储层 - 配置存储
    # src/storage/config_storage.c

    # 电源管理层 - 暂时注释掉，使用简单的system_off
    # src/power/sw2600_power_manager.c

    # 保留的演示和测试模块（可选编译）
    # src/main_vibration_demo.c
    # src/tests/adxl38x_tests.c
    # src/tests/adxl38x_calc.c
)

target_include_directories(app PRIVATE
    include
    src
    src/hal
    src/sensors
    src/calculate
    src/communication
    src/storage
    src/power
    src/config
    src/tests
)

# 编译选项优化
target_compile_options(app PRIVATE
    # 抑制第三方库和未使用参数的警告
    -Wno-maybe-uninitialized
    -Wno-unused-parameter
    -Wno-double-promotion
)

# NORDIC SDK APP END

target_sources_ifdef(CONFIG_BT_NUS app PRIVATE
  src/nus_cmd.c
)
