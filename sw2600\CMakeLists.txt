#
# Copyright (c) 2020 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

cmake_minimum_required(VERSION 3.20.0)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})

project(sw2600)

# NORDIC SDK APP START
target_sources(app PRIVATE
    # Main application
    src/main.c

    # Sensor drivers - Incremental addition
    src/sensors/m117_temperature.c  # GPIO I2C temperature sensor
    # src/sensors/t5848_microphone.c  # Disabled for now
    # src/sensors/adxl38x_sensor.c    # Disabled for now
    # src/sensors/spi_driver.c        # Disabled for now
)

target_include_directories(app PRIVATE
    include
    src
    src/sensors
)

# 编译选项优化
target_compile_options(app PRIVATE
    # 抑制第三方库和未使用参数的警告
    -Wno-maybe-uninitialized
    -Wno-unused-parameter
    -Wno-double-promotion
)

# NORDIC SDK APP END
