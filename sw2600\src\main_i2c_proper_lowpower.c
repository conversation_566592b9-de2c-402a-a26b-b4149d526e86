/*
 * SW2600 Proper I2C Low Power Management
 * Keep I2C enabled but properly suspend it after use
 * Target: 3.5µA sleep current with working M117 temperature reading
 */

#include <zephyr/kernel.h>
#include <zephyr/sys/poweroff.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/i2c.h>
#include <zephyr/pm/device.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include "sensors/m117_sensor.h"

LOG_MODULE_REGISTER(main, LOG_LEVEL_INF);

/* GPIO button for wake-up */
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});

/* I2C device */
static const struct device *i2c_dev;

/* Configure wake-up GPIO */
static int configure_wakeup_gpio(void)
{
    if (!gpio_is_ready_dt(&button)) {
        LOG_ERR("Button device not ready");
        return -ENODEV;
    }

    int ret = gpio_pin_configure_dt(&button, GPIO_INPUT);
    if (ret < 0) {
        LOG_ERR("Failed to configure button GPIO: %d", ret);
        return ret;
    }

    ret = gpio_pin_interrupt_configure_dt(&button, GPIO_INT_LEVEL_ACTIVE);
    if (ret < 0) {
        LOG_ERR("Failed to configure button interrupt: %d", ret);
        return ret;
    }

    LOG_INF("Wake-up GPIO configured");
    return 0;
}

/* Initialize I2C device */
static int init_i2c_device(void)
{
    i2c_dev = DEVICE_DT_GET(DT_NODELABEL(i2c1));
    
    if (!device_is_ready(i2c_dev)) {
        LOG_ERR("I2C device not ready");
        return -ENODEV;
    }
    
    LOG_INF("I2C device initialized");
    return 0;
}

/* Resume I2C device for operation */
static int resume_i2c_device(void)
{
    if (!i2c_dev) {
        return -ENODEV;
    }
    
    int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_RESUME);
    if (ret < 0 && ret != -EALREADY) {
        LOG_ERR("Failed to resume I2C: %d", ret);
        return ret;
    }
    
    LOG_INF("I2C device resumed");
    return 0;
}

/* Suspend I2C device for power saving */
static int suspend_i2c_device(void)
{
    if (!i2c_dev) {
        return -ENODEV;
    }
    
    int ret = pm_device_action_run(i2c_dev, PM_DEVICE_ACTION_SUSPEND);
    if (ret < 0 && ret != -EALREADY) {
        LOG_ERR("Failed to suspend I2C: %d", ret);
        return ret;
    }
    
    LOG_INF("I2C device suspended");
    return 0;
}

/* Initialize M117 sensor */
static int init_m117_sensor(void)
{
    LOG_INF("Initializing M117 sensor...");

    int ret = m117_init(M117_MPS_1, M117_REPEAT_MEDIUM);
    if (ret < 0) {
        LOG_ERR("M117 init failed: %d", ret);
        return ret;
    }

    LOG_INF("M117 sensor initialized successfully");
    return 0;
}

/* Read M117 temperature using correct protocol */
static int read_m117_temperature(float *temperature)
{
    int ret = m117_measure_temperature(temperature);
    if (ret < 0) {
        LOG_ERR("M117 temperature measurement failed: %d", ret);
        return ret;
    }

    LOG_INF("Temperature: %.2f°C", (double)*temperature);
    return 0;
}

/* Collect temperature data with proper I2C power management */
static void collect_temperature_data(void)
{
    LOG_INF("=== Temperature Data Collection ===");

    /* Resume I2C device */
    int ret = resume_i2c_device();
    if (ret < 0) {
        LOG_ERR("I2C resume failed: %d", ret);
        return;
    }

    /* Initialize M117 sensor */
    ret = init_m117_sensor();
    if (ret < 0) {
        LOG_ERR("M117 initialization failed: %d", ret);
        suspend_i2c_device();
        return;
    }

    /* Read temperature */
    float temperature = 0.0f;
    ret = read_m117_temperature(&temperature);
    if (ret < 0) {
        LOG_ERR("Temperature reading failed: %d", ret);
    }

    /* Immediately suspend I2C after use */
    ret = suspend_i2c_device();
    if (ret < 0) {
        LOG_ERR("I2C suspend failed: %d", ret);
    }

    LOG_INF("=== Temperature Collection Complete ===");
}

/* Suspend all peripherals for ultra-low power */
static void suspend_all_peripherals(void)
{
    LOG_INF("=== FINAL PERIPHERAL SUSPENSION ===");
    
    /* Ensure I2C is suspended */
    suspend_i2c_device();
    
    /* Suspend console device (critical for low power) */
    const struct device *cons = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
    if (device_is_ready(cons)) {
        int rc = pm_device_action_run(cons, PM_DEVICE_ACTION_SUSPEND);
        if (rc < 0) {
            LOG_ERR("Could not suspend console (%d)", rc);
        } else {
            LOG_INF("Console suspended");
        }
    }
    
    LOG_INF("=== ALL PERIPHERALS SUSPENDED ===");
}

int main(void)
{
    LOG_INF("\n=== SW2600 PROPER I2C LOW POWER ===");
    LOG_INF("Target: 3.5µA sleep current with working M117");
    
    /* Configure wake-up GPIO */
    int ret = configure_wakeup_gpio();
    if (ret < 0) {
        LOG_ERR("GPIO config failed: %d", ret);
    }
    
    /* Initialize I2C device */
    ret = init_i2c_device();
    if (ret < 0) {
        LOG_ERR("I2C init failed: %d", ret);
    }
    
    /* Collect temperature data with proper power management */
    collect_temperature_data();
    
    /* Wait before sleep */
    LOG_INF("Waiting 2 seconds before sleep...");
    k_sleep(K_SECONDS(2));
    
    LOG_INF("Entering ultra-low power mode...");
    LOG_INF("Press button to wake up");
    
    /* Small delay for UART output */
    k_msleep(200);
    
    /* Suspend all peripherals */
    suspend_all_peripherals();
    
    /* Final delay */
    k_msleep(100);
    
    /* Enter system off mode */
    sys_poweroff();
    
    /* Should never reach here */
    LOG_ERR("ERROR: Should not reach here!");
    while(1) {
        k_sleep(K_SECONDS(1));
        LOG_ERR("Still running!");
    }
    return 0;
}
