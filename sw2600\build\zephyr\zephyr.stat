ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x4a59
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1484964 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00c8e8 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000ca40 00cb20 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000ca48 00cb28 000088 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000cad0 00cbb0 000090 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000cb60 00cc40 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000cd88 00ce68 000088 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000ce10 00cef0 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000ce20 00cf00 001770 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00eab8 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00e670 000360 00  WA  0   0  4
  [12] device_states     PROGBITS        20000360 00e9d0 00000c 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        2000036c 00e9dc 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        200003a4 00ea14 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        200003a8 00ea18 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        20000434 00eaa4 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000448 00eab8 001141 00  WA  0   0  8
  [18] noinit            NOBITS          20001590 00eab8 001040 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00eab8 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00eaf8 001c90 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 010788 0b2e63 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 0c35eb 012e5d 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0d6448 02f745 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 105b90 00528c 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 10ae1c 01079c 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 11b5b8 032221 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 14d7e0 0089b0 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 156190 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000e9d4 00eab4 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 1561cc 00b780 10     31 1706  4
  [31] .strtab           STRTAB          00000000 16194c 008de6 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 16a732 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00cb20 0x0000ca40 0x0000ca40 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0e590 0x0e590 RWE 0x10
  LOAD           0x00e670 0x20000000 0x0000e590 0x00444 0x00444 RW  0x4
  LOAD           0x00eab4 0x0000e9d4 0x0000e9d4 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000448 0x20000448 0x00000 0x02188 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
