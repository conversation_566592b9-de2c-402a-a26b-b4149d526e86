ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x1571
  Start of program headers:          52 (bytes into file)
  Start of section headers:          866052 (bytes into file)
  Flags:                             0x5000200, Version5 EABI, soft-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         28
  Section header string table index: 27

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000154 000234 0069a8 00  AX  0   0  4
  [ 3] initlevel         PROGBITS        00006afc 006bdc 000070 00   A  0   0  4
  [ 4] device_area       PROGBITS        00006b6c 006c4c 000078 00   A  0   0  4
  [ 5] sw_isr_table      PROGBITS        00006be4 006cc4 000228 00  WA  0   0  4
  [ 6] tbss              NOBITS          00006e0c 006eec 000004 00 WAT  0   0  4
  [ 7] rodata            PROGBITS        00006e10 006ef0 0008ec 00   A  0   0 16
  [ 8] .ramfunc          PROGBITS        20000000 007998 000000 00   W  0   0  1
  [ 9] datas             PROGBITS        20000000 0077dc 00015c 00  WA  0   0  4
  [10] device_states     PROGBITS        2000015c 007938 00000a 00  WA  0   0  1
  [11] k_mutex_area      PROGBITS        20000168 007944 000050 00  WA  0   0  4
  [12] bss               NOBITS          200001b8 007998 000a86 00  WA  0   0  8
  [13] noinit            NOBITS          20000c40 007998 000d40 00  WA  0   0  8
  [14] .comment          PROGBITS        00000000 007998 000040 01  MS  0   0  1
  [15] .debug_aranges    PROGBITS        00000000 0079d8 001728 00      0   0  8
  [16] .debug_info       PROGBITS        00000000 009100 0503c4 00      0   0  1
  [17] .debug_abbrev     PROGBITS        00000000 0594c4 00e54f 00      0   0  1
  [18] .debug_line       PROGBITS        00000000 067a13 023a1c 00      0   0  1
  [19] .debug_frame      PROGBITS        00000000 08b430 0035ec 00      0   0  4
  [20] .debug_str        PROGBITS        00000000 08ea1c 00de35 01  MS  0   0  1
  [21] .debug_loc        PROGBITS        00000000 09c851 02202d 00      0   0  1
  [22] .debug_ranges     PROGBITS        00000000 0be880 005e70 00      0   0  8
  [23] .ARM.attributes   ARM_ATTRIBUTES  00000000 0c46f0 000038 00      0   0  1
  [24] .last_section     PROGBITS        000078b4 007994 000004 00   A  0   0  1
  [25] .symtab           SYMTAB          00000000 0c4728 007fb0 10     26 1095  4
  [26] .strtab           STRTAB          00000000 0cc6d8 006f14 00      0   0  1
  [27] .shstrtab         STRTAB          00000000 0d35ec 000117 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  LOAD           0x0000e0 0x00000000 0x00000000 0x076fc 0x076fc RWE 0x10
  LOAD           0x0077dc 0x20000000 0x000076fc 0x001b8 0x001b8 RW  0x4
  LOAD           0x007994 0x000078b4 0x000078b4 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x200001b8 0x200001b8 0x00000 0x017c8 RW  0x8
  TLS            0x006eec 0x00006e0c 0x00006e0c 0x00000 0x00004 R   0x4

 Section to Segment mapping:
  Segment Sections...
   00     rom_start text initlevel device_area sw_isr_table rodata 
   01     datas device_states k_mutex_area 
   02     .last_section 
   03     bss noinit 
   04     tbss 
