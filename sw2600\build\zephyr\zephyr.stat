ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x4c69
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1400460 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         34
  Section header string table index: 33

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00bdc4 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000bf1c 00bffc 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000bf24 00c004 000088 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000bfac 00c08c 000078 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000c024 00c104 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000c24c 00c32c 000070 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000c2bc 00c39c 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000c2d0 00c3b0 001718 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00deec 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00dac8 000328 00  WA  0   0  4
  [12] device_states     PROGBITS        20000328 00ddf0 00000a 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000334 00ddfc 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000036c 00de34 000004 00  WA  0   0  4
  [15] k_heap_area       PROGBITS        20000370 00de38 000014 00  WA  0   0  4
  [16] k_mutex_area      PROGBITS        20000384 00de4c 00008c 00  WA  0   0  4
  [17] k_sem_area        PROGBITS        20000410 00ded8 000010 00  WA  0   0  4
  [18] bss               NOBITS          20000420 00def0 0010bd 00  WA  0   0  8
  [19] noinit            NOBITS          200014e0 00def0 009c40 00  WA  0   0  8
  [20] .comment          PROGBITS        00000000 00deec 000040 01  MS  0   0  1
  [21] .debug_aranges    PROGBITS        00000000 00df30 001c00 00      0   0  8
  [22] .debug_info       PROGBITS        00000000 00fb30 0a51fe 00      0   0  1
  [23] .debug_abbrev     PROGBITS        00000000 0b4d2e 01235f 00      0   0  1
  [24] .debug_line       PROGBITS        00000000 0c708d 02d485 00      0   0  1
  [25] .debug_frame      PROGBITS        00000000 0f4514 005220 00      0   0  4
  [26] .debug_str        PROGBITS        00000000 0f9734 00f858 01  MS  0   0  1
  [27] .debug_loc        PROGBITS        00000000 108f8c 0315d8 00      0   0  1
  [28] .debug_ranges     PROGBITS        00000000 13a568 0081b0 00      0   0  8
  [29] .ARM.attributes   ARM_ATTRIBUTES  00000000 142718 00003a 00      0   0  1
  [30] .last_section     PROGBITS        0000de08 00dee8 000004 00   A  0   0  1
  [31] .symtab           SYMTAB          00000000 142754 00ae50 10     32 1599  4
  [32] .strtab           STRTAB          00000000 14d5a4 00876b 00      0   0  1
  [33] .shstrtab         STRTAB          00000000 155d0f 00017c 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00bffc 0x0000bf1c 0x0000bf1c 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0d9e8 0x0d9e8 RWE 0x10
  LOAD           0x00dac8 0x20000000 0x0000d9e8 0x00420 0x00420 RW  0x4
  LOAD           0x00dee8 0x0000de08 0x0000de08 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000420 0x20000420 0x00000 0x0ad00 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_heap_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
