ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x4685
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1234528 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00b544 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000b69c 00b77c 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000b6a4 00b784 000080 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000b724 00b804 000078 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000b79c 00b87c 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000b9c4 00baa4 000060 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000ba24 00bb04 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000ba40 00bb20 001268 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00d198 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00cd88 000328 00  WA  0   0  4
  [12] device_states     PROGBITS        20000328 00d0b0 00000a 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000334 00d0bc 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000036c 00d0f4 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        20000370 00d0f8 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        200003fc 00d184 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000410 00d198 0010bd 00  WA  0   0  8
  [18] noinit            NOBITS          200014d0 00d198 001040 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00d198 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00d1d8 001a30 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 00ec08 08a3a7 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 098faf 010ef1 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0a9ea0 02a226 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 0d40c8 004c30 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 0d8cf8 00f06b 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 0e7d63 02af6c 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 112cd0 007700 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 11a3d0 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000d0b4 00d194 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 11a40c 00aa30 10     31 1533  4
  [31] .strtab           STRTAB          00000000 124e3c 0086b3 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 12d4ef 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00b77c 0x0000b69c 0x0000b69c 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0cca8 0x0cca8 RWE 0x10
  LOAD           0x00cd88 0x20000000 0x0000cca8 0x0040c 0x0040c RW  0x4
  LOAD           0x00d194 0x0000d0b4 0x0000d0b4 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000410 0x20000410 0x00000 0x02100 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
