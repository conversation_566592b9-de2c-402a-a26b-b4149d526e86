ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x4b95
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1518236 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00ca50 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000cba8 00cc88 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000cbb0 00cc90 000088 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000cc38 00cd18 000090 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000ccc8 00cda8 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000cef0 00cfd0 000088 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000cf78 00d058 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000cf90 00d070 001940 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00edf8 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00e9b0 000360 00  WA  0   0  4
  [12] device_states     PROGBITS        20000360 00ed10 00000c 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        2000036c 00ed1c 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        200003a4 00ed54 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        200003a8 00ed58 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        20000434 00ede4 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000448 00edf8 001149 00  WA  0   0  8
  [18] noinit            NOBITS          20001598 00edf8 001040 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00edf8 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00ee38 001cd8 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 010b10 0b97b6 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 0ca2c6 0131dc 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0dd4a2 02fddf 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 10d284 0052e4 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 112568 01086c 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 122dd4 032a63 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 155838 008a70 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 15e2a8 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000ed14 00edf4 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 15e2e4 00b840 10     31 1716  4
  [31] .strtab           STRTAB          00000000 169b24 008e08 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 17292c 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00cc88 0x0000cba8 0x0000cba8 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0e8d0 0x0e8d0 RWE 0x10
  LOAD           0x00e9b0 0x20000000 0x0000e8d0 0x00444 0x00444 RW  0x4
  LOAD           0x00edf4 0x0000ed14 0x0000ed14 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000448 0x20000448 0x00000 0x02190 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
