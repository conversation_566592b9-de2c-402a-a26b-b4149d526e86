ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0xafd
  Start of program headers:          52 (bytes into file)
  Start of section headers:          844120 (bytes into file)
  Flags:                             0x5000200, Version5 EABI, soft-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         28
  Section header string table index: 27

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000154 000234 005e04 00  AX  0   0  4
  [ 3] initlevel         PROGBITS        00005f58 006038 000070 00   A  0   0  4
  [ 4] device_area       PROGBITS        00005fc8 0060a8 000078 00   A  0   0  4
  [ 5] sw_isr_table      PROGBITS        00006040 006120 000228 00  WA  0   0  4
  [ 6] tbss              NOBITS          00006268 006348 000004 00 WAT  0   0  4
  [ 7] rodata            PROGBITS        00006270 006350 00064c 00   A  0   0 16
  [ 8] .ramfunc          PROGBITS        20000000 006b58 000000 00   W  0   0  1
  [ 9] datas             PROGBITS        20000000 00699c 00015c 00  WA  0   0  4
  [10] device_states     PROGBITS        2000015c 006af8 00000a 00  WA  0   0  1
  [11] k_mutex_area      PROGBITS        20000168 006b04 000050 00  WA  0   0  4
  [12] bss               NOBITS          200001b8 006b58 000a81 00  WA  0   0  8
  [13] noinit            NOBITS          20000c40 006b58 000d40 00  WA  0   0  8
  [14] .comment          PROGBITS        00000000 006b58 000040 01  MS  0   0  1
  [15] .debug_aranges    PROGBITS        00000000 006b98 0016a8 00      0   0  8
  [16] .debug_info       PROGBITS        00000000 008240 04e8e5 00      0   0  1
  [17] .debug_abbrev     PROGBITS        00000000 056b25 00deef 00      0   0  1
  [18] .debug_line       PROGBITS        00000000 064a14 022ee6 00      0   0  1
  [19] .debug_frame      PROGBITS        00000000 0878fc 00336c 00      0   0  4
  [20] .debug_str        PROGBITS        00000000 08ac68 00dac3 01  MS  0   0  1
  [21] .debug_loc        PROGBITS        00000000 09872b 021486 00      0   0  1
  [22] .debug_ranges     PROGBITS        00000000 0b9bb8 005c78 00      0   0  8
  [23] .ARM.attributes   ARM_ATTRIBUTES  00000000 0bf830 000038 00      0   0  1
  [24] .last_section     PROGBITS        00006a74 006b54 000004 00   A  0   0  1
  [25] .symtab           SYMTAB          00000000 0bf868 007b60 10     26 1067  4
  [26] .strtab           STRTAB          00000000 0c73c8 006c77 00      0   0  1
  [27] .shstrtab         STRTAB          00000000 0ce03f 000117 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  LOAD           0x0000e0 0x00000000 0x00000000 0x068bc 0x068bc RWE 0x10
  LOAD           0x00699c 0x20000000 0x000068bc 0x001b8 0x001b8 RW  0x4
  LOAD           0x006b54 0x00006a74 0x00006a74 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x200001b8 0x200001b8 0x00000 0x017c8 RW  0x8
  TLS            0x006348 0x00006268 0x00006268 0x00000 0x00004 R   0x4

 Section to Segment mapping:
  Segment Sections...
   00     rom_start text initlevel device_area sw_isr_table rodata 
   01     datas device_states k_mutex_area 
   02     .last_section 
   03     bss noinit 
   04     tbss 
