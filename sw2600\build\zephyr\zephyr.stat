ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x4e4d
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1605520 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00d1a4 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000d2fc 00d3dc 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000d304 00d3e4 000090 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000d394 00d474 000090 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000d424 00d504 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000d64c 00d72c 000088 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000d6d4 00d7b4 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000d6f0 00d7d0 001810 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00f45c 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00efe0 000394 00  WA  0   0  8
  [12] device_states     PROGBITS        20000394 00f374 00000c 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        200003a0 00f380 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        200003d8 00f3b8 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        200003dc 00f3bc 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        20000468 00f448 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000478 00f460 001259 00  WA  0   0  8
  [18] noinit            NOBITS          200016d8 00f460 001440 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00f45c 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00f4a0 001e50 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 0112f0 0c3a6f 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 0d4d5f 01454a 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0e92a9 033ab9 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 11cd64 00574c 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 1224b0 010ef3 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 1333a3 03727e 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 16a628 009040 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 173668 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000f378 00f458 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 1736a4 00ba00 10     31 1745  4
  [31] .strtab           STRTAB          00000000 17f0a4 008d7c 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 187e20 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00d3dc 0x0000d2fc 0x0000d2fc 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0ef00 0x0ef00 RWE 0x10
  LOAD           0x00efe0 0x20000000 0x0000ef00 0x00478 0x00478 RW  0x8
  LOAD           0x00f458 0x0000f378 0x0000f378 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000478 0x20000478 0x00000 0x026a0 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
