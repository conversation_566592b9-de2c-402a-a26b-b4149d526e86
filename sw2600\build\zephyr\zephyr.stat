ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x46d1
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1229312 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00b4fc 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000b654 00b734 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000b65c 00b73c 000080 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000b6dc 00b7bc 000078 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000b754 00b834 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000b97c 00ba5c 000060 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000b9dc 00babc 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000b9f0 00bad0 0012dc 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00d1bc 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00cdac 000328 00  WA  0   0  4
  [12] device_states     PROGBITS        20000328 00d0d4 00000a 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000334 00d0e0 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000036c 00d118 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        20000370 00d11c 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        200003fc 00d1a8 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000410 00d1c0 0010bd 00  WA  0   0  8
  [18] noinit            NOBITS          200014d0 00d1c0 001040 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00d1bc 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00d200 001a28 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 00ec28 089628 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 098250 010ea5 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0a90f5 02a1a6 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 0d329c 004c18 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 0d7eb4 00ed70 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 0e6c24 02af19 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 111b40 007740 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 119280 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000d0d8 00d1b8 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 1192bc 00a900 10     31 1530  4
  [31] .strtab           STRTAB          00000000 123bbc 0084d1 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 12c08d 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00b734 0x0000b654 0x0000b654 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0cccc 0x0cccc RWE 0x10
  LOAD           0x00cdac 0x20000000 0x0000cccc 0x0040c 0x0040c RW  0x4
  LOAD           0x00d1b8 0x0000d0d8 0x0000d0d8 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000410 0x20000410 0x00000 0x02100 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
