ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x51b9
  Start of program headers:          52 (bytes into file)
  Start of section headers:          2081324 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         35
  Section header string table index: 34

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00ffe0 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       00010138 010218 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        00010140 010220 0000a8 00   A  0   0  4
  [ 5] device_area       PROGBITS        000101e8 0102c8 0000c0 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        000102a8 010388 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        000104d0 0105b0 0000a0 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        00010570 010650 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        00010580 010660 0020a4 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 012d78 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 012708 000551 00  WA  0   0  8
  [12] device_states     PROGBITS        20000551 012c59 000010 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000564 012c6c 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000059c 012ca4 000004 00  WA  0   0  4
  [15] k_mem_slab_area   PROGBITS        200005a0 012ca8 00001c 00  WA  0   0  4
  [16] k_heap_area       PROGBITS        200005bc 012cc4 000014 00  WA  0   0  4
  [17] k_mutex_area      PROGBITS        200005d0 012cd8 00008c 00  WA  0   0  4
  [18] k_sem_area        PROGBITS        2000065c 012d64 000010 00  WA  0   0  4
  [19] bss               NOBITS          20000670 012d78 001659 00  WA  0   0  8
  [20] noinit            NOBITS          20001cd0 012d78 00b940 00  WA  0   0  8
  [21] .comment          PROGBITS        00000000 012d78 000040 01  MS  0   0  1
  [22] .debug_aranges    PROGBITS        00000000 012db8 0022e8 00      0   0  8
  [23] .debug_info       PROGBITS        00000000 0150a0 10d09f 00      0   0  1
  [24] .debug_abbrev     PROGBITS        00000000 12213f 017d43 00      0   0  1
  [25] .debug_line       PROGBITS        00000000 139e82 03e27d 00      0   0  1
  [26] .debug_frame      PROGBITS        00000000 178100 0067ac 00      0   0  4
  [27] .debug_str        PROGBITS        00000000 17e8ac 013b73 01  MS  0   0  1
  [28] .debug_loc        PROGBITS        00000000 19241f 047a7e 00      0   0  1
  [29] .debug_ranges     PROGBITS        00000000 1d9ea0 00b780 00      0   0  8
  [30] .ARM.attributes   ARM_ATTRIBUTES  00000000 1e5620 00003a 00      0   0  1
  [31] .last_section     PROGBITS        00012c90 012d74 000004 00   A  0   0  1
  [32] .symtab           SYMTAB          00000000 1e565c 00d100 10     33 2051  4
  [33] .strtab           STRTAB          00000000 1f275c 009941 00      0   0  1
  [34] .shstrtab         STRTAB          00000000 1fc09d 00018c 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x010218 0x00010138 0x00010138 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x12624 0x12624 RWE 0x10
  LOAD           0x012708 0x20000000 0x00012624 0x0066c 0x0066c RW  0x8
  LOAD           0x012d74 0x00012c90 0x00012c90 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000670 0x20000670 0x00000 0x0cfa0 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_heap_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
