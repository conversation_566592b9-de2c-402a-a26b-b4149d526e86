ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x5555
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1866692 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         34
  Section header string table index: 33

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00f37c 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000f4d4 00f5b4 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000f4dc 00f5bc 0000a0 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000f57c 00f65c 0000a8 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000f624 00f704 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000f84c 00f92c 000080 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000f8cc 00f9ac 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000f8e0 00f9c0 001efc 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 011e48 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 0118c0 000481 00  WA  0   0  8
  [12] device_states     PROGBITS        20000481 011d41 00000e 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000490 011d50 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        200004c8 011d88 000004 00  WA  0   0  4
  [15] k_mem_slab_area   PROGBITS        200004cc 011d8c 00001c 00  WA  0   0  4
  [16] k_mutex_area      PROGBITS        200004e8 011da8 00008c 00  WA  0   0  4
  [17] k_sem_area        PROGBITS        20000574 011e34 000010 00  WA  0   0  4
  [18] bss               NOBITS          20000588 011e48 001621 00  WA  0   0  8
  [19] noinit            NOBITS          20001bb0 011e48 003940 00  WA  0   0  8
  [20] .comment          PROGBITS        00000000 011e48 000040 01  MS  0   0  1
  [21] .debug_aranges    PROGBITS        00000000 011e88 0020b8 00      0   0  8
  [22] .debug_info       PROGBITS        00000000 013f40 0ec1a8 00      0   0  1
  [23] .debug_abbrev     PROGBITS        00000000 1000e8 015a50 00      0   0  1
  [24] .debug_line       PROGBITS        00000000 115b38 039327 00      0   0  1
  [25] .debug_frame      PROGBITS        00000000 14ee60 006198 00      0   0  4
  [26] .debug_str        PROGBITS        00000000 154ff8 0129a8 01  MS  0   0  1
  [27] .debug_loc        PROGBITS        00000000 1679a0 03febf 00      0   0  1
  [28] .debug_ranges     PROGBITS        00000000 1a7860 00a4a8 00      0   0  8
  [29] .ARM.attributes   ARM_ATTRIBUTES  00000000 1b1d08 00003a 00      0   0  1
  [30] .last_section     PROGBITS        00011d60 011e44 000004 00   A  0   0  1
  [31] .symtab           SYMTAB          00000000 1b1d44 00c880 10     32 1933  4
  [32] .strtab           STRTAB          00000000 1be5c4 009480 00      0   0  1
  [33] .shstrtab         STRTAB          00000000 1c7a44 000180 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00f5b4 0x0000f4d4 0x0000f4d4 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x117dc 0x117dc RWE 0x10
  LOAD           0x0118c0 0x20000000 0x000117dc 0x00584 0x00584 RW  0x8
  LOAD           0x011e44 0x00011d60 0x00011d60 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000588 0x20000588 0x00000 0x04f68 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
