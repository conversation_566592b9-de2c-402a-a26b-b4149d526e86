ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x5db5
  Start of program headers:          52 (bytes into file)
  Start of section headers:          2248820 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         34
  Section header string table index: 33

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 010c60 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       00010db8 010e98 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        00010dc0 010ea0 0000a8 00   A  0   0  4
  [ 5] device_area       PROGBITS        00010e68 010f48 0000c0 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        00010f28 011008 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        00011150 011230 0000a8 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        000111f8 0112d8 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        00011210 0112f0 002a30 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 01437c 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 013d20 000551 00  WA  0   0  8
  [12] device_states     PROGBITS        20000551 014271 000010 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000564 014284 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000059c 0142bc 000004 00  WA  0   0  4
  [15] k_mem_slab_area   PROGBITS        200005a0 0142c0 00001c 00  WA  0   0  4
  [16] k_mutex_area      PROGBITS        200005bc 0142dc 00008c 00  WA  0   0  4
  [17] k_sem_area        PROGBITS        20000648 014368 000010 00  WA  0   0  4
  [18] bss               NOBITS          20000658 014380 004671 00  WA  0   0  8
  [19] noinit            NOBITS          20004cd0 014380 003940 00  WA  0   0  8
  [20] .comment          PROGBITS        00000000 01437c 000040 01  MS  0   0  1
  [21] .debug_aranges    PROGBITS        00000000 0143c0 0022f8 00      0   0  8
  [22] .debug_info       PROGBITS        00000000 0166b8 12f45c 00      0   0  1
  [23] .debug_abbrev     PROGBITS        00000000 145b14 017f98 00      0   0  1
  [24] .debug_line       PROGBITS        00000000 15daac 03f3ef 00      0   0  1
  [25] .debug_frame      PROGBITS        00000000 19ce9c 0067e0 00      0   0  4
  [26] .debug_str        PROGBITS        00000000 1a367c 013e0d 01  MS  0   0  1
  [27] .debug_loc        PROGBITS        00000000 1b7489 04ac03 00      0   0  1
  [28] .debug_ranges     PROGBITS        00000000 202090 00bee0 00      0   0  8
  [29] .ARM.attributes   ARM_ATTRIBUTES  00000000 20df70 00003a 00      0   0  1
  [30] .last_section     PROGBITS        00014298 014378 000004 00   A  0   0  1
  [31] .symtab           SYMTAB          00000000 20dfac 00d4e0 10     32 2106  4
  [32] .strtab           STRTAB          00000000 21b48c 009a66 00      0   0  1
  [33] .shstrtab         STRTAB          00000000 224ef2 000180 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x010e98 0x00010db8 0x00010db8 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x13c40 0x13c40 RWE 0x10
  LOAD           0x013d20 0x20000000 0x00013c40 0x00658 0x00658 RW  0x8
  LOAD           0x014378 0x00014298 0x00014298 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000658 0x20000658 0x00000 0x07fb8 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
