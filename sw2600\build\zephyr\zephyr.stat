ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x4a01
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1461172 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00c524 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000c67c 00c75c 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000c684 00c764 000088 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000c70c 00c7ec 000090 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000c79c 00c87c 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000c9c4 00caa4 000088 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000ca4c 00cb2c 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000ca60 00cb40 001648 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00e5a8 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00e188 000338 00  WA  0   0  4
  [12] device_states     PROGBITS        20000338 00e4c0 00000c 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000344 00e4cc 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000037c 00e504 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        20000380 00e508 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        2000040c 00e594 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000420 00e5a8 00111d 00  WA  0   0  8
  [18] noinit            NOBITS          20001540 00e5a8 001040 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00e5a8 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00e5e8 001c20 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 010208 0b024e 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 0c0456 012e7f 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0d32d5 02ed95 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 10206c 005108 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 107174 0104b6 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 11762a 03103c 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 148668 0085b8 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 150c20 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000e4c4 00e5a4 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 150c5c 00b390 10     31 1661  4
  [31] .strtab           STRTAB          00000000 15bfec 008a56 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 164a42 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00c75c 0x0000c67c 0x0000c67c 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0e0a8 0x0e0a8 RWE 0x10
  LOAD           0x00e188 0x20000000 0x0000e0a8 0x0041c 0x0041c RW  0x4
  LOAD           0x00e5a4 0x0000e4c4 0x0000e4c4 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000420 0x20000420 0x00000 0x02160 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
