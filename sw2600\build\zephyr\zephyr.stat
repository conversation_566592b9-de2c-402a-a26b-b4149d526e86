ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x5bd1
  Start of program headers:          52 (bytes into file)
  Start of section headers:          2210316 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         34
  Section header string table index: 33

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 010a7c 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       00010bd4 010cb4 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        00010bdc 010cbc 0000a8 00   A  0   0  4
  [ 5] device_area       PROGBITS        00010c84 010d64 0000c0 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        00010d44 010e24 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        00010f6c 01104c 0000a8 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        00011014 0110f4 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        00011030 011110 002824 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 013f94 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 013938 000551 00  WA  0   0  8
  [12] device_states     PROGBITS        20000551 013e89 000010 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000564 013e9c 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000059c 013ed4 000004 00  WA  0   0  4
  [15] k_mem_slab_area   PROGBITS        200005a0 013ed8 00001c 00  WA  0   0  4
  [16] k_mutex_area      PROGBITS        200005bc 013ef4 00008c 00  WA  0   0  4
  [17] k_sem_area        PROGBITS        20000648 013f80 000010 00  WA  0   0  4
  [18] bss               NOBITS          20000658 013f98 004671 00  WA  0   0  8
  [19] noinit            NOBITS          20004cd0 013f98 003940 00  WA  0   0  8
  [20] .comment          PROGBITS        00000000 013f94 000040 01  MS  0   0  1
  [21] .debug_aranges    PROGBITS        00000000 013fd8 0022f0 00      0   0  8
  [22] .debug_info       PROGBITS        00000000 0162c8 1275b0 00      0   0  1
  [23] .debug_abbrev     PROGBITS        00000000 13d878 017f66 00      0   0  1
  [24] .debug_line       PROGBITS        00000000 1557de 03efec 00      0   0  1
  [25] .debug_frame      PROGBITS        00000000 1947cc 0067b4 00      0   0  4
  [26] .debug_str        PROGBITS        00000000 19af80 013d4f 01  MS  0   0  1
  [27] .debug_loc        PROGBITS        00000000 1aeccf 049ef6 00      0   0  1
  [28] .debug_ranges     PROGBITS        00000000 1f8bc8 00bda0 00      0   0  8
  [29] .ARM.attributes   ARM_ATTRIBUTES  00000000 204968 00003a 00      0   0  1
  [30] .last_section     PROGBITS        00013eac 013f90 000004 00   A  0   0  1
  [31] .symtab           SYMTAB          00000000 2049a4 00d490 10     32 2101  4
  [32] .strtab           STRTAB          00000000 211e34 009a55 00      0   0  1
  [33] .shstrtab         STRTAB          00000000 21b889 000180 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x010cb4 0x00010bd4 0x00010bd4 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x13854 0x13854 RWE 0x10
  LOAD           0x013938 0x20000000 0x00013854 0x00658 0x00658 RW  0x8
  LOAD           0x013f90 0x00013eac 0x00013eac 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000658 0x20000658 0x00000 0x07fb8 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
