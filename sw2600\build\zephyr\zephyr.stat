ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x33bd
  Start of program headers:          52 (bytes into file)
  Start of section headers:          945876 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         28
  Section header string table index: 27

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00a2bc 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000a414 00a4f4 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000a41c 00a4fc 000068 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000a484 00a564 000060 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000a4e4 00a5c4 000228 00  WA  0   0  4
  [ 7] rodata            PROGBITS        0000a710 00a7f0 000790 00   A  0   0 16
  [ 8] .ramfunc          PROGBITS        20000000 00b384 000000 00   W  0   0  1
  [ 9] datas             PROGBITS        20000000 00af80 00036c 00  WA  0   0  8
  [10] device_states     PROGBITS        2000036c 00b2ec 000008 00  WA  0   0  1
  [11] k_mutex_area      PROGBITS        20000374 00b2f4 00008c 00  WA  0   0  4
  [12] bss               NOBITS          20000400 00b388 000c3b 00  WA  0   0  8
  [13] noinit            NOBITS          20001040 00b388 001140 00  WA  0   0  8
  [14] .comment          PROGBITS        00000000 00b384 000040 01  MS  0   0  1
  [15] .debug_aranges    PROGBITS        00000000 00b3c8 001818 00      0   0  8
  [16] .debug_info       PROGBITS        00000000 00cbe0 0571e7 00      0   0  1
  [17] .debug_abbrev     PROGBITS        00000000 063dc7 00e908 00      0   0  1
  [18] .debug_line       PROGBITS        00000000 0726cf 026c08 00      0   0  1
  [19] .debug_frame      PROGBITS        00000000 0992d8 004588 00      0   0  4
  [20] .debug_str        PROGBITS        00000000 09d860 00dbc8 01  MS  0   0  1
  [21] .debug_loc        PROGBITS        00000000 0ab428 025232 00      0   0  1
  [22] .debug_ranges     PROGBITS        00000000 0d0660 0062c0 00      0   0  8
  [23] .ARM.attributes   ARM_ATTRIBUTES  00000000 0d6920 00003a 00      0   0  1
  [24] .last_section     PROGBITS        0000b2a0 00b380 000004 00   A  0   0  1
  [25] .symtab           SYMTAB          00000000 0d695c 009170 10     26 1293  4
  [26] .strtab           STRTAB          00000000 0dfacc 0072e6 00      0   0  1
  [27] .shstrtab         STRTAB          00000000 0e6db2 000121 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00a4f4 0x0000a414 0x0000a414 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0aea0 0x0aea0 RWE 0x10
  LOAD           0x00af80 0x20000000 0x0000aea0 0x00400 0x00400 RW  0x8
  LOAD           0x00b380 0x0000b2a0 0x0000b2a0 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000400 0x20000400 0x00000 0x01d80 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table rodata 
   02     datas device_states k_mutex_area 
   03     .last_section 
   04     bss noinit 
