ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x5de5
  Start of program headers:          52 (bytes into file)
  Start of section headers:          2250468 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         34
  Section header string table index: 33

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 010c90 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       00010de8 010ec8 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        00010df0 010ed0 0000a8 00   A  0   0  4
  [ 5] device_area       PROGBITS        00010e98 010f78 0000c0 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        00010f58 011038 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        00011180 011260 0000a8 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        00011228 011308 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        00011240 011320 002a58 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 0143d4 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 013d78 000551 00  WA  0   0  8
  [12] device_states     PROGBITS        20000551 0142c9 000010 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000564 0142dc 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000059c 014314 000004 00  WA  0   0  4
  [15] k_mem_slab_area   PROGBITS        200005a0 014318 00001c 00  WA  0   0  4
  [16] k_mutex_area      PROGBITS        200005bc 014334 00008c 00  WA  0   0  4
  [17] k_sem_area        PROGBITS        20000648 0143c0 000010 00  WA  0   0  4
  [18] bss               NOBITS          20000658 0143d8 004671 00  WA  0   0  8
  [19] noinit            NOBITS          20004cd0 0143d8 003940 00  WA  0   0  8
  [20] .comment          PROGBITS        00000000 0143d4 000040 01  MS  0   0  1
  [21] .debug_aranges    PROGBITS        00000000 014418 0022f8 00      0   0  8
  [22] .debug_info       PROGBITS        00000000 016710 12f5a8 00      0   0  1
  [23] .debug_abbrev     PROGBITS        00000000 145cb8 017f8f 00      0   0  1
  [24] .debug_line       PROGBITS        00000000 15dc47 03f4ca 00      0   0  1
  [25] .debug_frame      PROGBITS        00000000 19d114 0067e0 00      0   0  4
  [26] .debug_str        PROGBITS        00000000 1a38f4 013e10 01  MS  0   0  1
  [27] .debug_loc        PROGBITS        00000000 1b7704 04af20 00      0   0  1
  [28] .debug_ranges     PROGBITS        00000000 202628 00bf98 00      0   0  8
  [29] .ARM.attributes   ARM_ATTRIBUTES  00000000 20e5c0 00003a 00      0   0  1
  [30] .last_section     PROGBITS        000142f0 0143d0 000004 00   A  0   0  1
  [31] .symtab           SYMTAB          00000000 20e5fc 00d500 10     32 2108  4
  [32] .strtab           STRTAB          00000000 21bafc 009a66 00      0   0  1
  [33] .shstrtab         STRTAB          00000000 225562 000180 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x010ec8 0x00010de8 0x00010de8 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x13c98 0x13c98 RWE 0x10
  LOAD           0x013d78 0x20000000 0x00013c98 0x00658 0x00658 RW  0x8
  LOAD           0x0143d0 0x000142f0 0x000142f0 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000658 0x20000658 0x00000 0x07fb8 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
