ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x49a5
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1326172 (bytes into file)
  Flags:                             0x5000400, Version5 EABI, hard-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         5
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 0000e0 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000238 00b840 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0000b998 00ba78 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0000b9a0 00ba80 000080 00   A  0   0  4
  [ 5] device_area       PROGBITS        0000ba20 00bb00 000078 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0000ba98 00bb78 000228 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0000bcc0 00bda0 000078 00   A  0   0  4
  [ 8] log_backend_area  PROGBITS        0000bd38 00be18 000010 00   A  0   0  4
  [ 9] rodata            PROGBITS        0000bd50 00be30 001458 00   A  0   0 16
  [10] .ramfunc          PROGBITS        20000000 00d698 000000 00   W  0   0  1
  [11] datas             PROGBITS        20000000 00d288 000328 00  WA  0   0  4
  [12] device_states     PROGBITS        20000328 00d5b0 00000a 00  WA  0   0  1
  [13] log_mpsc_pbu[...] PROGBITS        20000334 00d5bc 000038 00  WA  0   0  4
  [14] log_msg_ptr_area  PROGBITS        2000036c 00d5f4 000004 00  WA  0   0  4
  [15] k_mutex_area      PROGBITS        20000370 00d5f8 00008c 00  WA  0   0  4
  [16] k_sem_area        PROGBITS        200003fc 00d684 000010 00  WA  0   0  4
  [17] bss               NOBITS          20000410 00d698 0010bd 00  WA  0   0  8
  [18] noinit            NOBITS          200014d0 00d698 001040 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 00d698 000040 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 00d6d8 001b00 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 00f1d8 09ab8b 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 0a9d63 011991 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 0bb6f4 02b82d 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 0e6f24 004e98 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 0ebdbc 00f3a1 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 0fb15d 02dadd 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 128c40 007cc0 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 130900 00003a 00      0   0  1
  [29] .last_section     PROGBITS        0000d5b4 00d694 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 13093c 00abc0 10     31 1571  4
  [31] .strtab           STRTAB          00000000 13b4fc 0085ef 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 143aeb 000170 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x00ba78 0x0000b998 0x0000b998 0x00008 0x00008 R   0x4
  LOAD           0x0000e0 0x00000000 0x00000000 0x0d1a8 0x0d1a8 RWE 0x10
  LOAD           0x00d288 0x20000000 0x0000d1a8 0x0040c 0x0040c RW  0x4
  LOAD           0x00d694 0x0000d5b4 0x0000d5b4 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20000410 0x20000410 0x00000 0x02100 RW  0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
