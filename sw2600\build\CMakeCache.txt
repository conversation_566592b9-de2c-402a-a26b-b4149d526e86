# This is the CMakeCache file.
# For build in directory: c:/ncs/zy/sw2600/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Extra config fragments for 802154_rpmsg child image
802154_rpmsg_EXTRA_CONF_FILE:STRING=;C:/ncs/v2.9.0-zigbee/nrf/subsys/partition_manager/partition_manager_enabled.conf

//Application Binary Directory
APPLICATION_BINARY_DIR:PATH=C:/ncs/zy/sw2600/build

//The application configuration folder
APPLICATION_CONFIG_DIR:PATH=C:/ncs/zy/sw2600

//Application Source Directory
APPLICATION_SOURCE_DIR:PATH=C:/ncs/zy/sw2600

//Selected board
BOARD:STRING=nrf5340dk/nrf5340/cpuapp

//Main board directory for board (nrf5340dk)
BOARD_DIR:PATH=C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk

//Support board extensions
BOARD_EXTENSIONS:BOOL=ON

//Path to a program.
BOSSAC:FILEPATH=BOSSAC-NOTFOUND

//Kernel binary file
BYPRODUCT_KERNEL_BIN_NAME:FILEPATH=C:/ncs/zy/sw2600/build/zephyr/zephyr.bin

//Kernel elf file
BYPRODUCT_KERNEL_ELF_NAME:FILEPATH=C:/ncs/zy/sw2600/build/zephyr/zephyr.elf

//Kernel hex file
BYPRODUCT_KERNEL_HEX_NAME:FILEPATH=C:/ncs/zy/sw2600/build/zephyr/zephyr.hex

//Selected board
CACHED_BOARD:STRING=nrf5340dk/nrf5340/cpuapp

//Selected shield
CACHED_SHIELD:STRING=

//Selected snippet
CACHED_SNIPPET:STRING=

//Path to a program.
CCACHE_FOUND:FILEPATH=CCACHE_FOUND-NOTFOUND

//Path to a program.
CCACHE_PROGRAM:FILEPATH=CCACHE_PROGRAM-NOTFOUND

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-addr2line.exe

//Path to a program.
CMAKE_AR:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-ar.exe

//Path to a program.
CMAKE_AS:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-as.exe

//ASM compiler
CMAKE_ASM_COMPILER:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_ASM_COMPILER_AR:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_ASM_COMPILER_RANLIB:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib.exe

//Flags used by the ASM compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the ASM compiler during DEBUG builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=-g

//Flags used by the ASM compiler during MINSIZEREL builds.
CMAKE_ASM_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the ASM compiler during RELEASE builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the ASM compiler during RELWITHDEBINFO builds.
CMAKE_ASM_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Choose the type of build.
CMAKE_BUILD_TYPE:STRING=MinSizeRel

//CXX compiler
CMAKE_CXX_COMPILER:STRING=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_C_COMPILER:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Export CMake compile commands. Used by gen_app_partitions.py
// script
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/ncs/zy/sw2600/build/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_GCOV:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcov.exe

//Path to a program.
CMAKE_GDB:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb.exe

//Path to a program.
CMAKE_GDB_NO_PY:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb.exe

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/Zephyr-Kernel

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=sw2600

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=3.7.99

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=3

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=7

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=99

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-ranlib.exe

//Path to a program.
CMAKE_READELF:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-readelf.exe

CMAKE_REQUIRED_FLAGS:STRING=-c

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-strip.exe

CMAKE_SYSTEM_NAME:STRING=Generic

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//If desired, you can build the application usingthe configuration
// settings specified in an alternate .conf file using this parameter.
// These settings will override the settings in the application’s
// .config file or its default .conf file.Multiple files may be
// listed, e.g. CONF_FILE="prj1.conf;prj2.conf" The CACHED_CONF_FILE
// is internal Zephyr variable used between CMake runs. To change
// CONF_FILE, use the CONF_FILE variable.
CONF_FILE:STRING=C:/ncs/zy/sw2600/prj.conf

//NCS Toolchain DTC
DTC:FILEPATH=C:/ncs/toolchains/b620d30767/opt/bin/dtc.exe

//If desired, you can build the application using the DT configuration
// settings specified in an alternate .overlay file using this
// parameter. These settings will override the settings in the
// board's .dts file. Multiple files may be listed, e.g. DTC_OVERLAY_FILE="dts1.overlay
// dts2.overlay"
DTC_OVERLAY_FILE:STRING=C:/ncs/zy/sw2600/boards/nrf5340dk_nrf5340_cpuapp.overlay

//NCS Toolchain Git
GIT_EXECUTABLE:FILEPATH=C:/ncs/toolchains/b620d30767/bin/git.exe

//NCS GNU ARM emb path
GNUARMEMB_TOOLCHAIN_PATH:PATH=C:/ncs/toolchains/b620d30767/opt

//Linker BFD compatibility (compiler reported)
GNULD_LINKER_IS_BFD:BOOL=ON

//GNU ld version
GNULD_VERSION_STRING:STRING=2.38

//NCS Toolchain gperf
GPERF:FILEPATH=C:/ncs/toolchains/b620d30767/opt/bin/gperf.exe

//Path to a program.
IMGTOOL:FILEPATH=C:/ncs/toolchains/b620d30767/opt/bin/Scripts/imgtool.exe

MACHINE:STRING=arm

//NRFXLIB root directory
NRFXLIB_DIR:PATH=C:/ncs/v2.9.0-zigbee/nrfxlib

//nrfx Directory
NRFX_DIR:PATH=C:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx

//NCS root directory
NRF_DIR:PATH=C:/ncs/v2.9.0-zigbee/nrf

//The directory containing a CMake configuration file for NcsToolchain.
NcsToolchain_DIR:PATH=C:/ncs/toolchains/b620d30767/cmake

//Path to a program.
OPENOCD:FILEPATH=OPENOCD-NOTFOUND

//Path to a program.
PAHOLE:FILEPATH=PAHOLE-NOTFOUND

//NCS Toolchain protoc
PROTOBUF_PROTOC_EXECUTABLE:STRING=C:/Program Files/CMake/bin/cmake.exe;-E;env;PATH=C:\ncs\toolchains\b620d30767\opt\bin\;C:\ncs\v2.9.0-zigbee\zephyr\scripts\;C:\Users\<USER>\bin\;C:\Program Files\Git\mingw64\bin\;C:\Program Files\Git\usr\local\bin\;C:\Program Files\Git\usr\bin\;C:\Program Files\Git\usr\bin\;C:\Program Files\Git\mingw64\bin\;C:\Program Files\Git\usr\bin\;C:\Users\<USER>\bin\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Python313\Scripts\;C:\Python313\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin\;C:\Windows\system32\;C:\Windows\;C:\Windows\System32\Wbem\;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files\dotnet\;C:\ProgramData\anaconda3\;C:\ProgramData\anaconda3\Scripts\;C:\ProgramData\anaconda3\Library\bin\;C:\ProgramData\anaconda3\Library\mingw-w64\bin\;C:\Program Files\WinMerge\;C:\ProgramData\chocolatey\bin\;C:\Program Files\Docker\Docker\resources\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\;C:\Program Files\JetBrains\PyCharm 2024.3.2\bin\;C:\Users\<USER>\AppData\Local\Programs\Ollama\;C:\Users\<USER>\.dotnet\tools\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin\;C:\Program Files\nodejs\;C:\Program Files\nodejs\node_modules\npm\bin\;C:\Program Files\Nordic Semiconductor\nrf-command-line-tools\bin\;C:\Users\<USER>\.nrfutil\;C:\Program Files\PuTTY\;C:\ncs\toolchains\b620d30767\opt\bin\Scripts\;C:\Program Files\CMake\bin\;C:\Program Files\Git\cmd\;D:\zzh\OKT507-C_Forlinx desktop18.04_用户资料_R4(更新日期_20241009)\3-工具\PhoenixSuit_v1.13\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin\;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin\;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand\;C:\Program Files\Git\usr\bin\vendor_perl\;C:\Program Files\Git\usr\bin\core_perl;C:/ncs/toolchains/b620d30767/opt/nanopb/generator-bin/protoc.exe

//NCS Toolchain protoc
PROTOC:STRING=C:/Program Files/CMake/bin/cmake.exe;-E;env;PATH=C:\ncs\toolchains\b620d30767\opt\bin\;C:\ncs\v2.9.0-zigbee\zephyr\scripts\;C:\Users\<USER>\bin\;C:\Program Files\Git\mingw64\bin\;C:\Program Files\Git\usr\local\bin\;C:\Program Files\Git\usr\bin\;C:\Program Files\Git\usr\bin\;C:\Program Files\Git\mingw64\bin\;C:\Program Files\Git\usr\bin\;C:\Users\<USER>\bin\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Python313\Scripts\;C:\Python313\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin\;C:\Windows\system32\;C:\Windows\;C:\Windows\System32\Wbem\;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files\dotnet\;C:\ProgramData\anaconda3\;C:\ProgramData\anaconda3\Scripts\;C:\ProgramData\anaconda3\Library\bin\;C:\ProgramData\anaconda3\Library\mingw-w64\bin\;C:\Program Files\WinMerge\;C:\ProgramData\chocolatey\bin\;C:\Program Files\Docker\Docker\resources\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\;C:\Program Files\JetBrains\PyCharm 2024.3.2\bin\;C:\Users\<USER>\AppData\Local\Programs\Ollama\;C:\Users\<USER>\.dotnet\tools\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin\;C:\Program Files\nodejs\;C:\Program Files\nodejs\node_modules\npm\bin\;C:\Program Files\Nordic Semiconductor\nrf-command-line-tools\bin\;C:\Users\<USER>\.nrfutil\;C:\Program Files\PuTTY\;C:\ncs\toolchains\b620d30767\opt\bin\Scripts\;C:\Program Files\CMake\bin\;C:\Program Files\Git\cmd\;D:\zzh\OKT507-C_Forlinx desktop18.04_用户资料_R4(更新日期_20241009)\3-工具\PhoenixSuit_v1.13\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin\;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin\;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand\;C:\Program Files\Git\usr\bin\vendor_perl\;C:\Program Files\Git\usr\bin\core_perl;C:/ncs/toolchains/b620d30767/opt/nanopb/generator-bin/protoc.exe

//Path to a program.
PUNCOVER:FILEPATH=PUNCOVER-NOTFOUND

//NCS Toolchain Python
PYTHON_EXECUTABLE:FILEPATH=C:/ncs/toolchains/b620d30767/opt/bin/python.exe

//Value Computed by CMake
Picolibc_BINARY_DIR:STATIC=C:/ncs/zy/sw2600/build/modules/picolibc

//Value Computed by CMake
Picolibc_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Picolibc_SOURCE_DIR:STATIC=C:/ncs/v2.9.0-zigbee/modules/lib/picolibc

//NCS Toolchain Python
Python3_EXECUTABLE:FILEPATH=C:/ncs/toolchains/b620d30767/opt/bin/python.exe

//Path to the SoC directory.
SOC_FULL_DIR:PATH=C:/ncs/v2.9.0-zigbee/zephyr/soc/nordic

//Path to a CMSIS-SVD file
SOC_SVD_FILE:FILEPATH=C:/ncs/v2.9.0-zigbee/modules/hal/nordic/nrfx/mdk/nrf5340_application.svd

//True if toolchain supports newlib
TOOLCHAIN_HAS_NEWLIB:BOOL=ON

//True if toolchain supports picolibc
TOOLCHAIN_HAS_PICOLIBC:BOOL=ON

//Zephyr toolchain root
TOOLCHAIN_ROOT:STRING=C:/ncs/v2.9.0-zigbee/zephyr

//Build with sample applications
WITH_APPS:BOOL=OFF

//Build with all cache operations enabled
WITH_DCACHE:BOOL=OFF

//Build with buffers cache operations enabled
WITH_DCACHE_BUFFERS:BOOL=OFF

//Build with resource table cache operations enabled
WITH_DCACHE_RSC_TABLE:BOOL=OFF

//Build with vrings cache operations enabled
WITH_DCACHE_VRINGS:BOOL=OFF

//Build with default logger
WITH_DEFAULT_LOGGER:BOOL=OFF

//Build with documentation
WITH_DOC:BOOL=OFF

//Log with function name, line number prefix
WITH_FUNC_LINE_LOG:BOOL=OFF

//Check Libmetal library can be found
WITH_LIBMETAL_FIND:BOOL=OFF

//Build with proxy(access device controlled by other processor)
WITH_PROXY:BOOL=OFF

//Build with proxy sample applications
WITH_PROXY_APPS:BOOL=OFF

//Build with a static library
WITH_STATIC_LIB:BOOL=ON

//Build with virtio mmio driver support enabled
WITH_VIRTIO_MMIO_DRV:BOOL=OFF

//Zephyr base
ZEPHYR_BASE:PATH=C:/ncs/v2.9.0-zigbee/zephyr

//Path to Zephyr git repository index file
ZEPHYR_GIT_INDEX:PATH=C:/ncs/v2.9.0-zigbee/zephyr/.git/index

//Path to merged image in Intel Hex format
ZEPHYR_RUNNER_CONFIG_KERNEL_HEX:STRING=C:/ncs/zy/sw2600/build/zephyr/merged_domains.hex

//NCS Zephyr SDK install dir
ZEPHYR_SDK_INSTALL_DIR:PATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk

//NCS Toolchain Variant
ZEPHYR_TOOLCHAIN_VARIANT:STRING=zephyr

//Value Computed by CMake
Zephyr-Kernel_BINARY_DIR:STATIC=C:/ncs/zy/sw2600/build

//Value Computed by CMake
Zephyr-Kernel_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Zephyr-Kernel_SOURCE_DIR:STATIC=C:/ncs/zy/sw2600

//The directory containing a CMake configuration file for Zephyr-sdk.
Zephyr-sdk_DIR:PATH=C:/ncs/toolchains/b620d30767/opt/zephyr-sdk/cmake

//The directory containing a CMake configuration file for ZephyrAppConfiguration.
ZephyrAppConfiguration_DIR:PATH=ZephyrAppConfiguration_DIR-NOTFOUND

//The directory containing a CMake configuration file for ZephyrBuildConfiguration.
ZephyrBuildConfiguration_DIR:PATH=C:/ncs/v2.9.0-zigbee/nrf/share/zephyrbuild-package/cmake

//The directory containing a CMake configuration file for Zephyr.
Zephyr_DIR:PATH=C:/ncs/v2.9.0-zigbee/zephyr/share/zephyr-package/cmake

//Value Computed by CMake
metal_BINARY_DIR:STATIC=C:/ncs/zy/sw2600/build/modules/libmetal/libmetal

//Value Computed by CMake
metal_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
metal_SOURCE_DIR:STATIC=C:/ncs/v2.9.0-zigbee/modules/hal/libmetal/libmetal

//Value Computed by CMake
open_amp_BINARY_DIR:STATIC=C:/ncs/zy/sw2600/build/modules/open-amp/open-amp

//Value Computed by CMake
open_amp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
open_amp_SOURCE_DIR:STATIC=C:/ncs/v2.9.0-zigbee/modules/lib/open-amp/open-amp

//Value Computed by CMake
sw2600_BINARY_DIR:STATIC=C:/ncs/zy/sw2600/build

//Value Computed by CMake
sw2600_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
sw2600_SOURCE_DIR:STATIC=C:/ncs/zy/sw2600


########################
# INTERNAL cache entries
########################

//List of board directories for board (nrf5340dk)
BOARD_DIRECTORIES:INTERNAL=C:/ncs/v2.9.0-zigbee/zephyr/boards/nordic/nrf5340dk
//DT bindings root directories
CACHED_DTS_ROOT_BINDINGS:INTERNAL=C:/ncs/v2.9.0-zigbee/nrf/dts/bindings;C:/ncs/v2.9.0-zigbee/zephyr/dts/bindings
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER
CMAKE_ASM_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER_AR
CMAKE_ASM_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER_RANLIB
CMAKE_ASM_COMPILER_RANLIB-ADVANCED:INTERNAL=1
CMAKE_ASM_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS
CMAKE_ASM_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_DEBUG
CMAKE_ASM_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_MINSIZEREL
CMAKE_ASM_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELEASE
CMAKE_ASM_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELWITHDEBINFO
CMAKE_ASM_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/ncs/zy/sw2600/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/ncs/zy/sw2600
//NCS Toolchain ninja
CMAKE_MAKE_PROGRAM:INTERNAL=C:/ncs/toolchains/b620d30767/opt/bin/ninja.exe
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=154
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//The build type
CONF_FILE_BUILD_TYPE:INTERNAL=
CPUNET_PM_DOMAIN_DYNAMIC_PARTITION:INTERNAL=802154_rpmsg
//Details about finding Dtc
FIND_PACKAGE_MESSAGE_DETAILS_Dtc:INTERNAL=[C:/ncs/toolchains/b620d30767/opt/bin/dtc.exe][v1.4.7(1.4.6)]
//Details about finding GnuLd
FIND_PACKAGE_MESSAGE_DETAILS_GnuLd:INTERNAL=[c:/ncs/toolchains/b620d30767/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/bin/ld.bfd.exe][v2.38()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[C:/ncs/toolchains/b620d30767/opt/bin/python.exe][cfound components: Interpreter ][v3.12.4(3.8)]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Have include fcntl.h
HAVE_FCNTL_H:INTERNAL=1
//Have include stdatomic.h
HAVE_STDATOMIC_H:INTERNAL=1
//Zephyr hardware model version
HWM:INTERNAL=v2
//Zephyr hardware model
HWMv2:INTERNAL=True
KERNEL_META_PATH:INTERNAL=C:/ncs/zy/sw2600/build/zephyr/zephyr.meta
//List of SoC directories for SoC (nrf5340)
SOC_DIRECTORIES:INTERNAL=C:/ncs/v2.9.0-zigbee/zephyr/soc/nordic
//SoC Linker script
SOC_LINKER_SCRIPT:INTERNAL=C:/ncs/v2.9.0-zigbee/zephyr/include/zephyr/arch/arm/cortex_m/scripts/linker.ld
//West
WEST:INTERNAL=C:/ncs/toolchains/b620d30767/opt/bin/python.exe;-m;west
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=C:/ncs/toolchains/b620d30767/opt/bin/python.exe
//Path to a program.
_Python3_EXECUTABLE_DEBUG:INTERNAL=_Python3_EXECUTABLE_DEBUG-NOTFOUND
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;<none>;cp312-win_amd64.pyd;;C:\ncs\toolchains\b620d30767\opt\bin\Lib;C:\ncs\toolchains\b620d30767\opt\bin\Lib;C:\ncs\toolchains\b620d30767\opt\bin\Lib\site-packages;C:\ncs\toolchains\b620d30767\opt\bin\Lib\site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=0a1002e3f1b20f4aea9787ab1144025a
//Interpreter reason failure
_Python3_Interpreter_REASON_FAILURE:INTERNAL=
//NumPy reason failure
_Python3_NumPy_REASON_FAILURE:INTERNAL=

