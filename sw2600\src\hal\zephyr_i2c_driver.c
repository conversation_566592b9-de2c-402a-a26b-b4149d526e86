#include <zephyr/kernel.h>
#include <zephyr/drivers/i2c.h>
#include <zephyr/logging/log.h>
#include "zephyr_i2c_driver.h"

LOG_MODULE_REGISTER(i2c_driver, CONFIG_LOG_DEFAULT_LEVEL);

// I2C设备
static const struct device *i2c_dev;

// I2C初始化
int i2c_driver_init(void)
{
    i2c_dev = DEVICE_DT_GET_OR_NULL(DT_NODELABEL(i2c1));

    if (!i2c_dev) {
        LOG_ERR("I2C device not found in device tree");
        return -1;
    }

    if (!device_is_ready(i2c_dev)) {
        LOG_ERR("I2C device not ready");
        return -1;
    }

    LOG_DBG("I2C device initialized successfully");
    return 0;
}

// I2C写操作
int i2c_write_bytes(uint8_t dev_addr, uint8_t *data, uint8_t len)
{
    if (!i2c_dev) {
        return -1;
    }

    return i2c_write(i2c_dev, data, len, dev_addr);
}

// I2C读操作
int i2c_read_bytes(uint8_t dev_addr, uint8_t *data, uint8_t len)
{
    if (!i2c_dev) {
        return -1;
    }

    return i2c_read(i2c_dev, data, len, dev_addr);
}

// I2C写后读操作（适用于发送命令后读取数据）
int i2c_write_read_bytes(uint8_t dev_addr, uint8_t *write_data, uint8_t write_len,
                       uint8_t *read_data, uint8_t read_len)
{
    if (!i2c_dev) {
        return -1;
    }

    return i2c_write_read(i2c_dev, dev_addr, write_data, write_len, read_data, read_len);
}

// 计算校验和 CRC8
uint8_t calculate_crc8(uint8_t *data, uint8_t len)
{
    uint8_t crc = 0xFF;
    uint8_t polynomial = 0x31; // 0x131 (100110001) 右移一位

    for (uint8_t i = 0; i < len; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ polynomial;
            } else {
                crc = (crc << 1);
            }
        }
    }

    return crc;
}